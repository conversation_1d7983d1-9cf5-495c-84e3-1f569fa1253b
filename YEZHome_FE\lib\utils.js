import { clsx } from "clsx";
import { twMerge } from "tailwind-merge"

export function cn(...inputs) {
  return twMerge(clsx(inputs));
}

export function parseEmptyStringsToNull(payload) {
  if (Array.isArray(payload)) {
    return payload.map(item => parseEmptyStringsToNull(item));
  }

  if (typeof payload === 'object' && payload !== null) {
    const newPayload = { ...payload };

    Object.keys(newPayload).forEach(key => {
      if (newPayload[key] === '') {
        newPayload[key] = null;
      } else if (typeof newPayload[key] === 'object' && newPayload[key] !== null) {
        newPayload[key] = parseEmptyStringsToNull(newPayload[key]);
      }
    });

    return newPayload;
  }

  return payload;
}

export function formatCurrency(amount) {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
    maximumFractionDigits: 0
  }).format(amount);
}

export function formatDate(dateString) {
  return new Date(dateString).toLocaleDateString('vi-VN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

export const formatPriceShort = (price) => {
  if (price === null || price === undefined) return 'N/A';
  if (price >= 1000000000) {
      // Làm tròn 1 chữ số thập phân, loại bỏ .0 nếu là số nguyên tỷ
      const val = (price / 1000000000).toFixed(1);
      return val.endsWith('.0') ? val.slice(0, -2) + ' Tỷ' : val + ' Tỷ';
  }
  if (price >= 1000000) {
      // Làm tròn 1 chữ số thập phân, loại bỏ .0 nếu là số nguyên triệu
       const val = (price / 1000000).toFixed(1);
      return val.endsWith('.0') ? val.slice(0, -2) + ' Tr' : val + ' Tr';
  }
   // Định dạng số thông thường cho các giá trị nhỏ hơn 1 triệu
   if (typeof price === 'number') {
       return price.toLocaleString('vi-VN');
   }
  return String(price); // Trường hợp khác cố gắng convert sang string
};

export function debounce(func, delay) {
  let timeoutId;
  // Hàm debounce trả về một hàm mới
  const debounced = function(...args) {
    const context = this; // Lưu ngữ cảnh 'this'
    clearTimeout(timeoutId); // Xóa timer cũ nếu có
    // Thiết lập timer mới để gọi hàm gốc sau độ trễ
    timeoutId = setTimeout(() => {
      func.apply(context, args); // Gọi hàm gốc với ngữ cảnh và đối số đúng
    }, delay);
  };

  // Thêm phương thức cancel vào hàm debounced trả về
  debounced.cancel = function() {
    clearTimeout(timeoutId);
  };

  return debounced; // Trả về hàm đã được debounce
}