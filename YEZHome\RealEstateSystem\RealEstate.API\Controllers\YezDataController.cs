using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RealEstate.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class YezDataController : ControllerBase
    {
        private readonly IYezDataService _yezDataService;
        private readonly ILogger<YezDataController> _logger;

        public YezDataController(IYezDataService yezDataService, ILogger<YezDataController> logger)
        {
            _yezDataService = yezDataService;
            _logger = logger;
        }

        /// <summary>
        /// Get all highlight fees
        /// </summary>
        /// <returns>List of highlight fees for each membership rank</returns>
        /// <response code="200">Returns the list of highlight fees</response>
        [HttpGet("highlight-fees")]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<HighlightFeeDto>>> GetHighlightFees()
        {
            try
            {
                var highlightFees = await _yezDataService.GetAllHighlightFeesAsync();

                return Ok(highlightFees);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving highlight fees");
                return StatusCode(500, new { Message = "An error occurred while retrieving highlight fees" });
            }
        }

        /// <summary>
        /// Get highlight fee by rank name
        /// </summary>
        /// <param name="rankName">The name of the membership rank</param>
        /// <returns>The highlight fee for the specified rank</returns>
        /// <response code="200">Returns the highlight fee</response>
        /// <response code="404">If the rank is not found</response>
        [HttpGet("highlight-fees/{rankName}")]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<HighlightFeeDto>> GetHighlightFeeByRank(string rankName)
        {
            try
            {
                var highlightFeeDto = await _yezDataService.GetHighlightFeeByRankAsync(rankName);

                if (highlightFeeDto == null)
                {
                    return NotFound($"Highlight fee for rank '{rankName}' not found");
                }

                return Ok(highlightFeeDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving highlight fee for rank {RankName}", rankName);
                return StatusCode(500, new { Message = $"An error occurred while retrieving highlight fee for rank '{rankName}'" });
            }
        }

        /// <summary>
        /// Get all member rankings
        /// </summary>
        /// <returns>List of member rankings</returns>
        /// <response code="200">Returns the list of member rankings</response>
        [HttpGet("member-rankings")]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<MemberRankListDto>>> GetMemberRankings()
        {
            try
            {
                var memberRankings = await _yezDataService.GetAllMemberRankingsAsync();

                return Ok(memberRankings);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving member rankings");
                return StatusCode(500, new { Message = "An error occurred while retrieving member rankings" });
            }
        }

        /// <summary>
        /// Get member ranking by rank name
        /// </summary>
        /// <param name="rankName">The name of the membership rank</param>
        /// <returns>The member ranking for the specified rank</returns>
        /// <response code="200">Returns the member ranking</response>
        /// <response code="404">If the rank is not found</response>
        [HttpGet("member-rankings/{rankName}")]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<MemberRankListDto>> GetMemberRankingByName(string rankName)
        {
            try
            {
                var memberRankingDto = await _yezDataService.GetMemberRankingByNameAsync(rankName);

                if (memberRankingDto == null)
                {
                    return NotFound($"Member ranking '{rankName}' not found");
                }

                return Ok(memberRankingDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving member ranking {RankName}", rankName);
                return StatusCode(500, new { Message = $"An error occurred while retrieving member ranking '{rankName}'" });
            }
        }
    }
}
