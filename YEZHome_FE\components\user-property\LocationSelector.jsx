"use client";

import { useState, useEffect, forwardRef, useImperativeHandle, useRef } from "react";
import { FormField, FormItem, FormLabel, FormMessage, FormControl } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useTranslations } from "next-intl";
import { useToast } from "@/hooks/use-toast";
import { FormType } from "@/lib/enum";

const LocationSelector = forwardRef(({ form, isFormDisabled, property, formType, targetLocationNames, setTargetLocationNames }, ref) => {
  const t = useTranslations("PropertyForm");
  const { toast } = useToast();

  const [cities, setCities] = useState([]);
  const [districts, setDistricts] = useState([]);
  const [wards, setWards] = useState([]);

  const prevSelectedCityRef = useRef();
  const prevSelectedDistrictRef = useRef();
  const prevSelectedWardRef = useRef();

  // Expose cities, districts, and wards data to parent component
  useImperativeHandle(ref, () => ({
    cities,
    districts,
    wards,
  }));

  const selectedCity = form.watch("cityId");
  const selectedDistrict = form.watch("districtId");

  useEffect(() => {
    if (property && formType === FormType.EDIT) {
      prevSelectedCityRef.current = property?.cityId.toString();
      prevSelectedDistrictRef.current = property?.districtId.toString();
      prevSelectedWardRef.current = property?.wardId.toString();
    }
  }, [property, formType]);

  // Fetch cities
  useEffect(() => {
    console.log(`use effect for fetch cities`);
    const fetchCities = async () => {
      try {
        const response = await fetch(`/api/map/cities`);
        if (!response.ok) throw new Error(t("fetchCitiesError"));
        const data = await response.json();
        setCities(data);
      } catch (error) {
        toast({ variant: "destructive", description: error.message || t("fetchCitiesError") });
      }
    };

    fetchCities();
  }, [form, toast, t]);

  // Fetch districts based on selected city
  useEffect(() => {
    console.log(`use effect for fetch districts base on city ${selectedCity}`);

    const prev = prevSelectedCityRef.current;
    const current = selectedCity;

    console.log(`prev: ${prev}, current: ${current}`);

    if (!selectedCity && !targetLocationNames) {
      setDistricts([]);
      setWards([]);
      form.setValue("districtId", "");
      form.setValue("wardId", "");
      return;
    }

    const fetchDistricts = async () => {
      const controller = new AbortController();
      try {
        const response = await fetch(`/api/map/districts?cityId=${selectedCity}`, {
          signal: controller.signal,
        });
        if (!response.ok) throw new Error(t("fetchDistrictsError"));
        const data = await response.json();
        setDistricts(data);

        if (targetLocationNames && targetLocationNames.district) {
          const matchDistrictId = data.find((district) => district.nameWithType === targetLocationNames.district);
          form.setValue("districtId", matchDistrictId?.id.toString());
        } else if (prev && prev !== current) {
          form.setValue("districtId", "");
        }
      } catch (error) {
        if (error.name !== "AbortError") {
          toast({ variant: "destructive", description: error.message || t("fetchDistrictsError") });
        }
      }
      return () => controller.abort();
    };

    fetchDistricts();
    prevSelectedCityRef.current = selectedCity;
  }, [selectedCity, form, t, targetLocationNames]);

  // Fetch wards based on selected district
  useEffect(() => {
    console.log(`use effect for fetch ward base on district ${selectedDistrict}`);

    const prev = prevSelectedDistrictRef.current;
    const current = selectedDistrict;

    console.log(`prev: ${prev}, current: ${current}`);

    if ((!selectedDistrict || selectedDistrict === "") && !targetLocationNames) {
      setWards([]);
      form.setValue("wardId", "");
      return;
    }

    const fetchWards = async () => {
      const controller = new AbortController();
      try {

        if (!selectedDistrict || selectedDistrict === "") return;

        const response = await fetch(`/api/map/wards?districtId=${selectedDistrict}`, {
          signal: controller.signal,
        });
        if (!response.ok) throw new Error(t("fetchWardsError"));
        const data = await response.json();
        setWards(data);

        if (targetLocationNames && targetLocationNames.ward) {
          const matchWardId = data.find((ward) => ward.nameWithType === targetLocationNames.ward);
          form.setValue("wardId", matchWardId?.id.toString());
        } else if (prev && prev !== current) {
          form.setValue("wardId", "");
        }
      } catch (error) {
        if (error.name !== "AbortError") {
          toast({ variant: "destructive", description: error.message || t("fetchWardsError") });
        }
      }
      return () => controller.abort();
    };

    fetchWards();
    prevSelectedDistrictRef.current = selectedDistrict;
  }, [selectedDistrict, form, t, targetLocationNames]);

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-3">
      <div>
        {/* City Select */}
        <FormField
          control={form.control}
          name="cityId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("city")}</FormLabel>
              <FormControl>
                <Select
                  onValueChange={(e) => {
                    field.onChange(e);
                  }}
                  defaultValue={field.value ?? ""}
                  value={field.value}
                  disabled={isFormDisabled}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder={t("cityPlaceholder")} />
                  </SelectTrigger>
                  <SelectContent>
                    {cities?.map((city) => (
                      <SelectItem key={city.id} value={city?.id?.toString()}>
                        {city.nameWithType}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      <div>
        {/* District Select */}
        <FormField
          control={form.control}
          name="districtId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("district")}</FormLabel>
              <FormControl>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value ?? ""}
                  value={field.value}
                  disabled={!selectedCity || isFormDisabled}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder={t("districtPlaceholder")} />
                  </SelectTrigger>
                  <SelectContent>
                    {districts.map((district) => (
                      <SelectItem key={district.id} value={district.id?.toString()}>
                        {district.nameWithType}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      <div>
        {/* Ward Select */}
        <FormField
          control={form.control}
          name="wardId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("ward")}</FormLabel>
              <FormControl>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value ?? ""}
                  value={field.value}
                  disabled={!selectedDistrict || isFormDisabled}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder={t("wardPlaceholder")} />
                  </SelectTrigger>
                  <SelectContent>
                    {wards.map((ward) => (
                      <SelectItem key={ward.id} value={ward?.id?.toString()}>
                        {ward.nameWithType}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
});

export default LocationSelector;
