import { <PERSON><PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Check<PERSON>heck } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import {Link} from '@/i18n/navigation';;
import { getTranslations } from "next-intl/server";

const renderDemo1 = async () => {
  const t = await getTranslations("CreatePropertySuccessPage");

  return (
    <div className="w-full">
      <Card>
        <CardHeader className="border-b border-border p-4">
          <CardTitle className="text-lg text-center">
            {t("pageTitle")}
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="flex flex-col items-center gap-4">
            <div className="w-12 h-12 rounded-full bg-success flex justify-center items-center">
              <CheckCheck className="text-success-foreground w-6 h-6" />
            </div>
            <h3 className="text-xl font-semibold text-success">
              {t("successMessage")}
            </h3>
            <div className="text-muted-foreground text-sm text-center">
              {t("baseCostLabel")}
            </div>
            <p className="text-center text-sm text-muted-foreground">
              {t("moderationNotice")}
            </p>
          </div>
          <Separator className="my-6" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="font-semibold text-base">{t("statusLabel")}</h4>
              <Badge variant="outline">{t("statusPending")}</Badge>
            </div>
            <div className="space-y-4">
              <h4 className="font-semibold text-base">{t("listingIdLabel")}</h4>
              <p className="text-sm text-muted-foreground">6456465</p>
            </div>
            <div className="space-y-4">
              <h4 className="font-semibold text-base">
                {t("displayTimeLabel")}
              </h4>
              <p className="text-sm text-muted-foreground">
                12/03/2024 - 22/03/2024
              </p>
            </div>
            <div className="space-y-4">
              <h4 className="font-semibold text-base">Phí cơ bản</h4>
              <p className="text-sm text-muted-foreground">{t("baseCostLabel")}</p>
            </div>
          </div>
          <Separator className="my-6" />
          <div>
            <h4 className="font-semibold text-base mb-4">
              {t("highlightTitle")}
            </h4>
            <Card className="overflow-hidden">
              <div className="bg-muted/40 p-4">
                <div className="flex justify-between items-center">
                  <h5 className="font-medium text-sm">
                    {t("highlightPackageTitle")}
                  </h5>
                  <Switch defaultChecked />
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {t("highlightDuration")}
                </p>
              </div>
              <div className="p-4 grid grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <input type="radio" id="push3" name="highlight" value="3" />
                  <label htmlFor="push3" className="text-sm">
                    {t("highlightOption3")}
                  </label>
                </div>
                <div className="flex items-center gap-2">
                  <input type="radio" id="push6" name="highlight" value="6" />
                  <label htmlFor="push6" className="text-sm">
                    {t("highlightOption6")}
                  </label>
                </div>
              </div>
            </Card>
            <Card className="overflow-hidden mt-4">
              <div className="bg-muted/40 p-4">
                <div className="flex justify-between items-center">
                  <h5 className="font-medium text-sm">{t("autoRenewTitle")}</h5>
                  <Switch defaultChecked />
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {t("autoRenewDescription")}
                </p>
              </div>
            </Card>
          </div>
          <Separator className="my-6" />
          <div className="flex justify-end gap-4">
            <Button variant="outline" asChild>
              <Link href="../new">{t("postAnotherButton")}</Link>
            </Button>
            <Button asChild>
              <Link href="../">{t("manageListingsButton")}</Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

const CreateSuccess = () => {
  return <>{renderDemo1()}</>;
};

export default CreateSuccess;
