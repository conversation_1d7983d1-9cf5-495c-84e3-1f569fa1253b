﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Common;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;

namespace RealEstate.Application.Services
{
    public class PropertyService : IPropertyService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public PropertyService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<PropertyDto> GetPropertyByIdAsync(Guid id, bool asNoTracking = true)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(id, asNoTracking, p => p.PropertyMedia, p => p.PropertyReviews);
            return _mapper.Map<PropertyDto>(property);
        }

        public async Task<IEnumerable<PropertyDto>> GetAllPropertiesAsync()
        {
            var properties = await _unitOfWork.Properties.GetAllAsync(false);
            return _mapper.Map<IEnumerable<PropertyDto>>(properties);
        }

        public async Task<IEnumerable<PropertyDto>> GetPropertyByUserAsync(Guid userId)
        {
            var properties = await _unitOfWork.Properties.FindAsync(p => p.OwnerID == userId);
            return _mapper.Map<IEnumerable<PropertyDto>>(properties);
        }

        public async Task<PagedResultDto<PropertyDto>> GetPropertyByUserWithStatusAsync(Guid userId, List<string> statuses, int page = 1, int pageSize = 10)
        {
            // Start with a query for the user's properties
            var query = _unitOfWork.Properties.GetQueryable()
                .Include(p => p.PropertyMedia)
                .Where(p => p.OwnerID == userId && !p.IsDeleted);

            // Apply status filter if provided
            if (statuses != null && statuses.Any())
            {
                query = query.Where(p => statuses.Contains(p.Status));
            }

            // Count total items before pagination
            var totalCount = await query.CountAsync();

            // Apply pagination
            var pageCount = (int)Math.Ceiling(totalCount / (double)pageSize);

            // Ensure current page is valid
            if (page < 1)
                page = 1;
            else if (page > pageCount && pageCount > 0)
                page = pageCount;

            // Apply pagination and get items
            var items = await query
                .OrderByDescending(p => p.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            // Map to DTOs
            var itemDtos = _mapper.Map<List<PropertyDto>>(items);

            // Create and return paged result
            return new PagedResultDto<PropertyDto>
            {
                Items = itemDtos,
                TotalCount = totalCount,
                PageCount = pageCount,
                CurrentPage = page,
                PageSize = pageSize
            };
        }

        public async Task<PropertyDto> CreatePropertyAsync(CreatePropertyDto propertyDto, Guid userId)
        {
            var property = _mapper.Map<Property>(propertyDto);

            if (propertyDto.OwnerId == null)
            {
                property.OwnerID = userId;
            }
            else
            {
                property.OwnerID = propertyDto.OwnerId.Value;
            }

            property.Status = propertyDto.Status ?? EnumValues.PropertyStatus.PendingApproval.ToString();
            property.CreatedBy = userId;
            property.CreatedAt = DateTime.UtcNow;
            property.GenerateSlugFromName();
            await _unitOfWork.Properties.AddAsync(property);

            var updateStatusLog = new PropertyStatusLog
            {
                PropertyID = property.Id,
                ChangedBy = userId,
                ChangedAt = DateTime.UtcNow,
                Status = property.Status,
                Comment = "tạo tin đăng thành công",
            };

            await _unitOfWork.PropertyStatusLogs.AddAsync(updateStatusLog);

            await _unitOfWork.SaveChangesAsync();
            return _mapper.Map<PropertyDto>(property);
        }

        public async Task<PropertyDto> UpdatePropertyAsync(Guid id, CreatePropertyDto propertyDto, Guid userId)
        {
            // Validate input
            if (propertyDto == null)
            {
                throw new ArgumentNullException(nameof(propertyDto));
            }

            // Fetch the property to update
            var property = await _unitOfWork.Properties.GetByIdAsync(id);
            if (property == null)
            {
                throw new NotFoundException($"Property with ID {id} not found.");
            }

            _mapper.Map(propertyDto, property);

            property.UpdatedBy = userId;
            property.GenerateSlugFromName();

            _unitOfWork.Properties.Update(property);

            var updateStatusLog = new PropertyStatusLog
            {
                PropertyID = id,
                ChangedBy = userId,
                ChangedAt = DateTime.UtcNow,
                Status = propertyDto.Status,
                Comment = "update thông tin của tin đăng",
            };

            await _unitOfWork.PropertyStatusLogs.AddAsync(updateStatusLog);

            await _unitOfWork.SaveChangesAsync();

            return _mapper.Map<PropertyDto>(property);
        }

        public async Task<bool> DeletePropertyAsync(Guid id, Guid userId)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(id);
            if (property == null) return false;

            property.UpdatedBy = userId;
            _unitOfWork.Properties.Remove(property);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeletePropertiesAsync(List<Guid> ids, Guid userId)
        {
            if (ids == null || !ids.Any())
                return false;

            bool allSuccessful = true;

            foreach (var id in ids)
            {
                var property = await _unitOfWork.Properties.GetByIdAsync(id);
                if (property == null)
                {
                    allSuccessful = false;
                    continue;
                }

                property.UpdatedBy = userId;
                _unitOfWork.Properties.Remove(property);
            }

            await _unitOfWork.SaveChangesAsync();
            return allSuccessful;
        }

        public async Task<bool> UpdateStatusAsync(Guid id, Guid userId, UpdateStatusDto updateStatusDto)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(id);
            if (property == null) return false;

            property.Status = updateStatusDto.Status;
            property.UpdatedBy = userId;
            _unitOfWork.Properties.Update(property);

            var updateStatusLog = new PropertyStatusLog
            {
                PropertyID = id,
                ChangedBy = userId,
                ChangedAt = DateTime.UtcNow,
                Status = updateStatusDto.Status,
                Comment = updateStatusDto.Status == EnumValues.PropertyStatus.Sold.ToString() ? "Giao dịch thành công" : updateStatusDto.Comment,
            };

            await _unitOfWork.PropertyStatusLogs.AddAsync(updateStatusLog);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> UpdateStatusBulkAsync(List<Guid> ids, Guid userId, UpdateStatusDto updateStatusDto)
        {
            if (ids == null || !ids.Any())
                return false;

            bool allSuccessful = true;

            foreach (var id in ids)
            {
                var property = await _unitOfWork.Properties.GetByIdAsync(id);
                if (property == null)
                {
                    allSuccessful = false;
                    continue;
                }

                property.Status = updateStatusDto.Status;
                property.UpdatedBy = userId;
                _unitOfWork.Properties.Update(property);

                var updateStatusLog = new PropertyStatusLog
                {
                    PropertyID = id,
                    ChangedBy = userId,
                    ChangedAt = DateTime.UtcNow,
                    Status = updateStatusDto.Status,
                    Comment = updateStatusDto.Status == EnumValues.PropertyStatus.Sold.ToString() ? "Giao dịch thành công" : updateStatusDto.Comment,
                };

                await _unitOfWork.PropertyStatusLogs.AddAsync(updateStatusLog);
            }

            await _unitOfWork.SaveChangesAsync();
            return allSuccessful;
        }

        public async Task<bool> UpdateHighlightAsync(Guid id, Guid userId, bool isHighlighted)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(id);
            if (property == null) return false;

            property.IsHighlighted = isHighlighted;
            property.UpdatedBy = userId;
            _unitOfWork.Properties.Update(property);

            var updateStatusLog = new PropertyStatusLog
            {
                PropertyID = id,
                ChangedBy = userId,
                ChangedAt = DateTime.UtcNow,
                Status = property.Status,
                Comment = isHighlighted ? "Đánh dấu nổi bật" : "Bỏ đánh dấu nổi bật",
            };

            await _unitOfWork.PropertyStatusLogs.AddAsync(updateStatusLog);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> UpdateHighlightBulkAsync(List<Guid> ids, Guid userId, bool isHighlighted)
        {
            if (ids == null || !ids.Any())
                return false;

            bool allSuccessful = true;

            foreach (var id in ids)
            {
                var property = await _unitOfWork.Properties.GetByIdAsync(id);
                if (property == null)
                {
                    allSuccessful = false;
                    continue;
                }

                property.IsHighlighted = isHighlighted;
                property.UpdatedBy = userId;
                _unitOfWork.Properties.Update(property);

                var updateStatusLog = new PropertyStatusLog
                {
                    PropertyID = id,
                    ChangedBy = userId,
                    ChangedAt = DateTime.UtcNow,
                    Status = property.Status,
                    Comment = isHighlighted ? "Đánh dấu nổi bật" : "Bỏ đánh dấu nổi bật",
                };

                await _unitOfWork.PropertyStatusLogs.AddAsync(updateStatusLog);
            }

            await _unitOfWork.SaveChangesAsync();
            return allSuccessful;
        }

        public async Task<IEnumerable<PropertyStatusLogDto>> GetPropertyHistoryStatus(Guid propertyId)
        {
            var history = await _unitOfWork.PropertyStatusLogs.FindAsync(p => p.PropertyID == propertyId);
            return _mapper.Map<IEnumerable<PropertyStatusLogDto>>(history.OrderByDescending(x => x.ChangedAt));
        }

        public async Task<int> VerifyPropertyRemainingTimes(Guid propertyId)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(propertyId);
            if (property == null) throw new NotFoundException("Bất động sản không tồn tại.");

            var remainingTime = property.UpdateRemainingTimes;
            return remainingTime ?? 0;
        }

        public async Task<PropertyCountStatsDto> GetPropertyCountStatsByUserAsync(Guid userId)
        {
            // Get all properties for the user (not deleted)
            var properties = await _unitOfWork.Properties.FindAsync(p => p.OwnerID == userId && !p.IsDeleted);

            // Create the result object
            var result = new PropertyCountStatsDto
            {
                TotalProperties = properties.Count()
            };

            // Initialize the dictionary with all possible status values
            foreach (var status in Enum.GetNames(typeof(EnumValues.PropertyStatus)))
            {
                result.PropertiesByStatus[status] = 0;
            }

            // Count properties by status
            foreach (var property in properties)
            {
                if (result.PropertiesByStatus.ContainsKey(property.Status))
                {
                    result.PropertiesByStatus[property.Status]++;
                }
                else
                {
                    // Handle any status that might not be in the enum (for backward compatibility)
                    result.PropertiesByStatus[property.Status] = 1;
                }
            }

            return result;
        }

        public async Task<PagedResultDto<PropertyDto>> SearchPropertiesAsync(PropertyFilterCriteriaDto filterCriteria)
        {
            // Start with a query that gets all non-deleted properties
            var query = _unitOfWork.Properties.GetQueryable()
                .Include(p => p.PropertyMedia)
                .Where(p => !p.IsDeleted && p.Status == "Approved");

            // Apply filters
            if (filterCriteria.PostTypes != null && filterCriteria.PostTypes.Any())
                query = query.Where(p => filterCriteria.PostTypes.Contains(p.PostType));

            if (filterCriteria.PropertyTypes != null && filterCriteria.PropertyTypes.Any())
                query = query.Where(p => filterCriteria.PropertyTypes.Contains(p.PropertyType));

            //if (!string.IsNullOrEmpty(filterCriteria.CityId))
            //    query = query.Where(p => p.CityId == filterCriteria.CityId);

            //if (!string.IsNullOrEmpty(filterCriteria.DistrictId))
            //    query = query.Where(p => p.DistrictId == filterCriteria.DistrictId);

            if (!string.IsNullOrEmpty(filterCriteria.Address))
                query = query.Where(p => p.Address.Contains(filterCriteria.Address));

            if (filterCriteria.MinPrice.HasValue)
            {
                var minPrice = filterCriteria.MinPrice.Value * 1000000; // Convert to milion
                query = query.Where(p => p.Price >= minPrice);
            }

            if (filterCriteria.MaxPrice.HasValue)
            {
                var maxPrice = filterCriteria.MaxPrice.Value * 1000000; // Convert to milion
                query = query.Where(p => p.Price <= maxPrice);
            }

            if (filterCriteria.MinArea.HasValue)
                query = query.Where(p => p.Area >= filterCriteria.MinArea.Value);

            if (filterCriteria.MaxArea.HasValue)
                query = query.Where(p => p.Area <= filterCriteria.MaxArea.Value);

            if (filterCriteria.MinRooms.HasValue)
                query = query.Where(p => p.Rooms >= filterCriteria.MinRooms.Value);

            if (filterCriteria.MinToilets.HasValue)
                query = query.Where(p => p.Toilets >= filterCriteria.MinToilets.Value);

            if (!string.IsNullOrEmpty(filterCriteria.Direction))
                query = query.Where(p => p.Direction == filterCriteria.Direction);

            if (!string.IsNullOrEmpty(filterCriteria.Legality))
                query = query.Where(p => p.Legality == filterCriteria.Legality);

            if (filterCriteria.MinRoadWidth.HasValue)
                query = query.Where(p => p.RoadWidth >= filterCriteria.MinRoadWidth.Value);

            // Apply geographic filters if provided
            if (filterCriteria.SwLat.HasValue && filterCriteria.SwLng.HasValue && filterCriteria.NeLat.HasValue && filterCriteria.NeLng.HasValue)
            {
                query = query.Where(p =>
                    p.Latitude >= filterCriteria.SwLat.Value &&
                    p.Latitude <= filterCriteria.NeLat.Value &&
                    p.Longitude >= filterCriteria.SwLng.Value &&
                    p.Longitude <= filterCriteria.NeLng.Value);
            }

            //// Proximity search if coordinates and radius are provided
            //if (filterCriteria.Latitude.HasValue && filterCriteria.Longitude.HasValue && filterCriteria.Radius.HasValue)
            //{
            //    double earthRadiusKm = 6371; // Earth's radius in km

            //    // Convert latitude and longitude to radians BEFORE using them in the query
            //    double latRad = filterCriteria.Latitude.Value * Math.PI / 180.0;
            //    double lonRad = filterCriteria.Longitude.Value * Math.PI / 180.0;

            //    query = query.Where(l =>
            //                (earthRadiusKm * Math.Acos(
            //                    Math.Sin(latRad) * Math.Sin(Convert.ToDouble(l.Latitude) * Math.PI / 180.0) +
            //                    Math.Cos(latRad) * Math.Cos(Convert.ToDouble(l.Latitude) * Math.PI / 180.0) *
            //                    Math.Cos((Convert.ToDouble(l.Longitude) * Math.PI / 180.0) - lonRad)
            //                )) <= filterCriteria.Radius);
            //}

            // Count total items before pagination
            var totalCount = await query.CountAsync();

            // Apply pagination
            var pageSize = filterCriteria.PageSize;
            var pageCount = (int)Math.Ceiling(totalCount / (double)pageSize);
            var currentPage = filterCriteria.Page;

            // Ensure current page is valid
            if (currentPage < 1)
                currentPage = 1;
            else if (currentPage > pageCount && pageCount > 0)
                currentPage = pageCount;

            // Apply pagination and get items
            var items = await query
                .Skip((currentPage - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            // Map to DTOs
            var itemDtos = _mapper.Map<List<PropertyDto>>(items);

            // Create and return paged result
            return new PagedResultDto<PropertyDto>
            {
                Items = itemDtos,
                TotalCount = totalCount,
                PageCount = pageCount,
                CurrentPage = currentPage,
                PageSize = pageSize
            };
        }

        // Helper method for distance calculation (Haversine formula)
        private double CalculateDistance(double lat1, double lon1, decimal lat2Decimal, decimal lon2Decimal)
        {
            double lat2 = Convert.ToDouble(lat2Decimal);
            double lon2 = Convert.ToDouble(lon2Decimal);

            const double EarthRadiusKm = 6371;

            var dLat = DegreesToRadians(lat2 - lat1);
            var dLon = DegreesToRadians(lon2 - lon1);

            var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                    Math.Cos(DegreesToRadians(lat1)) * Math.Cos(DegreesToRadians(lat2)) *
                    Math.Sin(dLon / 2) * Math.Sin(dLon / 2);

            var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
            return EarthRadiusKm * c;
        }

        private double DegreesToRadians(double degrees)
        {
            return degrees * Math.PI / 180;
        }

        public async Task<bool> UpdatePropertyRenewalAsync(UpdatePropertyRenewalDto updateDto, Guid userId)
        {
            // Validate input
            if (updateDto == null)
            {
                throw new ArgumentNullException(nameof(updateDto));
            }

            // Fetch the property to update
            var property = await _unitOfWork.Properties.GetByIdAsync(updateDto.PropertyId, false);
            if (property == null)
            {
                throw new NotFoundException($"Property with ID {updateDto.PropertyId} not found.");
            }

            // Update expiration date
            property.ExpiresAt = updateDto.ExpiresAt;

            // Increment renewal count if requested
            if (updateDto.IncrementRenewalCount)
            {
                property.RenewalCount = (property.RenewalCount ?? 0) + 1;
            }

            // Update the property
            property.UpdatedBy = userId;
            _unitOfWork.Properties.Update(property);
            await _unitOfWork.SaveChangesAsync();

            return true;
        }
    }
}
