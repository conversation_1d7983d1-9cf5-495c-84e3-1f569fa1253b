"use client";

import { useEffect, useState } from "react";

import "react-multi-carousel/lib/styles.css"; // Đảm bảo import CSS c<PERSON>a thư viện
import { getNearbyProperties } from "@/app/actions/server/property";
import Image from "next/image";
import { Link } from "@/i18n/navigation";
import { cn, formatCurrency } from "@/lib/utils";
import "react-multi-carousel/lib/styles.css";
import { Heart } from "lucide-react";
import { Badge } from "../ui/badge";
import dynamic from "next/dynamic";

const Carousel = dynamic(() => import("react-multi-carousel"), { ssr: false });

export default function NearbyPropertiesCarousel({ latitude, longitude, tCommon }) {
  const [properties, setProperties] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchNearbyProperties = async () => {
      try {
        const response = await getNearbyProperties(latitude, longitude, 10);

        if (!response.data || !response.data.items || response.data?.items?.length === 0) {
          setProperties([]);
          console.log("No nearby properties found");
          return;
        }
        setProperties(response.data.items);
      } catch (error) {
        console.error("Error fetching nearby properties:", error);
      } finally {
        setIsLoading(false);
      }
    };

    if (latitude && longitude) {
      fetchNearbyProperties();
    }
  }, [latitude, longitude]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-48">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!properties.length) {
    return <div className="text-center text-gray-500 py-8">No nearby properties found</div>;
  }

  // Helper function to get the first image from propertyMedia
  const getFirstImage = (property) => {
    if (property.propertyMedia && property.propertyMedia.length > 0) {
      return property.propertyMedia[0].mediaURL;
    }
    return "/placeholder.svg?height=128&width=256";
  };

  // Define responsive breakpoints for the carousel
  const responsive = {
    superLargeDesktop: {
      breakpoint: { max: 4000, min: 1280 },
      items: 4,
      slidesToSlide: 2,
    },
    desktop: {
      breakpoint: { max: 1280, min: 1024 },
      items: 3,
      slidesToSlide: 1,
    },
    tablet: {
      breakpoint: { max: 1024, min: 640 },
      items: 2,
      slidesToSlide: 1,
    },
    mobile: {
      breakpoint: { max: 640, min: 0 },
      items: 1,
      slidesToSlide: 1,
    },
  };

  return (
    <Carousel
      swipeable={false}
      draggable={false}
      showDots={true}
      responsive={responsive}
      ssr={true} // means to render carousel on server-side.
      infinite={true}
      keyBoardControl={true}
      customTransition="all .5"
      transitionDuration={500}
      containerClass="carousel-container"
      removeArrowOnDeviceType={["tablet", "mobile"]}
      dotListClass="custom-dot-list-style"
      itemClass="p-2"
    >
      {properties.map((property) => (
        <div key={property.id}>
          <Link href={`/bds/${property.id}`}>
            <div className="border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
              <div className="relative h-36 ">
                <Image
                  src={getFirstImage(property)}
                  alt={property.name || "Property"}
                  fill
                  className="w-full h-36 object-cover"
                />
                <button
                  onClick={() => toggleFavorite(property.id)}
                  className="absolute top-2 right-2 p-2 rounded-full bg-white/80 hover:bg-white"
                  aria-label={property.isFavorite ? "Remove from favorites" : "Add to favorites"}
                >
                  <Heart
                    className={cn(
                      "h-6 w-6",
                      property.isFavorite ? "fill-rose-500 text-rose-500" : "text-gray-500"
                    )}
                  />
                </button>
              </div>
              <div className="p-4">
                <div className="font-bold text-xl mb-1">{formatCurrency(property.price)}</div>
                <div className="flex items-center gap-2 text-sm mb-2">
                  <span>{property.rooms || "__"} PN</span>
                  <span>|</span>
                  <span>{property.toilets || "__"} PT</span>
                  <span>|</span>
                  <span>{property.area || "__"} m²</span>
                </div>
                <div className="text-sm font-medium mb-1 line-clamp-1">{property.name}</div>
                <div className="text-sm text-gray-600 mb-2 line-clamp-1">{property.address}</div>
                <Badge variant="outline">
                  {tCommon(`propertyType_${property.propertyType}`) || property.propertyType}
                </Badge>
              </div>
            </div>
          </Link>
        </div>
      ))}
    </Carousel>
  );
}
