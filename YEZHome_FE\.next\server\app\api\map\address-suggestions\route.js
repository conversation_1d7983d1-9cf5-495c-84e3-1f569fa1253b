/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/map/address-suggestions/route";
exports.ids = ["app/api/map/address-suggestions/route"];
exports.modules = {

/***/ "(rsc)/./app/api/map/address-suggestions/route.jsx":
/*!***************************************************!*\
  !*** ./app/api/map/address-suggestions/route.jsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\nasync function GET(req) {\n    const { searchParams } = new URL(req.url);\n    const input = searchParams.get(\"input\");\n    if (!input) {\n        return Response.json({\n            error: \"Missing input query\"\n        }, {\n            status: 400\n        });\n    }\n    try {\n        const apiURl = `https://rsapi.goong.io/Place/AutoComplete?api_key=${process.env.GOONG_GEO_API_KEY}&input=${encodeURIComponent(input)}&limit=15&more_compound=true`;\n        const response = await fetch(apiURl);\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch address suggestions\");\n        }\n        const data = await response.json();\n        return Response.json(data, {\n            status: 200\n        });\n    } catch (error) {\n        console.error(error);\n        return Response.json({\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/map/address-suggestions/route.jsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmap%2Faddress-suggestions%2Froute&page=%2Fapi%2Fmap%2Faddress-suggestions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmap%2Faddress-suggestions%2Froute.jsx&appDir=D%3A%5CProject%5CYEZ_Tech%5CYEZ_Home%5CYEZHome_FE%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5CYEZ_Tech%5CYEZ_Home%5CYEZHome_FE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmap%2Faddress-suggestions%2Froute&page=%2Fapi%2Fmap%2Faddress-suggestions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmap%2Faddress-suggestions%2Froute.jsx&appDir=D%3A%5CProject%5CYEZ_Tech%5CYEZ_Home%5CYEZHome_FE%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5CYEZ_Tech%5CYEZ_Home%5CYEZHome_FE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Project_YEZ_Tech_YEZ_Home_YEZHome_FE_app_api_map_address_suggestions_route_jsx__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/map/address-suggestions/route.jsx */ \"(rsc)/./app/api/map/address-suggestions/route.jsx\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/map/address-suggestions/route\",\n        pathname: \"/api/map/address-suggestions\",\n        filename: \"route\",\n        bundlePath: \"app/api/map/address-suggestions/route\"\n    },\n    resolvedPagePath: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\app\\\\api\\\\map\\\\address-suggestions\\\\route.jsx\",\n    nextConfigOutput,\n    userland: D_Project_YEZ_Tech_YEZ_Home_YEZHome_FE_app_api_map_address_suggestions_route_jsx__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmap%2Faddress-suggestions%2Froute&page=%2Fapi%2Fmap%2Faddress-suggestions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmap%2Faddress-suggestions%2Froute.jsx&appDir=D%3A%5CProject%5CYEZ_Tech%5CYEZ_Home%5CYEZHome_FE%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5CYEZ_Tech%5CYEZ_Home%5CYEZHome_FE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmap%2Faddress-suggestions%2Froute&page=%2Fapi%2Fmap%2Faddress-suggestions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmap%2Faddress-suggestions%2Froute.jsx&appDir=D%3A%5CProject%5CYEZ_Tech%5CYEZ_Home%5CYEZHome_FE%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5CYEZ_Tech%5CYEZ_Home%5CYEZHome_FE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();