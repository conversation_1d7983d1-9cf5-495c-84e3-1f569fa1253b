"use server";

import { handleErrorResponse, logError } from "@/lib/apiUtils";
import { fetchWithAuth } from "@/lib/sessionUtils";

const API_BASE_URL = `${process.env.API_URL}/api/UserFavorites`;

export async function addToFavorites(propertyId) {
  try {
    return await fetchWithAuth(`${API_BASE_URL}/add`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ propertyId }),
    });
  } catch (error) {
    logError("UserService", error, {
      action: "addToFavorites",
      propertyId,
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi thêm vào danh sách yêu thích");
  }
}

export async function removeFromFavorites(propertyId) {
  try {
    return await fetchWithAuth(`${API_BASE_URL}/remove/${propertyId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("UserService", error, {
      action: "removeFromFavorites",
      propertyId,
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi xóa khỏi danh sách yêu thích");
  }
}

export async function checkFavoriteStatus(propertyIds) {
  try {
    return await fetchWithAuth(`${API_BASE_URL}/check`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ propertyIds: Array.isArray(propertyIds) ? propertyIds : [propertyIds] }),
    });
  } catch (error) {
    logError("UserService", error, {
      action: "checkFavoriteStatus",
      propertyIds,
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi kiểm tra trạng thái yêu thích");
  }
}

export async function getFavoritesCount() {
  try {
    return await fetchWithAuth(`${API_BASE_URL}/count`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("UserService", error, {
      action: "getFavoritesCount",
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi lấy số lượng bất động sản yêu thích");
  }
}

/**
 * Gets the user's favorite properties
 * @returns {Promise<{success: boolean, data: Array<UserFavoriteDto>, message: string}>} Response with array of UserFavoriteDto objects
 * @description UserFavoriteDto contains: id, propertyId, createdAt
 */
export async function getUserFavorites() {
  try {
    return await fetchWithAuth(`${API_BASE_URL}/favorites`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("UserService", error, {
      action: "getUserFavorites",
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi lấy danh sách bất động sản yêu thích");
  }
}
