"use client";

import { createContext, useContext, useState, useEffect, useCallback } from "react";
import { logout } from "@/app/actions/server/authenticate";
import AlertPopup from "@/components/layout/AlertPopup";

const AuthContext = createContext(undefined);

export function AuthProvider({ children, initialAuthState }) {
  const [isLoggedIn, setIsLoggedIn] = useState(initialAuthState.isLoggedIn);
  const [isExpired, setIsExpired] = useState(initialAuthState.isExpired ?? false);

  // Function to handle logout
  const handleLogout = useCallback(async () => {
    setIsExpired(false);
    setIsLoggedIn(false);

    try {
      await logout();
    } catch (error) {
      console.error("Error logging out:", error);
    }
  }, []);

  useEffect(() => {
    console.log("isLoggedIn", isLoggedIn);
    if (isExpired) {
      handleLogout();
    }
  }, [isLoggedIn, isExpired]);

  return (
    <AuthContext.Provider value={{
      isLoggedIn,
      isExpired,
      handleLogout,
      setIsLoggedIn,
      setIsExpired,
    }}>
      {children}

      {/* Show alert if token is expired */}
      {isExpired && (
        <AlertPopup
          open={isExpired}
          title="Phiên đăng nhập hết hạn"
          message="Phiên đăng nhập của bạn đã hết hạn. Vui lòng đăng nhập lại."
          hasCancel={false}
          onConfirm={handleLogout}
        ></AlertPopup>
      )}
    </AuthContext.Provider>
  );
}

// Hook to use AuthContext
export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) throw new Error("useAuth must be used within an AuthProvider");
  return context;
}