---
description: 
globs: 
alwaysApply: false
---
---
description: 
globs: 
alwaysApply: true
---
***
description: 
globs: 
alwaysApply: true
***
# NextJS 15 with shadcnUI, Tailwind CSS, and .NET Core API

## Global Instructions
You are working with a NextJS 15 project using the App Router feature. The frontend is styled with shadcnUI and Tailwind CSS. Backend services are provided by a .NET Core API, which requires JWT for authentication.

## Project Structure
* Following the nextjs 15 app-route.

project*root/
├── app/
│ ├── actions/
│ │ └── server/
│ │   └── property.jsx
│ └── page.jsx
├── components/
│ ├── layout/
│ │ ├── Navbar.jsx
│ │ └── NoData.jsx
│ └── ui/
│  ├── button.jsx
│  ├── input.jsx
│  ├── MapSection.jsx
│  ├── PropertyList.jsx
│  ├── SearchFilter.jsx
│  └── [other UI components]
├── common/
│ └── NavBarData.js
└── hooks/

## Technology Stack

### Core Technologies
* **Next.js** * React framework with server*side rendering capabilities
* **React** * Frontend library for building user interfaces
* **Tailwind CSS** * Utility*first CSS framework

### UI Libraries
* **Lucide React** * Icon library
* **Shadcn UI** * Component library (customized components)
* **Goong Maps** * Mapping service for Vietnam

### Development Tools
* ESLint * Code linting
* Prettier * Code formatting

## Naming Conventions

### Components
* PascalCase for component names (e.g., `SearchFilter`, `PropertyList`)
* Suffix with `.jsx` for React components
* Descriptive names indicating functionality

### Variables and Functions
* camelCase for variables and functions
* Boolean variables prefixed with 'is' or 'has' (e.g., `isLoggedIn`, `hasCreateButton`)
* Event handlers prefixed with 'on' (e.g., `onFilterChange`, `onOpenChange`)

### CSS Classes
* Uses Tailwind CSS utility classes
* Custom classes in kebab*case when needed
* BEM*like naming for custom components

## JavaScript and TypeScript Conventions
* Use modern JavaScript features like ES6+.
* Prefer double quotes for strings.
* Use semicolons to end lines.
* Use functional components and hooks (useState, useEffect).

## Framework and Library Usage
* For frontend, leverage Next.js SSR and CSR as needed.
* Use `fetch` for API calls, encapsulating token handling in a dedicated helper.
* Frontend styled components should be modular and reusable, integrated with TailwindCSS, and accessible using shadcnUI components.
* Implement responsive design principles.

## Backend Communication 
* normaly always create the new nextjs 'server function' in the 'action/server/[core-name-of-the-feature.jsx]. Example: property.jsx, user.jsx, blog.jsx
* incase of you think the 'api route' better than 'server function',  create the api route.
* with the API requests that have authenticate should use the function "fetchWithAuth" in the [sessionUtils.js](mdc:lib/sessionUtils.js)
* with the API that don't need authenticate just use "fetch"
* for the API endpoint: the METHOD, request and response data you should follow in the [swagger.json](mdc:document/swagger.json) file, if you think there is not API in this file can use to generate code, you can create the new api endpoint if needed and note all the details (ex: method, request data, response data, etc) of it then I can use it to create the API services backend code later. 

## Performance
* Utilize `React.memo()` and `useMemo` for performance improvements.

## State Management

### Local State
* Uses React's useState for component*level state
* Implements controlled components for form inputs
* Uses temporary states for popover contents

### Server State
* Implements server actions for data fetching
* Uses async/await for API calls
* Handles loading and error states

### Props Pattern
* Follows prop drilling for shallow component trees
* Uses callback props for parent*child communication
* Implements prop validation where necessary

## Utilities and Helpers

### Toast Notifications
* Custom hook: `use*toast`
* Provides feedback for user actions
* Handles success and error states

### Navigation
* Uses Next.js Link component
* Implements dynamic routing
* Handles authentication*based navigation

### Data Formatting
* Implements consistent data transformation
* Handles currency and measurement formatting
* Validates input data

## Automated Context Management
* Automatically generate and update `context.md` with new responses.
* Ensure `context.md` includes brief descriptions of project components and decisions.
* Use `context.md` to track changes in rules and major updates across sessions.

## Best Practices

1. **Component Organization**
   * Separate layout and UI components
   * Keep components focused and single*responsibility
   * Use composition over inheritance

2. **Performance Optimization**
   * Implement debouncing for search inputs
   * Use memo for expensive computations
   * Lazy load components when appropriate

3. **Error Handling**
   * Implement try*catch blocks for async operations
   * Display user*friendly error messages
   * Log errors for debugging

4. **Accessibility**
   * Use semantic HTML elements
   * Implement ARIA labels
   * Ensure keyboard navigation

5. **Responsive Design**
   * Mobile*first approach
   * Use Tailwind breakpoints consistently
   * Implement responsive navigation
