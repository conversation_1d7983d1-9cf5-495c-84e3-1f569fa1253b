"use client";

import ProfileContextTest from "@/components/test/ProfileContextTest";
import { Link } from '@/i18n/navigation';

export default function TestProfileContextPage() {
  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-4">Test Profile Context</h1>
      <p className="mb-4">This page tests the ProfileContext behavior when authentication changes.</p>

      <div className="bg-yellow-100 border-l-4 border-yellow-500 p-4 mb-6">
        <h2 className="font-bold text-lg mb-2">Testing Instructions</h2>
        <ol className="list-decimal pl-5 space-y-2">
          <li>This is a protected page - you must be logged in to view it</li>
          <li>The ProfileContext should only make API calls when you're authenticated</li>
          <li>To test logout behavior:
            <ul className="list-disc pl-5 mt-1">
              <li>Open browser dev tools and watch the console logs</li>
              <li>In another tab, <Link href="/dang-nhap" className="text-blue-600 underline">log out</Link></li>
              <li>Return to this tab - you should be redirected to login</li>
              <li>Check console logs - the interval should be cleared</li>
            </ul>
          </li>
          <li>After logging back in, return to this page to see the ProfileContext reactivate</li>
        </ol>
      </div>

      <ProfileContextTest />
    </div>
  );
}
