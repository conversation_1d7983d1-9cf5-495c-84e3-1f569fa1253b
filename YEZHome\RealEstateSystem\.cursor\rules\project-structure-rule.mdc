---
description: 
globs: 
alwaysApply: true
---

# Your rule content

# Real Estate Management System Documentation

## Project Overview

This is a comprehensive Real Estate Management System built with .NET Core, following a clean architecture pattern. The system enables property owners to list properties, buyers to browse and review properties, and includes additional features like blog posts, user management, and notifications.

## DB structure file 
*Always* follow the database tables, columns description in the file [DB_Script.sql](mdc:DB_Script.sql) 

## System Architecture

The project follows a layered architecture with clear separation of concerns:

### 1. Domain Layer
- Contains business entities and domain logic
- Defines interfaces for repositories and services
- Provides domain models and value objects

### 2. Application Layer
- Contains application-specific business logic
- Implements services that use domain entities
- Provides DTOs (Data Transfer Objects) for API communication
- Defines interfaces for infrastructure services

### 3. Infrastructure Layer
- Implements repositories for data access
- Provides database context and migrations
- Implements infrastructure-specific services
- Handles external services integration

### 4. API Layer
- Exposes RESTful endpoints for client applications
- Handles user authentication and authorization
- Implements controllers for different functional areas
- Manages request validation and response formatting

## Coding Style and Conventions

The project follows these coding conventions:

1. **Clean Architecture**: Separation of concerns with layered architecture
2. **Repository Pattern**: Data access through repository interfaces
3. **Unit of Work Pattern**: For transaction management
4. **Dependency Injection**: For loose coupling of components
5. **SOLID Principles**: Single responsibility, Open-closed, etc.
6. **Async/Await Pattern**: For non-blocking operations
7. **DTO Pattern**: For data transfer between layers
8. **Service Layer Pattern**: Business logic in dedicated services

## Domain Layer

### Key Entities

#### AppUser
- Represents users in the system (buyers, owners, admins)
- Contains personal information, authentication data
- Tracks user activity and spending
- Associated with wallet, properties, and reviews

#### Property
- Core entity representing real estate properties
- Contains property details, location, pricing
- Tracks property status (draft, approved, expired)
- Has relationship with owner, reviews, and media

#### Wallet
- Represents a user's financial account in the system
- Tracks balance and transactions
- Supports operations like top-up and spending

#### Notification
- Represents system messages to users
- Various types: news, wallet_update, promotion, customer_message
- Tracks read status and creation time

#### Reviews (PropertyReview and OwnerReview)
- Allows users to rate properties and owners
- Contains rating values and review text
- Links reviewer with the reviewed entity

#### BlogPost
- Represents articles and news in the blog section
- Contains content, author information
- Has associated comments

### Interface Definitions

The domain layer defines key interfaces:

- **IRepository<T>**: Generic CRUD operations
- **IAuditableRepository<T>**: Extends repository for auditable entities
- **IUnitOfWork**: Coordinates work across multiple repositories

## Application Layer

### Services

The application layer contains service implementations that perform business operations:

#### UserService
- Handles user registration, authentication, and profile management
- Manages user roles and permissions
- Provides user information retrieval methods

#### PropertyService
- Manages property listings including create, update, search
- Handles property status changes and expirations
- Supports filtering and searching properties

#### WalletService
- Manages financial transactions and wallet balances
- Handles deposits and payments
- Tracks spending history and updates user ranks

#### NotificationService
- Creates and manages user notifications
- Supports filtering notifications by type and user
- Handles notification read status and deletion

#### BlogService
- Manages blog posts and comments
- Supports content creation and moderation
- Provides methods for retrieving and filtering content

#### ReviewService
- Manages property and owner reviews
- Validates review submissions
- Aggregates ratings and feedback

### DTOs (Data Transfer Objects)

The application layer defines DTOs for data transfer:

- Input DTOs: For receiving data from clients
- Output DTOs: For sending data to clients
- Pagination DTOs: For handling paginated results

### Mapping

- Uses AutoMapper for entity-to-DTO mappings
- Maintains mapping profiles for different entity types
- Handles custom type conversions

## Infrastructure Layer

### Data Access

- Implements EF Core for database operations
- Includes migrations for schema management
- Provides repository implementations

### External Services

- Implements email sending functionality
- Handles file storage and media processing
- Integrates with payment gateways

### Security

- Implements JWT token generation and validation
- Provides password hashing and verification
- Manages role-based access control

## API Layer

### Controllers

#### UserController
- Handles user registration, authentication, login
- Manages user profiles and settings
- Provides user analytics and dashboard data

#### PropertyController
- Exposes endpoints for property CRUD operations
- Supports property search with filtering and pagination
- Handles property media uploads

#### WalletController
- Manages wallet operations (top-up, spend)
- Provides transaction history
- Handles balance inquiries

#### NotificationController
- Delivers user notifications
- Supports filtering by type and date
- Handles notification status management

#### BlogController
- Manages blog posts and comments
- Supports content creation and moderation
- Provides methods for retrieving and filtering content

### Authentication and Authorization

- JWT-based authentication
- Role-based authorization
- Custom authorization policies

### Middleware

- Exception handling middleware
- Request logging
- Performance monitoring

## Database Design

The system uses a relational database with the following key tables:

- **Users**: Stores user information and authentication data
- **Properties**: Contains property listings and details
- **PropertyMedia**: Stores images and videos for properties
- **Reviews**: Contains ratings and reviews for properties and owners
- **Wallets and Transactions**: Track financial operations
- **Notifications**: Stores user notifications
- **BlogPosts and Comments**: Contains blog content

## Feature Deep Dive

### 1. Property Management

The property management functionality allows:
- Creating property listings with detailed information
- Uploading and managing property images
- Setting property status (draft, active, expired)
- Searching and filtering properties based on multiple criteria
- Highlighting featured properties
- Managing property expiration and renewal

Implementation details:
- Properties have a complex lifecycle managed through status transitions
- Media files are processed for different sizes and watermarked
- Property searches support proximity-based results
- Advanced filtering options are available through the search API

### 2. User Management

The user management system handles:
- User registration and authentication
- Profile management
- Role-based permissions
- User favorites and history
- Membership ranking based on spending

Implementation details:
- Users can have multiple roles (buyer, owner, admin)
- JWT tokens are used for authentication
- Password hashing ensures security
- Member ranking is automatically updated based on spending

### 3. Wallet and Payment

The financial system includes:
- User wallets with balance tracking
- Transaction history
- Payment for listing properties
- Payment for premium features

Implementation details:
- Transactions are atomic operations
- Wallets are automatically created for new users
- Spending affects user membership ranking
- Transaction history is maintained for transparency

### 4. Notification System

The notification system provides:
- Various notification types (news, wallet updates, promotions, messages)
- Reading status tracking
- Filtering by type and date
- Notification preferences

Implementation details:
- System-wide notifications (news, promotions) are visible to all users
- User-specific notifications are private
- Pagination ensures performance with large notification volumes
- Read status is maintained per user

### 5. Blog System

The blog functionality supports:
- Article publishing and management
- Commenting and interaction
- Content filtering and searching

Implementation details:
- Rich text content storage and rendering
- Comment moderation capabilities
- SEO-friendly content structure

### 6. Review System

The review system allows:
- Property ratings and reviews
- Owner ratings and reviews
- Verification of reviewers
- Aggregation of ratings

Implementation details:
- Reviews require verified user accounts
- Rating aggregation is updated in real-time
- Protection against duplicate reviews

## API Documentation

The system exposes a comprehensive set of RESTful APIs:

### Property APIs
- Create, read, update, delete operations
- Search with filtering and pagination
- Media upload and management
- Status management

### User APIs
- Registration and authentication
- Profile management
- Dashboard analytics
- Favorites management

### Wallet APIs
- Balance inquiries
- Transaction operations
- Transaction history
- Spending analytics

### Notification APIs
- Retrieving notifications by type
- Marking notifications as read
- Filtering by date and type
- Counting unread notifications

### Blog APIs
- Article creation and management
- Comment submission and moderation
- Content filtering and pagination

## Conclusion

This Real Estate Management System is a comprehensive solution that follows modern architectural patterns and best practices. Its modular design allows for easy extension and maintenance, while the clean architecture ensures separation of concerns and testability.

The system provides all the necessary features for a complete real estate platform, including property management, user accounts, financial transactions, notifications, blog content, and reviews. The API-first approach enables integration with various client applications, making it a versatile solution for real estate businesses.

