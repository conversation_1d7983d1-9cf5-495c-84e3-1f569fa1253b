﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using System.Security.Claims;
using System.Text.Json;

namespace RealEstate.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;
        private readonly ILogger<AuthController> _logger;
        private readonly ITokenService _tokenService;
        private readonly IUserService _userService;
        private readonly IUserAvatarService _userAvatarService;
        private readonly IYezDataService _yezDataService;

        public AuthController(IAuthService authService, ILogger<AuthController> logger, ITokenService tokenService, IUserService userService, IUserAvatarService userAvatarService, IYezDataService yezDataService)
        {
            _authService = authService;
            _logger = logger;
            _tokenService = tokenService;
            _userService = userService;
            _userAvatarService = userAvatarService;
            _yezDataService = yezDataService;
        }

        [Authorize]
        [HttpGet("me")]
        public async Task<ActionResult<ProfileDto>> GetUserProfile()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (userId == null)
                {
                    return Unauthorized(new { Message = "User ID not found in token" });
                }

                var parsedUserId = Guid.Parse(userId);
                var userProfile = await _userService.GetUserByIdAsync(parsedUserId);

                // Get user avatar information
                var userAvatar = await _userAvatarService.GetUserAvatarByUserIdAsync(parsedUserId);

                // Add the avatar URL to the user profile
                if (userAvatar != null)
                {
                    userProfile.AvatarURL = userAvatar.MediaURL;
                }

                // Get member rank details and highlight fee based on user's rank
                var memberRankDetails = await _yezDataService.GetMemberRankingByNameAsync(userProfile.MemberRank);
                var highlightFee = await _yezDataService.GetHighlightFeeByRankAsync(userProfile.MemberRank);

                // Create a response object with user profile and additional information
                var response = new ProfileDto
                {
                    User = userProfile,
                    MemberRankDetails = memberRankDetails,
                    HighlightFee = highlightFee
                };

                return Ok(response);
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when retrieving user profile");
                return Unauthorized(new { Message = "An error occurred while processing your request. Please try again later." });
            }
        }

        [HttpPost("register")]
        [AllowAnonymous]
        public async Task<ActionResult<UserDto>> Register(CreateUserDto registerDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var user = await _authService.RegisterAsync(registerDto);
                return Ok(user);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when registering");
                return BadRequest(new { Message = "An error occurred during registration. Please try again later." });
            }
        }

        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<ActionResult<UserDto>> Login(LoginDto loginDto)
        {
            try
            {
                var user = await _authService.LoginAsync(loginDto);
                var refreshToken = _tokenService.GenerateRefreshToken();

                // Store Refresh Token in HttpOnly Secure Cookies
                Response.Cookies.Append("refreshToken", refreshToken, new CookieOptions
                {
                    HttpOnly = true,
                    Secure = true, // Enable on HTTPS
                    SameSite = SameSiteMode.Strict,
                    Expires = DateTime.UtcNow.AddDays(7)
                });

                return Ok(user);
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when logging in");
                return Unauthorized(new { Message = "An error occurred during login. Please try again later." });
            }
        }

        [HttpPost("reset-password")]
        [AllowAnonymous]
        public async Task<ActionResult<UserDto>> ResetPassword(ForgotPasswordDto resetPasswordDto)
        {
            try
            {
                return Ok(new { Message = "TODO: waiting to implement sending email" });
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when logging in");
                return Unauthorized(new { Message = "An error occurred during password reset. Please try again later." });
            }
        }

        [Authorize]
        [HttpPatch("/api/[controller]/me/password")]
        public async Task<ActionResult<UserDto>> Password(ChangePasswordDto changePasswordDto)
        {
            try
            {
                var user = await _authService.ChangePassword(changePasswordDto);
                user.Token = string.Empty;
                return Ok(user);
            }
            catch (UnauthorizedAccessException ex)
            {
                return BadRequest(new { Message = ex.Message, ErrorType = "invalid_credentials" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when changing password");
                return StatusCode(500, new { Message = "An error occurred while processing your request. Please try again later." });
            }
        }

        [HttpPost("refresh-token")]
        public IActionResult RefreshToken()
        {
            var refreshToken = Request.Cookies["refreshToken"];
            if (string.IsNullOrEmpty(refreshToken))
                return Unauthorized(new { message = "No refresh token found" });

            // Validate token (typically refresh tokens would be stored in DB for validation)

            // Get the current user's claims
            var claimsIdentity = User.Identity as ClaimsIdentity;
            if (claimsIdentity == null)
            {
                return Unauthorized();
            }

            var userId = claimsIdentity.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (userId == null)
            {
                return Unauthorized();
            }

            var user = _authService.RefreshToken(Guid.Parse(userId));
            return Ok(user);
        }

        [HttpGet("validate-token")]
        [Authorize]
        public async Task<IActionResult> ValidateToken()
        {
            // TODO: need to implement check the token is retrieve or not
            return Ok(new { Message = "token valid" });
        }
    }
}
