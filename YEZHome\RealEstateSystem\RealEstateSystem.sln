﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.11.35327.3
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RealEstate.API", "RealEstate.API\RealEstate.API.csproj", "{BD366428-0752-44CC-A758-691E68C3B0E7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RealEstate.Application", "RealEstate.Application\RealEstate.Application.csproj", "{213F1D4C-2FBB-4F10-8169-8E0EB3F20373}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RealEstate.Domain", "RealEstate.Domain\RealEstate.Domain.csproj", "{857AFBD0-9540-4413-A1C7-E60DC278FC21}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RealEstate.Infrastructure", "RealEstate.Infrastructure\RealEstate.Infrastructure.csproj", "{C37407F6-205B-40CE-B96B-2B6B6E5273E7}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{A7E25AAD-E23D-4429-A990-F22B25B78A0C}"
	ProjectSection(SolutionItems) = preProject
		AppUser_Alter.sql = AppUser_Alter.sql
		AppUser_Invoice_Alter.sql = AppUser_Invoice_Alter.sql
		DB_Script.sql = DB_Script.sql
		PropertyAnalytics_Schema.sql = PropertyAnalytics_Schema.sql
		readme.md = readme.md
		TranferCodeSequence_Alter.sql = TranferCodeSequence_Alter.sql
		UserAvatar_Table.sql = UserAvatar_Table.sql
		WalletTransaction_Alter.sql = WalletTransaction_Alter.sql
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ImportDataCityLocation", "ImportDataCityLocation\ImportDataCityLocation.csproj", "{E9C5499B-EAD4-4DF6-8645-B34ECC9B1C39}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{BD366428-0752-44CC-A758-691E68C3B0E7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BD366428-0752-44CC-A758-691E68C3B0E7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BD366428-0752-44CC-A758-691E68C3B0E7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BD366428-0752-44CC-A758-691E68C3B0E7}.Release|Any CPU.Build.0 = Release|Any CPU
		{213F1D4C-2FBB-4F10-8169-8E0EB3F20373}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{213F1D4C-2FBB-4F10-8169-8E0EB3F20373}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{213F1D4C-2FBB-4F10-8169-8E0EB3F20373}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{213F1D4C-2FBB-4F10-8169-8E0EB3F20373}.Release|Any CPU.Build.0 = Release|Any CPU
		{857AFBD0-9540-4413-A1C7-E60DC278FC21}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{857AFBD0-9540-4413-A1C7-E60DC278FC21}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{857AFBD0-9540-4413-A1C7-E60DC278FC21}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{857AFBD0-9540-4413-A1C7-E60DC278FC21}.Release|Any CPU.Build.0 = Release|Any CPU
		{C37407F6-205B-40CE-B96B-2B6B6E5273E7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C37407F6-205B-40CE-B96B-2B6B6E5273E7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C37407F6-205B-40CE-B96B-2B6B6E5273E7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C37407F6-205B-40CE-B96B-2B6B6E5273E7}.Release|Any CPU.Build.0 = Release|Any CPU
		{E9C5499B-EAD4-4DF6-8645-B34ECC9B1C39}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E9C5499B-EAD4-4DF6-8645-B34ECC9B1C39}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E9C5499B-EAD4-4DF6-8645-B34ECC9B1C39}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E9C5499B-EAD4-4DF6-8645-B34ECC9B1C39}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {7BC8C955-A36A-47B5-A3EA-026729258B19}
	EndGlobalSection
EndGlobal
