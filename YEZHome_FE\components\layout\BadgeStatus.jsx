import { PropertyStatus } from "@/lib/enum";
import { Badge } from "../ui/badge";
import { cn } from "@/lib/utils";

const statusColors = {
  [PropertyStatus.DRAFT]: "bg-gray-100 text-gray-700 hover:bg-gray-100",
  [PropertyStatus.SOLD]: "bg-sky-100 text-sky-700 hover:bg-sky-100",
  [PropertyStatus.EXPIRED]: "bg-amber-100 text-amber-700 hover:bg-amber-100",
  [PropertyStatus.APPROVED]: "bg-emerald-100 text-emerald-700 hover:bg-emerald-100",
  [PropertyStatus.PENDING_APPROVAL]: "bg-yellow-100 text-yellow-700 hover:bg-yellow-100",
  [PropertyStatus.REJECTED_BY_ADMIN]: "bg-rose-100 text-rose-700 hover:bg-rose-100",
  [PropertyStatus.REJECTED_DUE_TO_UNPAID]: "bg-red-100 text-red-700 hover:bg-red-100",
  [PropertyStatus.WAITING_PAYMENT]: "bg-orange-100 text-orange-700 hover:bg-orange-100",
};

export default function BadgeStatus({ className, status, statusText }) {
  return (
    <Badge
      className={cn(
        statusColors[status] || "bg-gray-200 text-gray-800",
        "font-medium rounded-md px-2 py-1 text-sm mt-3",
        className
      )}
    >
      {statusText || status}
    </Badge>
  );
}
