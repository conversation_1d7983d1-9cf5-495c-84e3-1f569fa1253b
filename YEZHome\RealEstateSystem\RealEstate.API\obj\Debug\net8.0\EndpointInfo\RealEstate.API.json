{"openapi": "3.0.1", "info": {"title": "API", "version": "v1"}, "paths": {"/api/Address/cities": {"get": {"tags": ["Address"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/City"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/City"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/City"}}}}}}}}, "/api/Address/cities/{cityId}/districts": {"get": {"tags": ["Address"], "parameters": [{"name": "cityId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/District"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/District"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/District"}}}}}}}}, "/api/Address/districts/{districtId}/wards": {"get": {"tags": ["Address"], "parameters": [{"name": "districtId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Ward"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Ward"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Ward"}}}}}}}}, "/api/Address/districts/{districtId}/streets": {"get": {"tags": ["Address"], "parameters": [{"name": "districtId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Street"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Street"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Street"}}}}}}}}, "/api/Address/wards/{wardId}/streets/{streetId}/projects": {"get": {"tags": ["Address"], "parameters": [{"name": "wardId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "streetId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Project"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Project"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Project"}}}}}}}}, "/api/Auth/me": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProfileDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProfileDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProfileDto"}}}}}}}, "/api/Auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}}}}}}, "/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}}}}}}, "/api/Auth/reset-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}}}}}}, "/api/Auth/me/password": {"patch": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}}}}}}, "/api/Auth/refresh-token": {"post": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/Auth/validate-token": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/Blog/{id}": {"get": {"tags": ["Blog"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlogPostDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlogPostDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlogPostDto"}}}}}}, "put": {"tags": ["Blog"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBlogPostDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateBlogPostDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateBlogPostDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Blog"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Blog/slug/{slug}": {"get": {"tags": ["Blog"], "parameters": [{"name": "slug", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlogPostDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlogPostDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlogPostDto"}}}}}}}, "/api/Blog": {"get": {"tags": ["Blog"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlogPostDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlogPostDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BlogPostDto"}}}}}}}, "post": {"tags": ["Blog"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBlogPostDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateBlogPostDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateBlogPostDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BlogPostDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BlogPostDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BlogPostDto"}}}}}}}, "/api/Blog/blog-posts": {"get": {"tags": ["Blog"], "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SortColumn", "in": "query", "schema": {"type": "string"}}, {"name": "SortDescending", "in": "query", "schema": {"type": "boolean"}}, {"name": "title", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ContactRequest/{id}": {"get": {"tags": ["ContactRequest"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContactRequestDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContactRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContactRequestDto"}}}}}}, "put": {"tags": ["ContactRequest"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateContactRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateContactRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateContactRequestDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["ContactRequest"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ContactRequest": {"get": {"tags": ["ContactRequest"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ContactRequestDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ContactRequestDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ContactRequestDto"}}}}}}}, "post": {"tags": ["ContactRequest"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateContactRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateContactRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateContactRequestDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContactRequestDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContactRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContactRequestDto"}}}}}}}, "/api/ContactRequest/requests": {"get": {"tags": ["ContactRequest"], "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SortColumn", "in": "query", "schema": {"type": "string"}}, {"name": "SortDescending", "in": "query", "schema": {"type": "boolean"}}, {"name": "phone", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ContactRequest/property/{propertyId}": {"get": {"tags": ["ContactRequest"], "parameters": [{"name": "propertyId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ContactRequestDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ContactRequestDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ContactRequestDto"}}}}}}}}, "/Media/{fileId}": {"get": {"tags": ["Media"], "parameters": [{"name": "fileId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "size", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/Media/update-caption": {"put": {"tags": ["Media"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePropertyMediaCaptionDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdatePropertyMediaCaptionDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdatePropertyMediaCaptionDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/Media/update-is-avatar": {"put": {"tags": ["Media"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePropertyMediaIsAvatarDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdatePropertyMediaIsAvatarDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdatePropertyMediaIsAvatarDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/Media/{id}": {"delete": {"tags": ["Media"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/notifications": {"get": {"tags": ["Notification"], "parameters": [{"name": "fromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "toDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/NotificationDtoPagedResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/NotificationDtoPagedResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NotificationDtoPagedResultDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/notifications/{id}": {"get": {"tags": ["Notification"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/NotificationDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/NotificationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NotificationDto"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}, "delete": {"tags": ["Notification"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/notifications/by-type/{type}": {"get": {"tags": ["Notification"], "parameters": [{"name": "type", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "fromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "toDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/NotificationDtoPagedResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/NotificationDtoPagedResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NotificationDtoPagedResultDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/notifications/by-type/{type}/user/{userId}": {"get": {"tags": ["Notification"], "parameters": [{"name": "type", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "fromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "toDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/NotificationDtoPagedResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/NotificationDtoPagedResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NotificationDtoPagedResultDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/notifications/for-user": {"get": {"tags": ["Notification"], "parameters": [{"name": "fromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "toDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/NotificationDtoPagedResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/NotificationDtoPagedResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NotificationDtoPagedResultDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/notifications/{id}/mark-as-read": {"put": {"tags": ["Notification"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/notifications/mark-all-as-read": {"put": {"tags": ["Notification"], "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/notifications/unread-count": {"get": {"tags": ["Notification"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UnreadNotificationCountDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UnreadNotificationCountDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UnreadNotificationCountDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/notifications/mark-multiple-as-read": {"put": {"tags": ["Notification"], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string", "format": "uuid"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string", "format": "uuid"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string", "format": "uuid"}}}}}, "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/notification-preferences": {"get": {"tags": ["NotificationPreference"], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["NotificationPreference"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationPreferenceDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NotificationPreferenceDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/NotificationPreferenceDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Property/{propertyId}": {"get": {"tags": ["Property"], "parameters": [{"name": "propertyId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PropertyDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PropertyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PropertyDto"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}, "put": {"tags": ["Property"], "parameters": [{"name": "propertyId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePropertyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreatePropertyDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreatePropertyDto"}}}}, "responses": {"201": {"description": "Created"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}, "delete": {"tags": ["Property"], "parameters": [{"name": "propertyId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"204": {"description": "No Content"}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Property": {"get": {"tags": ["Property"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyDto"}}}}}}}, "post": {"tags": ["Property"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePropertyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreatePropertyDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreatePropertyDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PropertyDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PropertyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PropertyDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Property/bulk": {"delete": {"tags": ["Property"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkPropertyIdsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkPropertyIdsDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BulkPropertyIdsDto"}}}}, "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Property/{propertyId}/status": {"put": {"tags": ["Property"], "parameters": [{"name": "propertyId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStatusDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateStatusDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateStatusDto"}}}}, "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Property/bulk/status": {"put": {"tags": ["Property"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateStatusDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateStatusDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BulkUpdateStatusDto"}}}}, "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Property/{propertyId}/highlight": {"put": {"tags": ["Property"], "parameters": [{"name": "propertyId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateHighlightDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateHighlightDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateHighlightDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PropertyDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PropertyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PropertyDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Property/bulk/highlight": {"put": {"tags": ["Property"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateHighlightDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateHighlightDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BulkUpdateHighlightDto"}}}}, "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Property/me": {"get": {"tags": ["Property"], "parameters": [{"name": "status", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PropertyDtoPagedResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PropertyDtoPagedResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PropertyDtoPagedResultDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Property/upload-images": {"post": {"tags": ["Property"], "parameters": [{"name": "propertyId", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "string", "format": "binary"}}}}, "encoding": {"files": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyMediaDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyMediaDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyMediaDto"}}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Property/edit-remaining/{propertyId}": {"get": {"tags": ["Property"], "parameters": [{"name": "propertyId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyDto"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Property/stats": {"get": {"tags": ["Property"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PropertyCountStatsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PropertyCountStatsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PropertyCountStatsDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Property/search": {"get": {"tags": ["Property"], "parameters": [{"name": "postType", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "propertyType", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "cityId", "in": "query", "schema": {"type": "string"}}, {"name": "districtId", "in": "query", "schema": {"type": "string"}}, {"name": "address", "in": "query", "schema": {"type": "string"}}, {"name": "minPrice", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "maxPrice", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "minArea", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "maxArea", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "minRooms", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "minToilets", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "direction", "in": "query", "schema": {"type": "string"}}, {"name": "legality", "in": "query", "schema": {"type": "string"}}, {"name": "minRoadWidth", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "swLat", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "swLng", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "neLat", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "neLng", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PropertyDtoPagedResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PropertyDtoPagedResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PropertyDtoPagedResultDto"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/Property/nearby": {"get": {"tags": ["Property"], "parameters": [{"name": "swLat", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "swLng", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "neLat", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "neLng", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PropertyDtoPagedResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PropertyDtoPagedResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PropertyDtoPagedResultDto"}}}}, "500": {"description": "Internal Server Error"}}}}, "/api/Property/renew": {"post": {"tags": ["Property"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PropertyRenewalDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PropertyRenewalDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PropertyRenewalDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PropertyDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PropertyDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PropertyDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/PropertyAnalytics/property/{propertyId}": {"get": {"tags": ["PropertyAnalytics"], "parameters": [{"name": "propertyId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PropertyAnalyticsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PropertyAnalyticsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PropertyAnalyticsDto"}}}}}}}, "/api/PropertyAnalytics/user": {"get": {"tags": ["PropertyAnalytics"], "parameters": [{"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PropertyStatuses", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "SpendingTypes", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "MinSpent", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "MaxSpent", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "MinViews", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "MaxViews", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortDescending", "in": "query", "schema": {"type": "boolean"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PropertyAnalyticsDtoPagedResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PropertyAnalyticsDtoPagedResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PropertyAnalyticsDtoPagedResultDto"}}}}}}}, "/api/PropertyAnalytics/property/{propertyId}/export": {"get": {"tags": ["PropertyAnalytics"], "parameters": [{"name": "propertyId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK"}}}}, "/api/PropertyAnalytics/user/export": {"get": {"tags": ["PropertyAnalytics"], "parameters": [{"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PropertyStatuses", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "SpendingTypes", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "MinSpent", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "MaxSpent", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "MinViews", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "MaxViews", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortDescending", "in": "query", "schema": {"type": "boolean"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/PropertyAnalytics/property/{propertyId}/view": {"post": {"tags": ["PropertyAnalytics"], "parameters": [{"name": "propertyId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/PropertyAnalytics/history/status/{propertyId}": {"get": {"tags": ["PropertyAnalytics"], "parameters": [{"name": "propertyId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyStatusLogDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyStatusLogDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyStatusLogDto"}}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/User/role": {"put": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddUserRoleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddUserRoleDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddUserRoleDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AddUserRoleDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AddUserRoleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddUserRoleDto"}}}}}}}, "/api/User/{id}": {"get": {"tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/User/dashboard": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDashboardDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDashboardDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDashboardDto"}}}}}}}, "/api/User/dashboard/{userId}": {"get": {"tags": ["User"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDashboardDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDashboardDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDashboardDto"}}}}}}}, "/api/User/wallet": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WalletInfoDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WalletInfoDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WalletInfoDto"}}}}}}}, "/api/User/properties/stats": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PropertyStatsDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PropertyStatsDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PropertyStatsDto"}}}}}}}, "/api/User/transactions": {"get": {"tags": ["User"], "parameters": [{"name": "count", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WalletTransactionDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WalletTransactionDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WalletTransactionDto"}}}}}}}}, "/api/User/ranking": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MemberRankingDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MemberRankingDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MemberRankingDto"}}}}}}}, "/api/User/spending/monthly": {"get": {"tags": ["User"], "parameters": [{"name": "year", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/User/properties/performance": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK"}}}}, "/api/User/deactivate": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeactivateUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeactivateUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeactivateUserDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/User/reactivate/{userId}": {"post": {"tags": ["User"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/User/permanent-delete": {"delete": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeactivateUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeactivateUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeactivateUserDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/User/tax-info": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserInvoiceInfoDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserInvoiceInfoDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserInvoiceInfoDto"}}}}}}, "put": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserTaxInfoDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserTaxInfoDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserTaxInfoDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/User/{id}/invoice-info": {"get": {"tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserInvoiceInfoDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserInvoiceInfoDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserInvoiceInfoDto"}}}}}}}, "/api/UserAvatar/upload": {"post": {"tags": ["UserAvatar"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}}}}}}, "/api/UserAvatar": {"get": {"tags": ["UserAvatar"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserAvatarDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserAvatarDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserAvatarDto"}}}}}}, "delete": {"tags": ["UserAvatar"], "responses": {"200": {"description": "OK"}}}}, "/UserAvatarMedia/{fileId}": {"get": {"tags": ["UserAvatarMedia"], "parameters": [{"name": "fileId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "size", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/UserFavorites/favorites": {"get": {"tags": ["UserFavorites"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserFavoriteDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserFavoriteDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserFavoriteDto"}}}}}}}}, "/api/UserFavorites/add": {"post": {"tags": ["UserFavorites"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserFavoriteDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUserFavoriteDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUserFavoriteDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/UserFavorites/remove/{propertyId}": {"delete": {"tags": ["UserFavorites"], "parameters": [{"name": "propertyId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/UserFavorites/check": {"post": {"tags": ["UserFavorites"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FavoriteCheckRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FavoriteCheckRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FavoriteCheckRequestDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FavoriteStatusDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FavoriteStatusDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FavoriteStatusDto"}}}}}}}}, "/api/UserFavorites/count": {"get": {"tags": ["UserFavorites"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FavoriteCountDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FavoriteCountDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FavoriteCountDto"}}}}}}}, "/api/WalletTransaction": {"get": {"tags": ["WalletTransaction"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WalletTransactionDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WalletTransactionDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WalletTransactionDto"}}}}}}}}, "/api/WalletTransaction/balance": {"get": {"tags": ["WalletTransaction"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WalletBalanceDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WalletBalanceDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WalletBalanceDto"}}}}}}}, "/api/WalletTransaction/topup": {"post": {"tags": ["WalletTransaction"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TopUpWalletDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TopUpWalletDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TopUpWalletDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WalletTransactionDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WalletTransactionDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WalletTransactionDto"}}}}}}}, "/api/WalletTransaction/spend": {"post": {"tags": ["WalletTransaction"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SpendWalletDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SpendWalletDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SpendWalletDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WalletTransactionDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WalletTransactionDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WalletTransactionDto"}}}}}}}, "/api/WalletTransaction/{id}": {"get": {"tags": ["WalletTransaction"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WalletTransactionDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WalletTransactionDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WalletTransactionDto"}}}}}}}, "/api/WalletTransaction/admin/user/{userId}": {"get": {"tags": ["WalletTransaction"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WalletTransactionDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WalletTransactionDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WalletTransactionDto"}}}}}}}}, "/api/WalletTransaction/pending": {"get": {"tags": ["WalletTransaction"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WalletTransactionDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WalletTransactionDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WalletTransactionDto"}}}}}}}}, "/api/WalletTransaction/admin/pending": {"get": {"tags": ["WalletTransaction"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WalletTransactionDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WalletTransactionDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WalletTransactionDto"}}}}}}}}, "/api/WalletTransaction/search": {"get": {"tags": ["WalletTransaction"], "parameters": [{"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "status", "in": "query", "schema": {"type": "string"}}, {"name": "paymentMethod", "in": "query", "schema": {"type": "string"}}, {"name": "type", "in": "query", "schema": {"type": "string"}}, {"name": "minAmount", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "maxAmount", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransactionSearchResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransactionSearchResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransactionSearchResultDto"}}}}}}}, "/api/WalletTransaction/admin/search": {"get": {"tags": ["WalletTransaction"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "status", "in": "query", "schema": {"type": "string"}}, {"name": "paymentMethod", "in": "query", "schema": {"type": "string"}}, {"name": "type", "in": "query", "schema": {"type": "string"}}, {"name": "minAmount", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "maxAmount", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransactionSearchResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransactionSearchResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransactionSearchResultDto"}}}}}}}, "/api/WalletTransaction/export": {"get": {"tags": ["WalletTransaction"], "parameters": [{"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "status", "in": "query", "schema": {"type": "string"}}, {"name": "paymentMethod", "in": "query", "schema": {"type": "string"}}, {"name": "type", "in": "query", "schema": {"type": "string"}}, {"name": "minAmount", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "maxAmount", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/WalletTransaction/admin/export": {"get": {"tags": ["WalletTransaction"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "status", "in": "query", "schema": {"type": "string"}}, {"name": "paymentMethod", "in": "query", "schema": {"type": "string"}}, {"name": "type", "in": "query", "schema": {"type": "string"}}, {"name": "minAmount", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "maxAmount", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "searchTerm", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/WalletTransaction/{id}/process": {"post": {"tags": ["WalletTransaction"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessTransactionDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProcessTransactionDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProcessTransactionDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WalletTransactionDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WalletTransactionDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WalletTransactionDto"}}}}}}}, "/api/YezData/highlight-fees": {"get": {"tags": ["YezData"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/HighlightFeeDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/HighlightFeeDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/HighlightFeeDto"}}}}}}}}, "/api/YezData/highlight-fees/{rankName}": {"get": {"tags": ["YezData"], "parameters": [{"name": "rankName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/HighlightFeeDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/HighlightFeeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HighlightFeeDto"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/YezData/member-rankings": {"get": {"tags": ["YezData"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MemberRankListDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MemberRankListDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MemberRankListDto"}}}}}}}}, "/api/YezData/member-rankings/{rankName}": {"get": {"tags": ["YezData"], "parameters": [{"name": "rankName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MemberRankListDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MemberRankListDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MemberRankListDto"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}}, "components": {"schemas": {"AddUserRoleDto": {"type": "object", "properties": {"userId": {"type": "string", "format": "uuid"}, "roleId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "BlogCommentDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "commentText": {"type": "string", "nullable": true}, "userId": {"type": "string", "format": "uuid"}, "postId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "BlogPostDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "authorID": {"type": "string", "format": "uuid"}, "authorName": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "content": {"type": "string", "nullable": true}, "featuredImage": {"type": "string", "nullable": true}, "tags": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "isFeature": {"type": "boolean"}, "publishedAt": {"type": "string", "format": "date-time", "nullable": true}, "blogComments": {"type": "array", "items": {"$ref": "#/components/schemas/BlogCommentDto"}, "nullable": true}}, "additionalProperties": false}, "BulkPropertyIdsDto": {"required": ["propertyIds"], "type": "object", "properties": {"propertyIds": {"type": "array", "items": {"type": "string", "format": "uuid"}}}, "additionalProperties": false}, "BulkUpdateHighlightDto": {"required": ["isHighlighted", "propertyIds"], "type": "object", "properties": {"propertyIds": {"type": "array", "items": {"type": "string", "format": "uuid"}}, "isHighlighted": {"type": "boolean"}}, "additionalProperties": false}, "BulkUpdateStatusDto": {"required": ["propertyIds", "status"], "type": "object", "properties": {"propertyIds": {"type": "array", "items": {"type": "string", "format": "uuid"}}, "status": {"minLength": 1, "type": "string"}, "comment": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ChangePasswordDto": {"required": ["email", "newPassword", "oldPassword"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string"}, "oldPassword": {"minLength": 1, "type": "string"}, "newPassword": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "City": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "nameWithType": {"type": "string", "nullable": true}, "districts": {"type": "array", "items": {"$ref": "#/components/schemas/District"}, "nullable": true}}, "additionalProperties": false}, "ContactRequestDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "sentAt": {"type": "string", "format": "date-time"}, "propertyId": {"type": "string", "format": "uuid"}, "agentId": {"type": "string", "format": "uuid"}, "userId": {"type": "string", "format": "uuid", "nullable": true}, "note": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateBlogPostDto": {"required": ["authorID", "content", "title"], "type": "object", "properties": {"authorID": {"type": "string", "format": "uuid"}, "title": {"maxLength": 100, "minLength": 0, "type": "string"}, "content": {"minLength": 1, "type": "string"}, "featuredImage": {"type": "string", "nullable": true}, "tags": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "publishedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "CreateContactRequestDto": {"required": ["agentId", "email", "name", "phone", "propertyId"], "type": "object", "properties": {"name": {"maxLength": 255, "minLength": 1, "type": "string"}, "email": {"maxLength": 255, "minLength": 1, "type": "string", "format": "email"}, "phone": {"maxLength": 20, "minLength": 1, "type": "string"}, "propertyId": {"type": "string", "format": "uuid"}, "agentId": {"type": "string", "format": "uuid"}, "userId": {"type": "string", "format": "uuid", "nullable": true}, "note": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreatePropertyDto": {"required": ["address", "cityId", "description", "districtId", "name", "ownerId", "postType", "price", "propertyType", "wardId"], "type": "object", "properties": {"ownerId": {"type": "string", "format": "uuid"}, "name": {"minLength": 1, "type": "string"}, "propertyType": {"minLength": 1, "type": "string"}, "postType": {"minLength": 1, "type": "string"}, "cityId": {"type": "integer", "format": "int32"}, "districtId": {"type": "integer", "format": "int32"}, "streetId": {"type": "integer", "format": "int32", "nullable": true}, "wardId": {"type": "integer", "format": "int32"}, "address": {"minLength": 1, "type": "string"}, "area": {"type": "number", "format": "double", "nullable": true}, "price": {"type": "number", "format": "double"}, "latitude": {"type": "number", "format": "double"}, "longitude": {"type": "number", "format": "double"}, "videoUrl": {"type": "string", "nullable": true}, "floors": {"type": "integer", "format": "int32", "nullable": true}, "rooms": {"type": "integer", "format": "int32", "nullable": true}, "toilets": {"type": "integer", "format": "int32", "nullable": true}, "direction": {"type": "string", "nullable": true}, "balconyDirection": {"type": "string", "nullable": true}, "legality": {"type": "string", "nullable": true}, "interior": {"type": "string", "nullable": true}, "width": {"type": "integer", "format": "int32", "nullable": true}, "roadWidth": {"type": "integer", "format": "int32", "nullable": true}, "description": {"minLength": 1, "type": "string"}, "overview": {"type": "string", "nullable": true}, "placeData": {"type": "string", "nullable": true}, "policies": {"type": "string", "nullable": true}, "neighborhood": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "isHighlighted": {"type": "boolean", "nullable": true}, "isAutoRenew": {"type": "boolean", "nullable": true}, "uploadedFiles": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyMediaDto"}, "nullable": true}}, "additionalProperties": false}, "CreateUserDto": {"required": ["email", "fullName", "password", "phone", "userType"], "type": "object", "properties": {"fullName": {"minLength": 1, "type": "string"}, "email": {"minLength": 1, "type": "string"}, "password": {"minLength": 1, "type": "string"}, "userType": {"minLength": 1, "type": "string"}, "phone": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "CreateUserFavoriteDto": {"type": "object", "properties": {"propertyId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "DailySpendingDto": {"type": "object", "properties": {"date": {"type": "string", "format": "date-time"}, "amount": {"type": "number", "format": "double"}, "spendingType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DailyViewsDto": {"type": "object", "properties": {"date": {"type": "string", "format": "date-time"}, "views": {"type": "integer", "format": "int32"}, "uniqueViews": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "DeactivateUserDto": {"required": ["password"], "type": "object", "properties": {"password": {"minLength": 1, "type": "string"}, "reason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "District": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "nameWithType": {"type": "string", "nullable": true}, "path": {"type": "string", "nullable": true}, "pathWithType": {"type": "string", "nullable": true}, "cityId": {"type": "integer", "format": "int32"}, "city": {"$ref": "#/components/schemas/City"}, "wards": {"type": "array", "items": {"$ref": "#/components/schemas/Ward"}, "nullable": true}, "streets": {"type": "array", "items": {"$ref": "#/components/schemas/Street"}, "nullable": true}}, "additionalProperties": false}, "FavoriteCheckRequestDto": {"type": "object", "properties": {"propertyIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}}, "additionalProperties": false}, "FavoriteCountDto": {"type": "object", "properties": {"count": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "FavoriteStatusDto": {"type": "object", "properties": {"propertyId": {"type": "string", "format": "uuid"}, "isFavorite": {"type": "boolean"}}, "additionalProperties": false}, "ForgotPasswordDto": {"required": ["email"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "HighlightFeeDto": {"type": "object", "properties": {"rankName": {"type": "string", "nullable": true}, "fee": {"type": "number", "format": "double"}}, "additionalProperties": false}, "LoginDto": {"required": ["email", "password"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string"}, "password": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "MemberRankListDto": {"type": "object", "properties": {"rankName": {"type": "string", "nullable": true}, "minSpent": {"type": "number", "format": "double", "nullable": true}, "maxSpent": {"type": "number", "format": "double", "nullable": true}, "highlightFee": {"type": "number", "format": "double"}}, "additionalProperties": false}, "MemberRankingDto": {"type": "object", "properties": {"currentRank": {"type": "string", "nullable": true}, "nextRank": {"type": "string", "nullable": true}, "spendingToNextRank": {"type": "number", "format": "double", "nullable": true}, "minSpent": {"type": "number", "format": "double", "nullable": true}, "maxSpent": {"type": "number", "format": "double", "nullable": true}, "progressPercentage": {"type": "number", "format": "double"}}, "additionalProperties": false}, "NotificationDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userId": {"type": "string", "format": "uuid", "nullable": true}, "type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "isRead": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "NotificationDtoPagedResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "currentPage": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "NotificationPreferenceDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userId": {"type": "string", "format": "uuid"}, "receivePromotions": {"type": "boolean"}, "receiveWalletUpdates": {"type": "boolean"}, "receiveNews": {"type": "boolean"}, "receiveCustomerMessages": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}, "ProcessTransactionDto": {"required": ["action", "transactionId"], "type": "object", "properties": {"transactionId": {"type": "string", "format": "uuid"}, "action": {"minLength": 1, "type": "string"}, "failureReason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProfileDto": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/UserDto"}, "memberRankDetails": {"$ref": "#/components/schemas/MemberRankListDto"}, "highlightFee": {"$ref": "#/components/schemas/HighlightFeeDto"}}, "additionalProperties": false}, "Project": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "projectName": {"type": "string", "nullable": true}, "wardId": {"type": "integer", "format": "int32", "nullable": true}, "streetId": {"type": "integer", "format": "int32", "nullable": true}, "ward": {"$ref": "#/components/schemas/Ward"}, "street": {"$ref": "#/components/schemas/Street"}}, "additionalProperties": false}, "PropertyAnalyticsDto": {"type": "object", "properties": {"propertyId": {"type": "string", "format": "uuid"}, "propertyTitle": {"type": "string", "nullable": true}, "propertyStatus": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "expiresAt": {"type": "string", "format": "date-time", "nullable": true}, "totalViews": {"type": "integer", "format": "int32"}, "uniqueViews": {"type": "integer", "format": "int32"}, "totalFavorites": {"type": "integer", "format": "int32"}, "totalSpent": {"type": "number", "format": "double"}, "extensionSpent": {"type": "number", "format": "double"}, "highlightSpent": {"type": "number", "format": "double"}, "viewsTrend": {"type": "array", "items": {"$ref": "#/components/schemas/DailyViewsDto"}, "nullable": true}, "spendingTrend": {"type": "array", "items": {"$ref": "#/components/schemas/DailySpendingDto"}, "nullable": true}}, "additionalProperties": false}, "PropertyAnalyticsDtoPagedResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyAnalyticsDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "currentPage": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPrevious": {"type": "boolean", "readOnly": true}, "hasNext": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "PropertyCountStatsDto": {"type": "object", "properties": {"totalProperties": {"type": "integer", "format": "int32"}, "propertiesByStatus": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "PropertyDto": {"required": ["address", "description", "name", "price"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "propertyType": {"type": "string", "nullable": true}, "postType": {"type": "string", "nullable": true}, "cityId": {"type": "integer", "format": "int32", "nullable": true}, "districtId": {"type": "integer", "format": "int32", "nullable": true}, "streetId": {"type": "integer", "format": "int32", "nullable": true}, "wardId": {"type": "integer", "format": "int32", "nullable": true}, "address": {"type": "string", "nullable": true}, "area": {"type": "number", "format": "double", "nullable": true}, "price": {"type": "number", "format": "double"}, "videoUrl": {"type": "string", "nullable": true}, "floors": {"type": "integer", "format": "int32", "nullable": true}, "rooms": {"type": "integer", "format": "int32", "nullable": true}, "toilets": {"type": "integer", "format": "int32", "nullable": true}, "direction": {"type": "string", "nullable": true}, "balconyDirection": {"type": "string", "nullable": true}, "legality": {"type": "string", "nullable": true}, "interior": {"type": "string", "nullable": true}, "width": {"type": "integer", "format": "int32", "nullable": true}, "roadWidth": {"type": "integer", "format": "int32", "nullable": true}, "description": {"type": "string", "nullable": true}, "overview": {"type": "string", "nullable": true}, "placeData": {"type": "string", "nullable": true}, "policies": {"type": "string", "nullable": true}, "neighborhood": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "postPrice": {"type": "number", "format": "double"}, "isHighlighted": {"type": "boolean"}, "isAutoRenew": {"type": "boolean"}, "expiresAt": {"type": "string", "format": "date-time"}, "updateRemainingTimes": {"type": "integer", "format": "int32", "nullable": true}, "propertyMedia": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyMediaDto"}, "nullable": true}, "propertyReviews": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyReviewDto"}, "nullable": true}, "ownerId": {"type": "string", "format": "uuid"}, "owner": {"$ref": "#/components/schemas/UserDto"}, "createdAt": {"type": "string", "format": "date-time"}, "swLat": {"type": "number", "format": "double", "nullable": true}, "swLng": {"type": "number", "format": "double", "nullable": true}, "neLat": {"type": "number", "format": "double", "nullable": true}, "neLng": {"type": "number", "format": "double", "nullable": true}, "latitude": {"type": "number", "format": "double", "nullable": true}, "longitude": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "PropertyDtoPagedResultDto": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "currentPage": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "PropertyMediaDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "propertyID": {"type": "string", "format": "uuid", "nullable": true}, "mediaType": {"type": "string", "nullable": true}, "mediaURL": {"type": "string", "nullable": true}, "thumbnailURL": {"type": "string", "nullable": true}, "smallURL": {"type": "string", "nullable": true}, "mediumURL": {"type": "string", "nullable": true}, "largeURL": {"type": "string", "nullable": true}, "filePath": {"type": "string", "nullable": true}, "uploadedAt": {"type": "string", "format": "date-time"}, "caption": {"type": "string", "nullable": true}, "isAvatar": {"type": "boolean"}}, "additionalProperties": false}, "PropertyRenewalDto": {"required": ["durationInDays", "propertyId"], "type": "object", "properties": {"propertyId": {"type": "string", "format": "uuid"}, "durationInDays": {"maximum": 90, "minimum": 10, "type": "integer", "format": "int32"}}, "additionalProperties": false}, "PropertyReviewDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "rating": {"type": "integer", "format": "int32"}, "reviewText": {"type": "string", "nullable": true}, "buyerId": {"type": "string", "format": "uuid"}, "propertyId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "PropertyStatsDto": {"type": "object", "properties": {"totalProperties": {"type": "integer", "format": "int32"}, "activeProperties": {"type": "integer", "format": "int32"}, "expiredProperties": {"type": "integer", "format": "int32"}, "draftProperties": {"type": "integer", "format": "int32"}, "favoriteProperties": {"type": "integer", "format": "int32"}, "totalViews": {"type": "integer", "format": "int32"}, "averageRating": {"type": "number", "format": "double"}}, "additionalProperties": false}, "PropertyStatusLogDto": {"type": "object", "properties": {"status": {"type": "string", "nullable": true}, "changedAt": {"type": "string", "format": "date-time"}, "comment": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SpendWalletDto": {"type": "object", "properties": {"amount": {"type": "number", "format": "double"}, "description": {"type": "string", "nullable": true}, "paymentType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Street": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "districtId": {"type": "integer", "format": "int32"}, "district": {"$ref": "#/components/schemas/District"}, "projects": {"type": "array", "items": {"$ref": "#/components/schemas/Project"}, "nullable": true}}, "additionalProperties": false}, "TopUpWalletDto": {"type": "object", "properties": {"amount": {"type": "number", "format": "double"}, "paymentMethod": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TransactionSearchResultDto": {"type": "object", "properties": {"transactions": {"type": "array", "items": {"$ref": "#/components/schemas/WalletTransactionDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "currentPage": {"type": "integer", "format": "int32"}, "totalAmount": {"type": "number", "format": "double"}}, "additionalProperties": false}, "UnreadNotificationCountDto": {"type": "object", "properties": {"count": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateContactRequestDto": {"required": ["id", "status"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "note": {"type": "string", "nullable": true}, "status": {"maxLength": 20, "minLength": 1, "type": "string"}}, "additionalProperties": false}, "UpdateHighlightDto": {"required": ["isHighlighted"], "type": "object", "properties": {"isHighlighted": {"type": "boolean"}}, "additionalProperties": false}, "UpdatePropertyMediaCaptionDto": {"required": ["caption", "id"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "caption": {"maxLength": 50, "minLength": 1, "type": "string"}}, "additionalProperties": false}, "UpdatePropertyMediaIsAvatarDto": {"required": ["id", "isAvatar"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "isAvatar": {"type": "boolean"}}, "additionalProperties": false}, "UpdateStatusDto": {"required": ["status"], "type": "object", "properties": {"status": {"minLength": 1, "type": "string"}, "comment": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateUserTaxInfoDto": {"type": "object", "properties": {"personalTaxCode": {"pattern": "^(\\d{10}|\\d{10}-\\d{3})$", "type": "string", "nullable": true}, "invoiceInfo": {"$ref": "#/components/schemas/UserInvoiceInfoDto"}}, "additionalProperties": false}, "UserAvatarDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userID": {"type": "string", "format": "uuid"}, "mediaType": {"type": "string", "nullable": true}, "mediaURL": {"type": "string", "nullable": true}, "filePath": {"type": "string", "nullable": true}, "uploadedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "UserDashboardDto": {"type": "object", "properties": {"userInfo": {"$ref": "#/components/schemas/UserInfoDto"}, "walletInfo": {"$ref": "#/components/schemas/WalletInfoDto"}, "propertyStats": {"$ref": "#/components/schemas/PropertyStatsDto"}, "recentTransactions": {"type": "array", "items": {"$ref": "#/components/schemas/WalletTransactionDto"}, "nullable": true}, "memberRanking": {"$ref": "#/components/schemas/MemberRankingDto"}}, "additionalProperties": false}, "UserDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "fullName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "userType": {"type": "string", "nullable": true}, "token": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "phone2": {"type": "string", "nullable": true}, "phone3": {"type": "string", "nullable": true}, "lastLogin": {"type": "string", "format": "date-time", "nullable": true}, "totalSpent": {"type": "number", "format": "double", "nullable": true}, "memberRank": {"type": "string", "nullable": true}, "avatarImage": {"type": "string", "nullable": true}, "avatarURL": {"type": "string", "nullable": true}, "transferCode": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "personalTaxCode": {"type": "string", "nullable": true}, "invoiceInfo": {"$ref": "#/components/schemas/UserInvoiceInfoDto"}, "wallet": {"$ref": "#/components/schemas/WalletDto"}}, "additionalProperties": false}, "UserFavoriteDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "propertyId": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "UserInfoDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "fullName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "userType": {"type": "string", "nullable": true}, "memberRank": {"type": "string", "nullable": true}, "lastLogin": {"type": "string", "format": "date-time", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "roles": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UserInvoiceInfoDto": {"type": "object", "properties": {"buyerName": {"type": "string", "nullable": true}, "email": {"type": "string", "format": "email", "nullable": true}, "companyName": {"type": "string", "nullable": true}, "taxCode": {"pattern": "^(\\d{10}|\\d{10}-\\d{3})$", "type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WalletBalanceDto": {"type": "object", "properties": {"balance": {"type": "number", "format": "double"}, "totalSpent": {"type": "number", "format": "double"}}, "additionalProperties": false}, "WalletDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userId": {"type": "string", "format": "uuid"}, "balance": {"type": "number", "format": "double"}}, "additionalProperties": false}, "WalletInfoDto": {"type": "object", "properties": {"balance": {"type": "number", "format": "double"}, "totalSpent": {"type": "number", "format": "double"}, "totalTransactions": {"type": "integer", "format": "int32"}, "lastMonthSpending": {"type": "number", "format": "double"}}, "additionalProperties": false}, "WalletTransactionDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userId": {"type": "string", "format": "uuid"}, "amount": {"type": "number", "format": "double"}, "type": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "status": {"type": "string", "nullable": true}, "paymentMethod": {"type": "string", "nullable": true}, "transactionReference": {"type": "string", "nullable": true}, "processedAt": {"type": "string", "format": "date-time", "nullable": true}, "failureReason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Ward": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "nameWithType": {"type": "string", "nullable": true}, "path": {"type": "string", "nullable": true}, "pathWithType": {"type": "string", "nullable": true}, "districtId": {"type": "integer", "format": "int32"}, "district": {"$ref": "#/components/schemas/District"}, "projects": {"type": "array", "items": {"$ref": "#/components/schemas/Project"}, "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Example: 'Bearer {token}'", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}