import type { BlogPost, BlogCategory } from "@/types/blog"

export const categories: BlogCategory[] = [
  { id: "1", name: "Market Trends", slug: "market-trends" },
  { id: "2", name: "Buying Guide", slug: "buying-guide" },
  { id: "3", name: "Home Improvement", slug: "home-improvement" },
  { id: "4", name: "Investment Tips", slug: "investment-tips" },
]

export const blogPosts: BlogPost[] = [
  {
    id: "1",
    title: "Top 10 Emerging Real Estate Markets in 2024",
    slug: "top-10-emerging-real-estate-markets-2024",
    excerpt: "Discover the most promising real estate markets that are expected to boom in 2024.",
    content:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua...",
    category: "market-trends",
    date: "2024-02-26",
    image: "/placeholder.svg?height=400&width=600",
  },
  {
    id: "2",
    title: "Essential Tips for First-Time Home Buyers",
    slug: "essential-tips-first-time-home-buyers",
    excerpt: "Everything you need to know before making your first home purchase.",
    content:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua...",
    category: "buying-guide",
    date: "2024-02-25",
    image: "/placeholder.svg?height=400&width=600",
  },
  // Add more mock posts as needed
]

export async function getPosts(page = 1, category?: string) {
  const postsPerPage = 6
  const filteredPosts = category ? blogPosts.filter((post) => post.category === category) : blogPosts

  const totalPosts = filteredPosts.length
  const totalPages = Math.ceil(totalPosts / postsPerPage)

  const posts = filteredPosts.slice((page - 1) * postsPerPage, page * postsPerPage)

  return {
    posts,
    totalPages,
    currentPage: page,
  }
}

export async function getPost(slug: string) {
  return blogPosts.find((post) => post.slug === slug)
}

