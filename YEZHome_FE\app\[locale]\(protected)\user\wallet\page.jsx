"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useProfile } from "@/contexts/ProfileContext";
import { useTranslations } from "next-intl";
import WalletInformation from "@/components/wallet/WalletInformation";
import WalletTopup from "./WalletTopup";

export default function WalletPage() {
  const t = useTranslations("UserWalletPage");
  const { profile, walletInfo, loading: profileLoading } = useProfile();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [transferContent, setTransferContent] = useState("");

  const router = useRouter();
  const searchParams = useSearchParams();

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);

      // Get transfer code from profile
      if (profile?.user?.transferCode) {
        setTransferContent(profile.transferCode);
      }
    } catch (err) {
      setError(t("genericFetchError"));
      console.error(err);
    } finally {
      setLoading(false);
    }
  }, [t, profile]);

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className="p-6 mx-auto">
      <h1 className="text-2xl font-bold text-navy-blue mb-6">{t("pageTitle")}</h1>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Wallet Information */}
        <WalletInformation walletInfo={walletInfo} profile={profile} />
        <WalletTopup />
      </div>
    </div>
  );
}
