const CHUNK_PUBLIC_PATH = "server/app/[locale]/(protected)/user/bds/[propertyId]/page.js";
const runtime = require("../../../../../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_c336f25b._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__4c58c055._.js");
runtime.loadChunk("server/chunks/ssr/app_1f3630ef._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_96715ba7._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_forbidden-error_ea7ea172.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_unauthorized-error_c8949b27.js");
runtime.loadChunk("server/chunks/ssr/messages_fa4aa9bc._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__49ec74c5._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_093fb13f._.js");
runtime.loadChunk("server/chunks/ssr/_9d1df110._.js");
runtime.loadChunk("server/chunks/ssr/app_[locale]_not-found_jsx_7aca902c._.js");
runtime.loadChunk("server/chunks/ssr/_ab3184e1._.js");
runtime.loadChunk("server/chunks/ssr/app_[locale]_(protected)_user_loading_jsx_f4ac3e38._.js");
runtime.loadChunk("server/chunks/ssr/app_[locale]_(protected)_user_bds_loading_jsx_10e35b1c._.js");
runtime.loadChunk("server/chunks/ssr/_d765287a._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_8888f815._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/[locale]/(protected)/user/bds/[propertyId]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/actions/server/authenticate.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/sessionUtils.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/app/actions/server/userFavorite.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/app/actions/server/notification.jsx [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/app/actions/server/property.jsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/[locale]/(protected)/user/bds/[propertyId]/page { METADATA_0 => \"[project]/app/favicon.ico.mjs { IMAGE => \\\"[project]/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/app/[locale]/layout.jsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/app/[locale]/loading.jsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_6 => \"[project]/app/[locale]/not-found.jsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/app/[locale]/(protected)/user/layout.jsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_8 => \"[project]/app/[locale]/(protected)/user/loading.jsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_9 => \"[project]/app/[locale]/(protected)/user/bds/loading.jsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_10 => \"[project]/app/[locale]/(protected)/user/bds/[propertyId]/page.jsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/[locale]/(protected)/user/bds/[propertyId]/page { METADATA_0 => \"[project]/app/favicon.ico.mjs { IMAGE => \\\"[project]/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/app/[locale]/layout.jsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/app/[locale]/loading.jsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_6 => \"[project]/app/[locale]/not-found.jsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/app/[locale]/(protected)/user/layout.jsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_8 => \"[project]/app/[locale]/(protected)/user/loading.jsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_9 => \"[project]/app/[locale]/(protected)/user/bds/loading.jsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_10 => \"[project]/app/[locale]/(protected)/user/bds/[propertyId]/page.jsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
