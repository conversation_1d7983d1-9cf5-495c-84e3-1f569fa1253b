"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { useRouter, useSearchParams, usePathname } from "next/navigation";
import dynamic from "next/dynamic";
import { searchProperties } from "@/app/actions/server/property";
import { toast } from "@/hooks/use-toast";
import { HCM_COORDINATES_DISTRICT_2 } from "@/lib/enum";
import { useTranslations } from "next-intl";
import { useAuth } from "@/contexts/AuthContext";
const PropertyDetailModal = dynamic(() => import("@/components/property/PropertyDetailModal"), {
  ssr: false,
  loading: () => <div className="h-16 bg-white animate-pulse"></div>,
});
// Dynamically import heavy components
const SearchFilterComponent = dynamic(() => import("@/components/property/SearchFilter"), {
  loading: () => <div className="h-16 bg-white animate-pulse"></div>,
});

const MapSectionComponent = dynamic(() => import("@/components/property/HomeMap"), {
  ssr: false, // Map libraries often need to be client-side only
  loading: () => <div className="flex-1 md:w-3/5 bg-gray-100 animate-pulse"></div>,
});

const PropertyListComponent = dynamic(() => import("@/components/property/PropertyList"), {
  loading: () => <div className="w-full md:w-2/5 bg-white animate-pulse"></div>,
});

// Helper function to parse URL parameters into filter criteria
const parseUrlToFilterCriteria = (searchParams) => {
  const filterCriteria = {
    transactionType: [],
    propertyType: [],
    location: {
      province: "",
      district: "",
      address: "",
    },
    priceRange: {
      min: "",
      max: "",
    },
    areaRange: {
      min: "",
      max: "",
    },
    bedrooms: "",
    bathrooms: "",
    direction: "",
    legalStatus: "",
    roadWidth: "",
  };

  // Parse transaction type
  const postType = searchParams.getAll("postType");
  if (postType.length > 0) {
    filterCriteria.transactionType = postType;
  }

  // Parse property type
  const propertyType = searchParams.getAll("propertyType");
  if (propertyType.length > 0) {
    filterCriteria.propertyType = propertyType;
  }

  // Parse location
  const province = searchParams.get("cityId");
  if (province) filterCriteria.location.province = province;

  const district = searchParams.get("districtId");
  if (district) filterCriteria.location.district = district;

  const address = searchParams.get("address");
  if (address) filterCriteria.location.address = address;

  // Parse price range
  const minPrice = searchParams.get("minPrice");
  if (minPrice) filterCriteria.priceRange.min = minPrice;

  const maxPrice = searchParams.get("maxPrice");
  if (maxPrice) filterCriteria.priceRange.max = maxPrice;

  // Parse area range
  const minArea = searchParams.get("minArea");
  if (minArea) filterCriteria.areaRange.min = minArea;

  const maxArea = searchParams.get("maxArea");
  if (maxArea) filterCriteria.areaRange.max = maxArea;

  // Parse other filters
  const bedrooms = searchParams.get("minRooms");
  if (bedrooms) filterCriteria.bedrooms = bedrooms;

  const bathrooms = searchParams.get("minToilets");
  if (bathrooms) filterCriteria.bathrooms = bathrooms;

  const direction = searchParams.get("direction");
  if (direction) filterCriteria.direction = direction;

  const legalStatus = searchParams.get("legality");
  if (legalStatus) filterCriteria.legalStatus = legalStatus;

  const roadWidth = searchParams.get("minRoadWidth");
  if (roadWidth) filterCriteria.roadWidth = roadWidth;

  return filterCriteria;
};

// Helper function to convert filter criteria to URL parameters
const filterCriteriaToUrlParams = (criteria) => {
  const params = new URLSearchParams();

  // Transaction Type
  if (criteria.transactionType && criteria.transactionType.length > 0) {
    criteria.transactionType.forEach((type) => {
      params.append("postType", type);
    });
  }

  // Property Type
  if (criteria.propertyType && criteria.propertyType.length > 0) {
    criteria.propertyType.forEach((type) => {
      params.append("propertyType", type);
    });
  }

  // Location
  if (criteria.location) {
    if (criteria.location.province) {
      params.append("cityId", criteria.location.province);
    }
    if (criteria.location.district) {
      params.append("districtId", criteria.location.district);
    }
    if (criteria.location.address) {
      params.append("address", criteria.location.address);
    }
  }

  // Price Range
  if (criteria.priceRange) {
    if (criteria.priceRange.min) {
      params.append("minPrice", criteria.priceRange.min);
    }
    if (criteria.priceRange.max) {
      params.append("maxPrice", criteria.priceRange.max);
    }
  }

  // Area Range
  if (criteria.areaRange) {
    if (criteria.areaRange.min) {
      params.append("minArea", criteria.areaRange.min);
    }
    if (criteria.areaRange.max) {
      params.append("maxArea", criteria.areaRange.max);
    }
  }

  // Other filters
  if (criteria.bedrooms) {
    params.append("minRooms", criteria.bedrooms);
  }
  if (criteria.bathrooms) {
    params.append("minToilets", criteria.bathrooms);
  }
  if (criteria.direction) {
    params.append("direction", criteria.direction);
  }
  if (criteria.legalStatus) {
    params.append("legality", criteria.legalStatus);
  }
  if (criteria.roadWidth) {
    params.append("minRoadWidth", criteria.roadWidth);
  }

  return params.toString();
};

export default function Home() {
  const tError = useTranslations("ErrorMessage");
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  const [filterCriteria, setFilterCriteria] = useState({
    transactionType: [],
    propertyType: [],
    location: {
      province: "",
      district: "",
      address: "",
    },
    priceRange: {
      min: "",
      max: "",
    },
    areaRange: {
      min: "",
      max: "",
    },
    bedrooms: "",
    bathrooms: "",
    direction: "",
    legalStatus: "",
    roadWidth: "",
    page: 1,
    pageSize: 10,
    sw_lat: null,
    sw_lng: null,
    ne_lat: null,
    ne_lng: null,
  });

  const [filteredProperties, setFilteredProperties] = useState([]);
  const [paginationInfo, setPaginationInfo] = useState({
    totalCount: 0,
    pageCount: 1,
    currentPage: 1,
    pageSize: 10,
    hasNextPage: false,
    hasPreviousPage: false,
  });
  const [selectedProperty, setSelectedProperty] = useState(null);
  const [selectedPropertyForModal, setSelectedPropertyForModal] = useState(null);

  // --- State cho Vị trí ---
  const [userLocation, setUserLocation] = useState(null);
  const [locationError, setLocationError] = useState(null);
  const [isLoadingLocation, setIsLoadingLocation] = useState(true);
  const [finalLocation, setFinalLocation] = useState(null);

  // --- State cho Danh sách Bất động sản ---
  const [properties, setProperties] = useState(null);
  const [isLoadingProperties, setIsLoadingProperties] = useState(false);
  const [propertiesError, setPropertiesError] = useState(null);

  const [mapBounds, setMapBounds] = useState(null);

  const { isLoggedIn } = useAuth();

  // Get user's location on component mount - modified to not set default location when permission denied
  useEffect(() => {

    // Kiểm tra hỗ trợ Geolocation API
    if (!navigator.geolocation) {
      setLocationError(new Error("Trình duyệt của bạn không hỗ trợ Geolocation."));
      setIsLoadingLocation(false);

      // Xác định và set finalLocation ngay cả khi có lỗi hoặc không hỗ trợ
      setFinalLocation(HCM_COORDINATES_DISTRICT_2); // Set vị trí mặc định

      return;
    }

    // Callback khi lấy vị trí thành công
    const successHandler = (position) => {

      const determinedLocation = {
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        accuracy: position.coords.accuracy,
      };

      setUserLocation(determinedLocation);
      setLocationError(null); // Xóa lỗi nếu có
      setIsLoadingLocation(false);
      // Set finalLocation khi lấy vị trí thành công
      setFinalLocation(determinedLocation);
    };

    // Callback khi lấy vị trí thất bại
    const errorHandler = (err) => {
      setLocationError(new Error(`Lỗi ${err.code}: ${err.message}`));
      setUserLocation(null); // Đảm bảo userLocation là null khi có lỗi
      setIsLoadingLocation(false);
      // Set finalLocation khi lấy vị trí thất bại
      setFinalLocation(HCM_COORDINATES_DISTRICT_2); // Set vị trí mặc định

      // Hiển thị toast thông báo lỗi cho người dùng
      toast({
        title: tError("locationErrorTitle"),
        description: tError("locationErrorDescription", { message: err.message }),
        className: "bg-red-600 text-white",
      });
    };

    // Tùy chọn cấu hình cho getCurrentPosition
    const options = {
      enableHighAccuracy: true, // Ưu tiên độ chính xác
      timeout: 10000, // Thời gian chờ tối đa (ms)
      maximumAge: 0, // Không dùng cache vị trí cũ
    };

    // Gọi API lấy vị trí hiện tại
    // Đây là nơi trình duyệt sẽ hiển thị popup xin quyền người dùng
    navigator.geolocation.getCurrentPosition(successHandler, errorHandler, options);
  }, []);

  // Parse URL parameters on initial load
  useEffect(() => {
    if (searchParams && isInitialLoad) {
      const urlFilterCriteria = parseUrlToFilterCriteria(searchParams);
      setFilterCriteria(urlFilterCriteria);
      setIsInitialLoad(false);
    }
  }, [searchParams, isInitialLoad]);

  // Fetch properties based on filter criteria
  useEffect(() => {   
    // Chỉ tiến hành fetch nếu finalLocation đã được set
    // và chưa có dữ liệu BĐS (tránh fetch lại không cần thiết khi component render lại)
    // và không đang trong quá trình fetch
    if (!mapBounds || isLoadingProperties) {
      return; // Dừng nếu chưa sẵn sàng hoặc đã fetch
    }

    // Bắt đầu quá trình fetch BĐS
    setIsLoadingProperties(true);
    setPropertiesError(null); // Xóa lỗi fetch trước đó nếu có

    const fetchProperties = async () => {
      try {
        // Add user location to filter criteria for proximity search ONLY if available
        const searchCriteria = {
          ...filterCriteria,
        };

        const response = await searchProperties(searchCriteria);

        if (response.success) {
          // Update state with paginated data
          setPropertiesError(null); // Xóa lỗi fetch
          setProperties(response.data.items);
          setFilteredProperties(response.data.items);
          setPaginationInfo({
            totalCount: response.data.totalCount,
            pageCount: response.data.pageCount,
            currentPage: response.data.currentPage,
            pageSize: response.data.pageSize,
            hasNextPage: response.data.hasNextPage,
            hasPreviousPage: response.data.hasPreviousPage,
          });
        } else {
          setProperties(null); // Xóa data BĐS cũ khi có lỗi
          setPropertiesError(response.message); // Cập nhật state lỗi fetch
          toast({
            title: "Lỗi",
            description: response.message,
            variant: "destructive",
          });
        }
      } catch (error) {
        setProperties(null); // Xóa data BĐS cũ khi có lỗi
        setPropertiesError(error); // Cập nhật state lỗi fetch
        toast({
          title: "Lỗi",
          description: "Đã xảy ra lỗi khi tải dữ liệu",
          variant: "destructive",
        });
      } finally {
        setIsLoadingProperties(false);
      }
    };

    fetchProperties();
  }, [filterCriteria, finalLocation, mapBounds]);

  // Tạo mảng markers từ dữ liệu properties đã fetch
  const propertyMarkers = useMemo(() => {
    if (!properties) return [];
    return properties
      .map((prop) => {
        if (prop.latitude !== undefined && prop.longitude !== undefined) {
          return {
            ...prop,
            imageUrl: prop.propertyMedia?.[0]?.mediaURL,
          };
        }
        return null;
      })
      .filter((marker) => marker !== null);
  }, [properties]);

  // Update URL when filter criteria changes
  useEffect(() => {
    if (isInitialLoad) return;

    const urlParams = filterCriteriaToUrlParams(filterCriteria);
    const url = urlParams ? `${pathname}?${urlParams}` : pathname;

    // Update URL without refreshing the page
    router.push(url, { scroll: false });
  }, [filterCriteria, router, pathname, isInitialLoad]);

  // Handle filter changes
  const handleFilterChange = useCallback((newFilterCriteria) => {
    setFilterCriteria(newFilterCriteria);
  }, []);

  // Handle property selection (for map centering and modal)
  const handlePropertySelect = useCallback((property) => {
    setSelectedProperty(property);
    setSelectedPropertyForModal(property);
  }, []);

  // Handle page change
  const handlePageChange = useCallback((page) => {
    setFilterCriteria((prev) => ({
      ...prev,
      page,
    }));
  }, []);

  const handleBoundsChange = useCallback((bounds) => {
    setMapBounds(bounds);
    setFilterCriteria((prev) => ({
      ...prev,
      sw_lat: bounds.getSouthWest().lat,
      sw_lng: bounds.getSouthWest().lng,
      ne_lat: bounds.getNorthEast().lat,
      ne_lng: bounds.getNorthEast().lng,
    }));
  }, []);

  return (
    <div>
      <main className="flex flex-col">
        <SearchFilterComponent
          onFilterChange={handleFilterChange}
          initialFilters={filterCriteria}
        />
        <div className="flex-grow flex flex-col md:flex-row">
          {/* Map Section */}
          {/* Hiển thị Bản đồ - Chỉ render khi finalLocation đã được set */}
          {finalLocation ? (
            <MapSectionComponent
              center={finalLocation}
              onBoundsChange={handleBoundsChange}
              onViewDetails={handlePropertySelect}
              markers={propertyMarkers}
              activePropertyId ={selectedProperty?.id}
            ></MapSectionComponent>
          ) : (
            // Hiển thị thông báo khi chưa có finalLocation (chưa xác định vị trí)
            !isLoadingLocation && <p>Thiếu API Key Goong Maps.</p> // Nếu không loading location và thiếu key
            // Trường hợp đang loading location, thông báo "Đang lấy vị trí..." đã hiển thị bên trên
          )}

          {/* Hiển thị trạng thái lấy vị trí */}
          {isLoadingLocation && (
            <div className="w-full h-[calc(100vh-227px)] relative z-0 opacity-75 cursor-not-allowed">
              <div className="flex items-center justify-center h-full w-full">Đang lấy vị trí hiện tại...</div>
            </div>
          )}
          {/* Property List Section */}
          <PropertyListComponent
            properties={filteredProperties}
            loading={isLoadingProperties}
            onPropertySelect={handlePropertySelect}
            pagination={paginationInfo}
            onPageChange={handlePageChange}
            isLoggedIn={isLoggedIn}
          />
        </div>
      </main>
      {selectedPropertyForModal && (
        <PropertyDetailModal
          property={selectedPropertyForModal}
          onClose={() => setSelectedPropertyForModal(null)}
        />
      )}
    </div>
  );
}
