"use client";

import { motion } from "framer-motion";
import { Check } from "lucide-react";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

export function PaymentMethodSelection({ selectedMethod, setSelectedMethod, paymentMethods, t }) {
  return (
    <motion.div initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }} transition={{ duration: 0.3 }}>
      <div className="space-y-4">
        <Label className="text-base">{t("paymentMethodLabel")}</Label>

        <RadioGroup value={selectedMethod} onValueChange={setSelectedMethod} className="space-y-2">
          {paymentMethods.map((method) => (
            <Label
              key={method.id}
              htmlFor={method.id}
              className={`flex items-center space-x-3 border rounded-lg p-4 cursor-pointer transition-colors ${
                selectedMethod === method.id ? "border-primary bg-primary/5" : "border-gray-200 hover:bg-gray-50"
              }`}
            >
              <RadioGroupItem value={method.id} id={method.id} className="sr-only" />
              <div className="flex-shrink-0">{method.icon}</div>
              <div className="flex-grow">
                <div className="font-medium">{method.name}</div>
                <div className="text-sm text-gray-500">{method.description}</div>
              </div>
              {selectedMethod === method.id && <Check className="h-5 w-5 text-primary" />}
            </Label>
          ))}
        </RadioGroup>
      </div>
    </motion.div>
  );
}
