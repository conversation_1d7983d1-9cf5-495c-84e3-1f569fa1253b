"use client"

import { useState } from "react"
import { Wallet } from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Check, ChevronRight } from "lucide-react"

import { PAYMENT_METHODS, PRESET_AMOUNTS } from "@/lib/enum"
import { AmountSelection } from "@/components/wallet/AmountSelection"
import { PaymentMethodSelection } from "@/components/wallet/PaymentMethodSelection"
import { InvoiceOptions } from "@/components/wallet/InvoiceOptions"
import { ConfirmationScreen } from "./ConfirmationScreen"
import { useTranslations } from 'next-intl';

export default function WalletTopup() {
  // Form state
  const [amount, setAmount] = useState(PRESET_AMOUNTS[2])
  const [selectedMethod, setSelectedMethod] = useState("banking")
  const [needInvoice, setNeedInvoice] = useState(false)
  const [invoiceDetails, setInvoiceDetails] = useState({
    companyName: "",
    taxId: "",
    address: "",
  })
  const [currentStep, setCurrentStep] = useState(1)
  const [transactionId, setTransactionId] = useState("")
  const t = useTranslations('UserWalletPage');
  const tCommon = useTranslations('Common');

  // Next step handler
  const handleNextStep = () => {
    if (currentStep === 1 && (amount <= 0 || isNaN(amount))) {
      return
    }

    setCurrentStep(currentStep + 1)
  }

  // Previous step handler
  const handlePreviousStep = () => {
    setCurrentStep(currentStep - 1)
  }

  // Submit handler
  const handleSubmit = () => {
    // Here you would integrate with your payment processing system
    console.log({
      amount,
      paymentMethod: selectedMethod,
      needInvoice,
      invoiceDetails: needInvoice ? invoiceDetails : null,
    })

    // Generate a random transaction ID
    setTransactionId(`TXN${Math.floor(Math.random() * 1000000)}`)

    // Go to confirmation step
    setCurrentStep(4)
  }

  return (
    <div className="lg:col-span-3 min-h-screen flex">
      <Card className="w-full max-w-xl">
        <CardHeader>
          <div className="flex items-center justify-between mb-3">
            <div>
              <CardTitle className="text-2xl">t{'depositSectionTitle'}</CardTitle>
            </div>
            <Wallet className="h-8 w-8" />
          </div>

          {/* Progress indicator - only show for steps 1-3 */}
          {currentStep < 4 && (
            <div className="flex justify-between mt-6">
              {[1, 2, 3].map((step) => (
                <div key={step} className="flex flex-col items-center">
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      currentStep >= step ? "bg-navy-blue text-primary-foreground" : "bg-gray-200 text-gray-500"
                    }`}
                  >
                    {currentStep > step ? <Check className="h-4 w-4" /> : step}
                  </div>
                  <span className="text-xs mt-1 text-gray-500">
                    {step === 1 ? t('depositStep1') : step === 2 ? t('depositStep2') : t('depositStep3')}
                  </span>
                </div>
              ))}
            </div>
          )}
        </CardHeader>

        <CardContent>
          {/* Step 1: Amount Selection */}
          {currentStep === 1 && (
            <AmountSelection
              amount={amount}
              setAmount={setAmount}
              presetAmounts={PRESET_AMOUNTS}
            />
          )}

          {/* Step 2: Payment Method */}
          {currentStep === 2 && (
            <PaymentMethodSelection
              selectedMethod={selectedMethod}
              setSelectedMethod={setSelectedMethod}
              paymentMethods={PAYMENT_METHODS}
              t={t}
            />
          )}

          {/* Step 3: Invoice Option */}
          {currentStep === 3 && (
            <InvoiceOptions
              needInvoice={needInvoice}
              setNeedInvoice={setNeedInvoice}
              invoiceDetails={invoiceDetails}
              setInvoiceDetails={setInvoiceDetails}
              amount={amount}
              selectedMethod={selectedMethod}
              paymentMethods={PAYMENT_METHODS}
              t={t}
            />
          )}

          {/* Step 4: Confirmation */}
          {currentStep === 4 && (
            <ConfirmationScreen
              amount={amount}
              selectedMethod={selectedMethod}
              transactionId={transactionId}
              paymentMethods={PAYMENT_METHODS}
              t={t}
            />
          )}
        </CardContent>

        <CardFooter className="flex justify-between">
          {currentStep < 4 && (
            <>
              {currentStep > 1 && (
                <Button variant="outline" onClick={handlePreviousStep}>
                  {tCommon('button_previous')}
                </Button>
              )}
              {currentStep === 1 && <div></div>}

              {(currentStep < 3) ? (
                <Button onClick={handleNextStep} disabled={currentStep === 1 && (amount <= 0 || isNaN(amount))}>
                  {tCommon('button_next')} <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              ) : (
                <Button onClick={handleSubmit}>
                  {tCommon('button_confirm')}
                </Button>
              )}
            </>
          )}
        </CardFooter>
      </Card>
    </div>
  )
}