"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-intl";
exports.ids = ["vendor-chunks/next-intl"];
exports.modules = {

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/navigation/react-server/createNavigation.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/react-server/createNavigation.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ createNavigation)\n/* harmony export */ });\n/* harmony import */ var _shared_createSharedNavigationFns_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../shared/createSharedNavigationFns.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js\");\n/* harmony import */ var _getServerLocale_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getServerLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/navigation/react-server/getServerLocale.js\");\n\n\n\nfunction createNavigation(routing) {\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  const {\n    config,\n    ...fns\n  } = (0,_shared_createSharedNavigationFns_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_getServerLocale_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"], routing);\n  function notSupported(hookName) {\n    return () => {\n      throw new Error(`\\`${hookName}\\` is not supported in Server Components. You can use this hook if you convert the calling component to a Client Component.`);\n    };\n  }\n  return {\n    ...fns,\n    usePathname: notSupported('usePathname'),\n    useRouter: notSupported('useRouter')\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L25hdmlnYXRpb24vcmVhY3Qtc2VydmVyL2NyZWF0ZU5hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQStFO0FBQzVCOztBQUVuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSxFQUFFLGdGQUF5QixDQUFDLDJEQUFlO0FBQy9DO0FBQ0E7QUFDQSwyQkFBMkIsU0FBUztBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV1QyIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXFlFWl9UZWNoXFxZRVpfSG9tZVxcWUVaSG9tZV9GRVxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcZGV2ZWxvcG1lbnRcXG5hdmlnYXRpb25cXHJlYWN0LXNlcnZlclxcY3JlYXRlTmF2aWdhdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlU2hhcmVkTmF2aWdhdGlvbkZucyBmcm9tICcuLi9zaGFyZWQvY3JlYXRlU2hhcmVkTmF2aWdhdGlvbkZucy5qcyc7XG5pbXBvcnQgZ2V0U2VydmVyTG9jYWxlIGZyb20gJy4vZ2V0U2VydmVyTG9jYWxlLmpzJztcblxuZnVuY3Rpb24gY3JlYXRlTmF2aWdhdGlvbihyb3V0aW5nKSB7XG4gIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tdW51c2VkLXZhcnNcbiAgY29uc3Qge1xuICAgIGNvbmZpZyxcbiAgICAuLi5mbnNcbiAgfSA9IGNyZWF0ZVNoYXJlZE5hdmlnYXRpb25GbnMoZ2V0U2VydmVyTG9jYWxlLCByb3V0aW5nKTtcbiAgZnVuY3Rpb24gbm90U3VwcG9ydGVkKGhvb2tOYW1lKSB7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgXFxgJHtob29rTmFtZX1cXGAgaXMgbm90IHN1cHBvcnRlZCBpbiBTZXJ2ZXIgQ29tcG9uZW50cy4gWW91IGNhbiB1c2UgdGhpcyBob29rIGlmIHlvdSBjb252ZXJ0IHRoZSBjYWxsaW5nIGNvbXBvbmVudCB0byBhIENsaWVudCBDb21wb25lbnQuYCk7XG4gICAgfTtcbiAgfVxuICByZXR1cm4ge1xuICAgIC4uLmZucyxcbiAgICB1c2VQYXRobmFtZTogbm90U3VwcG9ydGVkKCd1c2VQYXRobmFtZScpLFxuICAgIHVzZVJvdXRlcjogbm90U3VwcG9ydGVkKCd1c2VSb3V0ZXInKVxuICB9O1xufVxuXG5leHBvcnQgeyBjcmVhdGVOYXZpZ2F0aW9uIGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/navigation/react-server/createNavigation.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/navigation/react-server/getServerLocale.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/react-server/getServerLocale.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getServerLocale)\n/* harmony export */ });\n/* harmony import */ var _server_react_server_getConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../server/react-server/getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n/**\n * This is only moved to a separate module for easier mocking in\n * `../createNavigatoin.test.tsx` in order to avoid suspending.\n */\nasync function getServerLocale() {\n  const config = await (0,_server_react_server_getConfig_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])();\n  return config.locale;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L25hdmlnYXRpb24vcmVhY3Qtc2VydmVyL2dldFNlcnZlckxvY2FsZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUErRDs7QUFFL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1Qiw2RUFBUztBQUNoQztBQUNBOztBQUVzQyIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXFlFWl9UZWNoXFxZRVpfSG9tZVxcWUVaSG9tZV9GRVxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcZGV2ZWxvcG1lbnRcXG5hdmlnYXRpb25cXHJlYWN0LXNlcnZlclxcZ2V0U2VydmVyTG9jYWxlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBnZXRDb25maWcgZnJvbSAnLi4vLi4vc2VydmVyL3JlYWN0LXNlcnZlci9nZXRDb25maWcuanMnO1xuXG4vKipcbiAqIFRoaXMgaXMgb25seSBtb3ZlZCB0byBhIHNlcGFyYXRlIG1vZHVsZSBmb3IgZWFzaWVyIG1vY2tpbmcgaW5cbiAqIGAuLi9jcmVhdGVOYXZpZ2F0b2luLnRlc3QudHN4YCBpbiBvcmRlciB0byBhdm9pZCBzdXNwZW5kaW5nLlxuICovXG5hc3luYyBmdW5jdGlvbiBnZXRTZXJ2ZXJMb2NhbGUoKSB7XG4gIGNvbnN0IGNvbmZpZyA9IGF3YWl0IGdldENvbmZpZygpO1xuICByZXR1cm4gY29uZmlnLmxvY2FsZTtcbn1cblxuZXhwb3J0IHsgZ2V0U2VydmVyTG9jYWxlIGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/navigation/react-server/getServerLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\node_modules\\\\next-intl\\\\dist\\\\esm\\\\development\\\\navigation\\\\shared\\\\BaseLink.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome_FE\\node_modules\\next-intl\\dist\\esm\\development\\navigation\\shared\\BaseLink.js",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ createSharedNavigationFns)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _routing_config_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../routing/config.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/routing/config.js\");\n/* harmony import */ var _shared_use_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../shared/use.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/use.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/utils.js\");\n/* harmony import */ var _BaseLink_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./BaseLink.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\n\n\n\n\n\n\n\n\n\n/**\n * Shared implementations for `react-server` and `react-client`\n */\nfunction createSharedNavigationFns(getLocale, routing) {\n  const config = (0,_routing_config_js__WEBPACK_IMPORTED_MODULE_3__.receiveRoutingConfig)(routing || {});\n  {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.validateReceivedConfig)(config);\n  }\n  const pathnames = config.pathnames;\n  function Link({\n    href,\n    locale,\n    ...rest\n  }, ref) {\n    let pathname, params;\n    if (typeof href === 'object') {\n      pathname = href.pathname;\n      // @ts-expect-error -- This is ok\n      params = href.params;\n    } else {\n      pathname = href;\n    }\n\n    // @ts-expect-error -- This is ok\n    const isLocalizable = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_5__.isLocalizableHref)(href);\n    const localePromiseOrValue = getLocale();\n    const curLocale = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_5__.isPromise)(localePromiseOrValue) ? (0,_shared_use_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(localePromiseOrValue) : localePromiseOrValue;\n    const finalPathname = isLocalizable ? getPathname({\n      locale: locale || curLocale,\n      // @ts-expect-error -- This is ok\n      href: pathnames == null ? pathname : {\n        pathname,\n        params\n      }\n    }, locale != null || undefined) : pathname;\n    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_BaseLink_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n      ref: ref\n      // @ts-expect-error -- This is ok\n      ,\n      href: typeof href === 'object' ? {\n        ...href,\n        pathname: finalPathname\n      } : finalPathname,\n      locale: locale,\n      localeCookie: config.localeCookie,\n      ...rest\n    });\n  }\n  const LinkWithRef = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(Link);\n  function getPathname(args, /** @private Removed in types returned below */\n  _forcePrefix) {\n    const {\n      href,\n      locale\n    } = args;\n    let pathname;\n    if (pathnames == null) {\n      if (typeof href === 'object') {\n        pathname = href.pathname;\n        if (href.query) {\n          pathname += (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.serializeSearchParams)(href.query);\n        }\n      } else {\n        pathname = href;\n      }\n    } else {\n      pathname = (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.compileLocalizedPathname)({\n        locale,\n        // @ts-expect-error -- This is ok\n        ...(0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.normalizeNameOrNameWithParams)(href),\n        // @ts-expect-error -- This is ok\n        pathnames: config.pathnames\n      });\n    }\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.applyPathnamePrefix)(pathname, locale, config, _forcePrefix);\n  }\n  function getRedirectFn(fn) {\n    /** @see https://next-intl.dev/docs/routing/navigation#redirect */\n    return function redirectFn(args, ...rest) {\n      return fn(getPathname(args), ...rest);\n    };\n  }\n  const redirect$1 = getRedirectFn(next_navigation__WEBPACK_IMPORTED_MODULE_0__.redirect);\n  const permanentRedirect$1 = getRedirectFn(next_navigation__WEBPACK_IMPORTED_MODULE_0__.permanentRedirect);\n  return {\n    config,\n    Link: LinkWithRef,\n    redirect: redirect$1,\n    permanentRedirect: permanentRedirect$1,\n    // Remove `_forcePrefix` from public API\n    getPathname: getPathname\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyPathnamePrefix: () => (/* binding */ applyPathnamePrefix),\n/* harmony export */   compileLocalizedPathname: () => (/* binding */ compileLocalizedPathname),\n/* harmony export */   getBasePath: () => (/* binding */ getBasePath),\n/* harmony export */   getRoute: () => (/* binding */ getRoute),\n/* harmony export */   normalizeNameOrNameWithParams: () => (/* binding */ normalizeNameOrNameWithParams),\n/* harmony export */   serializeSearchParams: () => (/* binding */ serializeSearchParams),\n/* harmony export */   validateReceivedConfig: () => (/* binding */ validateReceivedConfig)\n/* harmony export */ });\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/utils.js\");\n\n\n// Minor false positive: A route that has both optional and\n// required params will allow optional params.\n\n// For `Link`\n\n// For `getPathname` (hence also its consumers: `redirect`, `useRouter`, …)\n\nfunction normalizeNameOrNameWithParams(href) {\n  return typeof href === 'string' ? {\n    pathname: href\n  } : href;\n}\nfunction serializeSearchParams(searchParams) {\n  function serializeValue(value) {\n    return String(value);\n  }\n  const urlSearchParams = new URLSearchParams();\n  for (const [key, value] of Object.entries(searchParams)) {\n    if (Array.isArray(value)) {\n      value.forEach(cur => {\n        urlSearchParams.append(key, serializeValue(cur));\n      });\n    } else {\n      urlSearchParams.set(key, serializeValue(value));\n    }\n  }\n  return '?' + urlSearchParams.toString();\n}\nfunction compileLocalizedPathname({\n  pathname,\n  locale,\n  params,\n  pathnames,\n  query\n}) {\n  function getNamedPath(value) {\n    let namedPath = pathnames[value];\n    if (!namedPath) {\n      // Unknown pathnames\n      namedPath = value;\n    }\n    return namedPath;\n  }\n  function compilePath(namedPath, internalPathname) {\n    const template = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getLocalizedTemplate)(namedPath, locale, internalPathname);\n    let compiled = template;\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        let regexp, replacer;\n        if (Array.isArray(value)) {\n          regexp = `(\\\\[)?\\\\[...${key}\\\\](\\\\])?`;\n          replacer = value.map(v => String(v)).join('/');\n        } else {\n          regexp = `\\\\[${key}\\\\]`;\n          replacer = String(value);\n        }\n        compiled = compiled.replace(new RegExp(regexp, 'g'), replacer);\n      });\n    }\n\n    // Clean up optional catch-all segments that were not replaced\n    compiled = compiled.replace(/\\[\\[\\.\\.\\..+\\]\\]/g, '');\n    compiled = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.normalizeTrailingSlash)(compiled);\n    if (compiled.includes('[')) {\n      // Next.js throws anyway, therefore better provide a more helpful error message\n      throw new Error(`Insufficient params provided for localized pathname.\\nTemplate: ${template}\\nParams: ${JSON.stringify(params)}`);\n    }\n    if (query) {\n      compiled += serializeSearchParams(query);\n    }\n    return compiled;\n  }\n  if (typeof pathname === 'string') {\n    const namedPath = getNamedPath(pathname);\n    const compiled = compilePath(namedPath, pathname);\n    return compiled;\n  } else {\n    const {\n      pathname: internalPathname,\n      ...rest\n    } = pathname;\n    const namedPath = getNamedPath(internalPathname);\n    const compiled = compilePath(namedPath, internalPathname);\n    const result = {\n      ...rest,\n      pathname: compiled\n    };\n    return result;\n  }\n}\nfunction getRoute(locale, pathname, pathnames) {\n  const sortedPathnames = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getSortedPathnames)(Object.keys(pathnames));\n  const decoded = decodeURI(pathname);\n  for (const internalPathname of sortedPathnames) {\n    const localizedPathnamesOrPathname = pathnames[internalPathname];\n    if (typeof localizedPathnamesOrPathname === 'string') {\n      const localizedPathname = localizedPathnamesOrPathname;\n      if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)(localizedPathname, decoded)) {\n        return internalPathname;\n      }\n    } else {\n      if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getLocalizedTemplate)(localizedPathnamesOrPathname, locale, internalPathname), decoded)) {\n        return internalPathname;\n      }\n    }\n  }\n  return pathname;\n}\nfunction getBasePath(pathname, windowPathname = window.location.pathname) {\n  if (pathname === '/') {\n    return windowPathname;\n  } else {\n    return windowPathname.replace(pathname, '');\n  }\n}\nfunction applyPathnamePrefix(pathname, locale, routing, force) {\n  const {\n    mode\n  } = routing.localePrefix;\n  let shouldPrefix;\n  if (force !== undefined) {\n    shouldPrefix = force;\n  } else if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.isLocalizableHref)(pathname)) {\n    if (mode === 'always') {\n      shouldPrefix = true;\n    } else if (mode === 'as-needed') {\n      shouldPrefix = routing.domains ?\n      // Since locales are unique per domain, any locale that is a\n      // default locale of a domain doesn't require a prefix\n      !routing.domains.some(cur => cur.defaultLocale === locale) : locale !== routing.defaultLocale;\n    }\n  }\n  return shouldPrefix ? (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.prefixPathname)((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getLocalePrefix)(locale, routing.localePrefix), pathname) : pathname;\n}\nfunction validateReceivedConfig(config) {\n  if (config.localePrefix?.mode === 'as-needed' && !('defaultLocale' in config)) {\n    throw new Error(\"`localePrefix: 'as-needed' requires a `defaultLocale`.\");\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/react-server/NextIntlClientProviderServer.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/react-server/NextIntlClientProviderServer.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NextIntlClientProviderServer)\n/* harmony export */ });\n/* harmony import */ var _server_react_server_getConfigNow_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../server/react-server/getConfigNow.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfigNow.js\");\n/* harmony import */ var _server_react_server_getFormats_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../server/react-server/getFormats.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getFormats.js\");\n/* harmony import */ var _shared_NextIntlClientProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../shared/NextIntlClientProvider.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\n/* harmony import */ var _server_react_server_getTimeZone_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../server/react-server/getTimeZone.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getTimeZone.js\");\n/* harmony import */ var _server_react_server_getMessages_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../server/react-server/getMessages.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getMessages.js\");\n/* harmony import */ var _server_react_server_getLocale_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../server/react-server/getLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getLocale.js\");\n\n\n\n\n\n\n\n\nasync function NextIntlClientProviderServer({\n  formats,\n  locale,\n  messages,\n  now,\n  timeZone,\n  ...rest\n}) {\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_shared_NextIntlClientProvider_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]\n  // We need to be careful about potentially reading from headers here.\n  // See https://github.com/amannn/next-intl/issues/631\n  , {\n    formats: formats === undefined ? await (0,_server_react_server_getFormats_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])() : formats,\n    locale: locale ?? (await (0,_server_react_server_getLocale_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])()),\n    messages: messages === undefined ? await (0,_server_react_server_getMessages_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])() : messages\n    // Note that we don't assign a default for `now` here,\n    // we only read one from the request config - if any.\n    // Otherwise this would cause a `dynamicIO` error.\n    ,\n    now: now ?? (await (0,_server_react_server_getConfigNow_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()),\n    timeZone: timeZone ?? (await (0,_server_react_server_getTimeZone_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])()),\n    ...rest\n  });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/react-server/NextIntlClientProviderServer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/routing/config.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/routing/config.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   receiveRoutingConfig: () => (/* binding */ receiveRoutingConfig)\n/* harmony export */ });\nfunction receiveRoutingConfig(input) {\n  return {\n    ...input,\n    localePrefix: receiveLocalePrefixConfig(input.localePrefix),\n    localeCookie: receiveLocaleCookie(input.localeCookie),\n    localeDetection: input.localeDetection ?? true,\n    alternateLinks: input.alternateLinks ?? true\n  };\n}\nfunction receiveLocaleCookie(localeCookie) {\n  return localeCookie ?? true ? {\n    name: 'NEXT_LOCALE',\n    sameSite: 'lax',\n    ...(typeof localeCookie === 'object' && localeCookie)\n\n    // `path` needs to be provided based on a detected base path\n    // that depends on the environment when setting a cookie\n  } : false;\n}\nfunction receiveLocalePrefixConfig(localePrefix) {\n  return typeof localePrefix === 'object' ? localePrefix : {\n    mode: localePrefix || 'always'\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3JvdXRpbmcvY29uZmlnLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVnQyIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXFlFWl9UZWNoXFxZRVpfSG9tZVxcWUVaSG9tZV9GRVxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcZGV2ZWxvcG1lbnRcXHJvdXRpbmdcXGNvbmZpZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiByZWNlaXZlUm91dGluZ0NvbmZpZyhpbnB1dCkge1xuICByZXR1cm4ge1xuICAgIC4uLmlucHV0LFxuICAgIGxvY2FsZVByZWZpeDogcmVjZWl2ZUxvY2FsZVByZWZpeENvbmZpZyhpbnB1dC5sb2NhbGVQcmVmaXgpLFxuICAgIGxvY2FsZUNvb2tpZTogcmVjZWl2ZUxvY2FsZUNvb2tpZShpbnB1dC5sb2NhbGVDb29raWUpLFxuICAgIGxvY2FsZURldGVjdGlvbjogaW5wdXQubG9jYWxlRGV0ZWN0aW9uID8/IHRydWUsXG4gICAgYWx0ZXJuYXRlTGlua3M6IGlucHV0LmFsdGVybmF0ZUxpbmtzID8/IHRydWVcbiAgfTtcbn1cbmZ1bmN0aW9uIHJlY2VpdmVMb2NhbGVDb29raWUobG9jYWxlQ29va2llKSB7XG4gIHJldHVybiBsb2NhbGVDb29raWUgPz8gdHJ1ZSA/IHtcbiAgICBuYW1lOiAnTkVYVF9MT0NBTEUnLFxuICAgIHNhbWVTaXRlOiAnbGF4JyxcbiAgICAuLi4odHlwZW9mIGxvY2FsZUNvb2tpZSA9PT0gJ29iamVjdCcgJiYgbG9jYWxlQ29va2llKVxuXG4gICAgLy8gYHBhdGhgIG5lZWRzIHRvIGJlIHByb3ZpZGVkIGJhc2VkIG9uIGEgZGV0ZWN0ZWQgYmFzZSBwYXRoXG4gICAgLy8gdGhhdCBkZXBlbmRzIG9uIHRoZSBlbnZpcm9ubWVudCB3aGVuIHNldHRpbmcgYSBjb29raWVcbiAgfSA6IGZhbHNlO1xufVxuZnVuY3Rpb24gcmVjZWl2ZUxvY2FsZVByZWZpeENvbmZpZyhsb2NhbGVQcmVmaXgpIHtcbiAgcmV0dXJuIHR5cGVvZiBsb2NhbGVQcmVmaXggPT09ICdvYmplY3QnID8gbG9jYWxlUHJlZml4IDoge1xuICAgIG1vZGU6IGxvY2FsZVByZWZpeCB8fCAnYWx3YXlzJ1xuICB9O1xufVxuXG5leHBvcnQgeyByZWNlaXZlUm91dGluZ0NvbmZpZyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/routing/config.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/routing/defineRouting.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/routing/defineRouting.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ defineRouting)\n/* harmony export */ });\nfunction defineRouting(config) {\n  if (config.domains) {\n    validateUniqueLocalesPerDomain(config.domains);\n  }\n  return config;\n}\nfunction validateUniqueLocalesPerDomain(domains) {\n  const domainsByLocale = new Map();\n  for (const {\n    domain,\n    locales\n  } of domains) {\n    for (const locale of locales) {\n      const localeDomains = domainsByLocale.get(locale) || new Set();\n      localeDomains.add(domain);\n      domainsByLocale.set(locale, localeDomains);\n    }\n  }\n  const duplicateLocaleMessages = Array.from(domainsByLocale.entries()).filter(([, localeDomains]) => localeDomains.size > 1).map(([locale, localeDomains]) => `- \"${locale}\" is used by: ${Array.from(localeDomains).join(', ')}`);\n  if (duplicateLocaleMessages.length > 0) {\n    console.warn('Locales are expected to be unique per domain, but found overlap:\\n' + duplicateLocaleMessages.join('\\n') + '\\nPlease see https://next-intl.dev/docs/routing#domains');\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3JvdXRpbmcvZGVmaW5lUm91dGluZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxS0FBcUssT0FBTyxnQkFBZ0IscUNBQXFDO0FBQ2pPO0FBQ0E7QUFDQTtBQUNBOztBQUVvQyIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXFlFWl9UZWNoXFxZRVpfSG9tZVxcWUVaSG9tZV9GRVxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcZGV2ZWxvcG1lbnRcXHJvdXRpbmdcXGRlZmluZVJvdXRpbmcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gZGVmaW5lUm91dGluZyhjb25maWcpIHtcbiAgaWYgKGNvbmZpZy5kb21haW5zKSB7XG4gICAgdmFsaWRhdGVVbmlxdWVMb2NhbGVzUGVyRG9tYWluKGNvbmZpZy5kb21haW5zKTtcbiAgfVxuICByZXR1cm4gY29uZmlnO1xufVxuZnVuY3Rpb24gdmFsaWRhdGVVbmlxdWVMb2NhbGVzUGVyRG9tYWluKGRvbWFpbnMpIHtcbiAgY29uc3QgZG9tYWluc0J5TG9jYWxlID0gbmV3IE1hcCgpO1xuICBmb3IgKGNvbnN0IHtcbiAgICBkb21haW4sXG4gICAgbG9jYWxlc1xuICB9IG9mIGRvbWFpbnMpIHtcbiAgICBmb3IgKGNvbnN0IGxvY2FsZSBvZiBsb2NhbGVzKSB7XG4gICAgICBjb25zdCBsb2NhbGVEb21haW5zID0gZG9tYWluc0J5TG9jYWxlLmdldChsb2NhbGUpIHx8IG5ldyBTZXQoKTtcbiAgICAgIGxvY2FsZURvbWFpbnMuYWRkKGRvbWFpbik7XG4gICAgICBkb21haW5zQnlMb2NhbGUuc2V0KGxvY2FsZSwgbG9jYWxlRG9tYWlucyk7XG4gICAgfVxuICB9XG4gIGNvbnN0IGR1cGxpY2F0ZUxvY2FsZU1lc3NhZ2VzID0gQXJyYXkuZnJvbShkb21haW5zQnlMb2NhbGUuZW50cmllcygpKS5maWx0ZXIoKFssIGxvY2FsZURvbWFpbnNdKSA9PiBsb2NhbGVEb21haW5zLnNpemUgPiAxKS5tYXAoKFtsb2NhbGUsIGxvY2FsZURvbWFpbnNdKSA9PiBgLSBcIiR7bG9jYWxlfVwiIGlzIHVzZWQgYnk6ICR7QXJyYXkuZnJvbShsb2NhbGVEb21haW5zKS5qb2luKCcsICcpfWApO1xuICBpZiAoZHVwbGljYXRlTG9jYWxlTWVzc2FnZXMubGVuZ3RoID4gMCkge1xuICAgIGNvbnNvbGUud2FybignTG9jYWxlcyBhcmUgZXhwZWN0ZWQgdG8gYmUgdW5pcXVlIHBlciBkb21haW4sIGJ1dCBmb3VuZCBvdmVybGFwOlxcbicgKyBkdXBsaWNhdGVMb2NhbGVNZXNzYWdlcy5qb2luKCdcXG4nKSArICdcXG5QbGVhc2Ugc2VlIGh0dHBzOi8vbmV4dC1pbnRsLmRldi9kb2NzL3JvdXRpbmcjZG9tYWlucycpO1xuICB9XG59XG5cbmV4cG9ydCB7IGRlZmluZVJvdXRpbmcgYXMgZGVmYXVsdCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/routing/defineRouting.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocale.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocale.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRequestLocale: () => (/* binding */ getRequestLocale)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _shared_constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/constants.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/constants.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/utils.js\");\n/* harmony import */ var _RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RequestLocaleCache.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocaleCache.js\");\n\n\n\n\n\n\nasync function getHeadersImpl() {\n  const promiseOrValue = (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.headers)();\n\n  // Compatibility with Next.js <15\n  return (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_2__.isPromise)(promiseOrValue) ? await promiseOrValue : promiseOrValue;\n}\nconst getHeaders = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(getHeadersImpl);\nasync function getLocaleFromHeaderImpl() {\n  let locale;\n  try {\n    locale = (await getHeaders()).get(_shared_constants_js__WEBPACK_IMPORTED_MODULE_3__.HEADER_LOCALE_NAME) || undefined;\n  } catch (error) {\n    if (error instanceof Error && error.digest === 'DYNAMIC_SERVER_USAGE') {\n      const wrappedError = new Error('Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering', {\n        cause: error\n      });\n      wrappedError.digest = error.digest;\n      throw wrappedError;\n    } else {\n      throw error;\n    }\n  }\n  return locale;\n}\nconst getLocaleFromHeader = (0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(getLocaleFromHeaderImpl);\nasync function getRequestLocale() {\n  return (0,_RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__.getCachedRequestLocale)() || (await getLocaleFromHeader());\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocaleCache.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocaleCache.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCachedRequestLocale: () => (/* binding */ getCachedRequestLocale),\n/* harmony export */   setCachedRequestLocale: () => (/* binding */ setCachedRequestLocale)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n\n\n// See https://github.com/vercel/next.js/discussions/58862\nfunction getCacheImpl() {\n  const value = {\n    locale: undefined\n  };\n  return value;\n}\nconst getCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getCacheImpl);\nfunction getCachedRequestLocale() {\n  return getCache().locale;\n}\nfunction setCachedRequestLocale(locale) {\n  getCache().locale = locale;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZUNhY2hlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4Qjs7QUFFOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsNENBQUs7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUUwRCIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXFlFWl9UZWNoXFxZRVpfSG9tZVxcWUVaSG9tZV9GRVxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcZGV2ZWxvcG1lbnRcXHNlcnZlclxccmVhY3Qtc2VydmVyXFxSZXF1ZXN0TG9jYWxlQ2FjaGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2FjaGUgfSBmcm9tICdyZWFjdCc7XG5cbi8vIFNlZSBodHRwczovL2dpdGh1Yi5jb20vdmVyY2VsL25leHQuanMvZGlzY3Vzc2lvbnMvNTg4NjJcbmZ1bmN0aW9uIGdldENhY2hlSW1wbCgpIHtcbiAgY29uc3QgdmFsdWUgPSB7XG4gICAgbG9jYWxlOiB1bmRlZmluZWRcbiAgfTtcbiAgcmV0dXJuIHZhbHVlO1xufVxuY29uc3QgZ2V0Q2FjaGUgPSBjYWNoZShnZXRDYWNoZUltcGwpO1xuZnVuY3Rpb24gZ2V0Q2FjaGVkUmVxdWVzdExvY2FsZSgpIHtcbiAgcmV0dXJuIGdldENhY2hlKCkubG9jYWxlO1xufVxuZnVuY3Rpb24gc2V0Q2FjaGVkUmVxdWVzdExvY2FsZShsb2NhbGUpIHtcbiAgZ2V0Q2FjaGUoKS5sb2NhbGUgPSBsb2NhbGU7XG59XG5cbmV4cG9ydCB7IGdldENhY2hlZFJlcXVlc3RMb2NhbGUsIHNldENhY2hlZFJlcXVlc3RMb2NhbGUgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocaleCache.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getConfig)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var use_intl_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! use-intl/core */ \"(rsc)/./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/utils.js\");\n/* harmony import */ var _RequestLocale_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./RequestLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/RequestLocale.js\");\n/* harmony import */ var next_intl_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/config */ \"(rsc)/./i18n/request.ts\");\n/* harmony import */ var _validateLocale_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./validateLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/validateLocale.js\");\n\n\n\n\n\n\n\n// This is automatically inherited by `NextIntlClientProvider` if\n// the component is rendered from a Server Component\nfunction getDefaultTimeZoneImpl() {\n  return Intl.DateTimeFormat().resolvedOptions().timeZone;\n}\nconst getDefaultTimeZone = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getDefaultTimeZoneImpl);\nasync function receiveRuntimeConfigImpl(getConfig, localeOverride) {\n  if (typeof getConfig !== 'function') {\n    throw new Error(`Invalid i18n request configuration detected.\n\nPlease verify that:\n1. In case you've specified a custom location in your Next.js config, make sure that the path is correct.\n2. You have a default export in your i18n request configuration file.\n\nSee also: https://next-intl.dev/docs/usage/configuration#i18n-request\n`);\n  }\n  const params = {\n    locale: localeOverride,\n    // In case the consumer doesn't read `params.locale` and instead provides the\n    // `locale` (either in a single-language workflow or because the locale is\n    // read from the user settings), don't attempt to read the request locale.\n    get requestLocale() {\n      return localeOverride ? Promise.resolve(localeOverride) : (0,_RequestLocale_js__WEBPACK_IMPORTED_MODULE_2__.getRequestLocale)();\n    }\n  };\n  let result = getConfig(params);\n  if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.isPromise)(result)) {\n    result = await result;\n  }\n  if (!result.locale) {\n    throw new Error('No locale was returned from `getRequestConfig`.\\n\\nSee https://next-intl.dev/docs/usage/configuration#i18n-request');\n  }\n  {\n    (0,_validateLocale_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(result.locale);\n  }\n  return result;\n}\nconst receiveRuntimeConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(receiveRuntimeConfigImpl);\nconst getFormatters = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(use_intl_core__WEBPACK_IMPORTED_MODULE_5__.b);\nconst getCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(use_intl_core__WEBPACK_IMPORTED_MODULE_5__.d);\nasync function getConfigImpl(localeOverride) {\n  const runtimeConfig = await receiveRuntimeConfig(next_intl_config__WEBPACK_IMPORTED_MODULE_1__[\"default\"], localeOverride);\n  return {\n    ...(0,use_intl_core__WEBPACK_IMPORTED_MODULE_5__.i)(runtimeConfig),\n    _formatters: getFormatters(getCache()),\n    timeZone: runtimeConfig.timeZone || getDefaultTimeZone()\n  };\n}\nconst getConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getConfigImpl);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfigNow.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getConfigNow.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getConfigNow)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n\nasync function getConfigNowImpl(locale) {\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(locale);\n  return config.now;\n}\nconst getConfigNow = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getConfigNowImpl);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Q29uZmlnTm93LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QjtBQUNTOztBQUV2QztBQUNBLHVCQUF1Qix5REFBUztBQUNoQztBQUNBO0FBQ0EscUJBQXFCLDRDQUFLOztBQUVTIiwic291cmNlcyI6WyJEOlxcUHJvamVjdFxcWUVaX1RlY2hcXFlFWl9Ib21lXFxZRVpIb21lX0ZFXFxub2RlX21vZHVsZXNcXG5leHQtaW50bFxcZGlzdFxcZXNtXFxkZXZlbG9wbWVudFxcc2VydmVyXFxyZWFjdC1zZXJ2ZXJcXGdldENvbmZpZ05vdy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjYWNoZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBnZXRDb25maWcgZnJvbSAnLi9nZXRDb25maWcuanMnO1xuXG5hc3luYyBmdW5jdGlvbiBnZXRDb25maWdOb3dJbXBsKGxvY2FsZSkge1xuICBjb25zdCBjb25maWcgPSBhd2FpdCBnZXRDb25maWcobG9jYWxlKTtcbiAgcmV0dXJuIGNvbmZpZy5ub3c7XG59XG5jb25zdCBnZXRDb25maWdOb3cgPSBjYWNoZShnZXRDb25maWdOb3dJbXBsKTtcblxuZXhwb3J0IHsgZ2V0Q29uZmlnTm93IGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfigNow.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getFormats.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getFormats.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getFormats)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n\nasync function getFormatsCachedImpl() {\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n  return config.formats;\n}\nconst getFormats = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getFormatsCachedImpl);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Rm9ybWF0cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEI7QUFDUzs7QUFFdkM7QUFDQSx1QkFBdUIseURBQVM7QUFDaEM7QUFDQTtBQUNBLG1CQUFtQiw0Q0FBSzs7QUFFUyIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXFlFWl9UZWNoXFxZRVpfSG9tZVxcWUVaSG9tZV9GRVxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcZGV2ZWxvcG1lbnRcXHNlcnZlclxccmVhY3Qtc2VydmVyXFxnZXRGb3JtYXRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNhY2hlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IGdldENvbmZpZyBmcm9tICcuL2dldENvbmZpZy5qcyc7XG5cbmFzeW5jIGZ1bmN0aW9uIGdldEZvcm1hdHNDYWNoZWRJbXBsKCkge1xuICBjb25zdCBjb25maWcgPSBhd2FpdCBnZXRDb25maWcoKTtcbiAgcmV0dXJuIGNvbmZpZy5mb3JtYXRzO1xufVxuY29uc3QgZ2V0Rm9ybWF0cyA9IGNhY2hlKGdldEZvcm1hdHNDYWNoZWRJbXBsKTtcblxuZXhwb3J0IHsgZ2V0Rm9ybWF0cyBhcyBkZWZhdWx0IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getFormats.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getLocale.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getLocale.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getLocaleCached)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n\nasync function getLocaleCachedImpl() {\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n  return config.locale;\n}\nconst getLocaleCached = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getLocaleCachedImpl);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0TG9jYWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QjtBQUNTOztBQUV2QztBQUNBLHVCQUF1Qix5REFBUztBQUNoQztBQUNBO0FBQ0Esd0JBQXdCLDRDQUFLOztBQUVTIiwic291cmNlcyI6WyJEOlxcUHJvamVjdFxcWUVaX1RlY2hcXFlFWl9Ib21lXFxZRVpIb21lX0ZFXFxub2RlX21vZHVsZXNcXG5leHQtaW50bFxcZGlzdFxcZXNtXFxkZXZlbG9wbWVudFxcc2VydmVyXFxyZWFjdC1zZXJ2ZXJcXGdldExvY2FsZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjYWNoZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBnZXRDb25maWcgZnJvbSAnLi9nZXRDb25maWcuanMnO1xuXG5hc3luYyBmdW5jdGlvbiBnZXRMb2NhbGVDYWNoZWRJbXBsKCkge1xuICBjb25zdCBjb25maWcgPSBhd2FpdCBnZXRDb25maWcoKTtcbiAgcmV0dXJuIGNvbmZpZy5sb2NhbGU7XG59XG5jb25zdCBnZXRMb2NhbGVDYWNoZWQgPSBjYWNoZShnZXRMb2NhbGVDYWNoZWRJbXBsKTtcblxuZXhwb3J0IHsgZ2V0TG9jYWxlQ2FjaGVkIGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getMessages.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getMessages.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getMessages),\n/* harmony export */   getMessagesFromConfig: () => (/* binding */ getMessagesFromConfig)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n\nfunction getMessagesFromConfig(config) {\n  if (!config.messages) {\n    throw new Error('No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages');\n  }\n  return config.messages;\n}\nasync function getMessagesCachedImpl(locale) {\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(locale);\n  return getMessagesFromConfig(config);\n}\nconst getMessagesCached = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getMessagesCachedImpl);\nasync function getMessages(opts) {\n  return getMessagesCached(opts?.locale);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0TWVzc2FnZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE4QjtBQUNTOztBQUV2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1Qix5REFBUztBQUNoQztBQUNBO0FBQ0EsMEJBQTBCLDRDQUFLO0FBQy9CO0FBQ0E7QUFDQTs7QUFFeUQiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0XFxZRVpfVGVjaFxcWUVaX0hvbWVcXFlFWkhvbWVfRkVcXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXGRldmVsb3BtZW50XFxzZXJ2ZXJcXHJlYWN0LXNlcnZlclxcZ2V0TWVzc2FnZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2FjaGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgZ2V0Q29uZmlnIGZyb20gJy4vZ2V0Q29uZmlnLmpzJztcblxuZnVuY3Rpb24gZ2V0TWVzc2FnZXNGcm9tQ29uZmlnKGNvbmZpZykge1xuICBpZiAoIWNvbmZpZy5tZXNzYWdlcykge1xuICAgIHRocm93IG5ldyBFcnJvcignTm8gbWVzc2FnZXMgZm91bmQuIEhhdmUgeW91IGNvbmZpZ3VyZWQgdGhlbSBjb3JyZWN0bHk/IFNlZSBodHRwczovL25leHQtaW50bC5kZXYvZG9jcy9jb25maWd1cmF0aW9uI21lc3NhZ2VzJyk7XG4gIH1cbiAgcmV0dXJuIGNvbmZpZy5tZXNzYWdlcztcbn1cbmFzeW5jIGZ1bmN0aW9uIGdldE1lc3NhZ2VzQ2FjaGVkSW1wbChsb2NhbGUpIHtcbiAgY29uc3QgY29uZmlnID0gYXdhaXQgZ2V0Q29uZmlnKGxvY2FsZSk7XG4gIHJldHVybiBnZXRNZXNzYWdlc0Zyb21Db25maWcoY29uZmlnKTtcbn1cbmNvbnN0IGdldE1lc3NhZ2VzQ2FjaGVkID0gY2FjaGUoZ2V0TWVzc2FnZXNDYWNoZWRJbXBsKTtcbmFzeW5jIGZ1bmN0aW9uIGdldE1lc3NhZ2VzKG9wdHMpIHtcbiAgcmV0dXJuIGdldE1lc3NhZ2VzQ2FjaGVkKG9wdHM/LmxvY2FsZSk7XG59XG5cbmV4cG9ydCB7IGdldE1lc3NhZ2VzIGFzIGRlZmF1bHQsIGdldE1lc3NhZ2VzRnJvbUNvbmZpZyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getMessages.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getRequestConfig)\n/* harmony export */ });\n/**\n * Should be called in `i18n/request.ts` to create the configuration for the current request.\n */\nfunction getRequestConfig(createRequestConfig) {\n  return createRequestConfig;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0UmVxdWVzdENvbmZpZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV1QyIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXFlFWl9UZWNoXFxZRVpfSG9tZVxcWUVaSG9tZV9GRVxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcZGV2ZWxvcG1lbnRcXHNlcnZlclxccmVhY3Qtc2VydmVyXFxnZXRSZXF1ZXN0Q29uZmlnLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogU2hvdWxkIGJlIGNhbGxlZCBpbiBgaTE4bi9yZXF1ZXN0LnRzYCB0byBjcmVhdGUgdGhlIGNvbmZpZ3VyYXRpb24gZm9yIHRoZSBjdXJyZW50IHJlcXVlc3QuXG4gKi9cbmZ1bmN0aW9uIGdldFJlcXVlc3RDb25maWcoY3JlYXRlUmVxdWVzdENvbmZpZykge1xuICByZXR1cm4gY3JlYXRlUmVxdWVzdENvbmZpZztcbn1cblxuZXhwb3J0IHsgZ2V0UmVxdWVzdENvbmZpZyBhcyBkZWZhdWx0IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getServerTranslator.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getServerTranslator.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getServerTranslator)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var use_intl_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/core */ \"(rsc)/./node_modules/use-intl/dist/esm/development/core.js\");\n\n\n\nfunction getServerTranslatorImpl(config, namespace) {\n  return (0,use_intl_core__WEBPACK_IMPORTED_MODULE_1__.createTranslator)({\n    ...config,\n    namespace\n  });\n}\nvar getServerTranslator = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getServerTranslatorImpl);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0U2VydmVyVHJhbnNsYXRvci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEI7QUFDbUI7O0FBRWpEO0FBQ0EsU0FBUywrREFBZ0I7QUFDekI7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLDBCQUEwQiw0Q0FBSzs7QUFFVyIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXFlFWl9UZWNoXFxZRVpfSG9tZVxcWUVaSG9tZV9GRVxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcZGV2ZWxvcG1lbnRcXHNlcnZlclxccmVhY3Qtc2VydmVyXFxnZXRTZXJ2ZXJUcmFuc2xhdG9yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNhY2hlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgY3JlYXRlVHJhbnNsYXRvciB9IGZyb20gJ3VzZS1pbnRsL2NvcmUnO1xuXG5mdW5jdGlvbiBnZXRTZXJ2ZXJUcmFuc2xhdG9ySW1wbChjb25maWcsIG5hbWVzcGFjZSkge1xuICByZXR1cm4gY3JlYXRlVHJhbnNsYXRvcih7XG4gICAgLi4uY29uZmlnLFxuICAgIG5hbWVzcGFjZVxuICB9KTtcbn1cbnZhciBnZXRTZXJ2ZXJUcmFuc2xhdG9yID0gY2FjaGUoZ2V0U2VydmVyVHJhbnNsYXRvckltcGwpO1xuXG5leHBvcnQgeyBnZXRTZXJ2ZXJUcmFuc2xhdG9yIGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getServerTranslator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getTimeZone.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getTimeZone.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getTimeZone)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n\n\n\nasync function getTimeZoneCachedImpl(locale) {\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(locale);\n  return config.timeZone;\n}\nconst getTimeZoneCached = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getTimeZoneCachedImpl);\nasync function getTimeZone(opts) {\n  return getTimeZoneCached(opts?.locale);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0VGltZVpvbmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQThCO0FBQ1M7O0FBRXZDO0FBQ0EsdUJBQXVCLHlEQUFTO0FBQ2hDO0FBQ0E7QUFDQSwwQkFBMEIsNENBQUs7QUFDL0I7QUFDQTtBQUNBOztBQUVrQyIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXFlFWl9UZWNoXFxZRVpfSG9tZVxcWUVaSG9tZV9GRVxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcZGV2ZWxvcG1lbnRcXHNlcnZlclxccmVhY3Qtc2VydmVyXFxnZXRUaW1lWm9uZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjYWNoZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBnZXRDb25maWcgZnJvbSAnLi9nZXRDb25maWcuanMnO1xuXG5hc3luYyBmdW5jdGlvbiBnZXRUaW1lWm9uZUNhY2hlZEltcGwobG9jYWxlKSB7XG4gIGNvbnN0IGNvbmZpZyA9IGF3YWl0IGdldENvbmZpZyhsb2NhbGUpO1xuICByZXR1cm4gY29uZmlnLnRpbWVab25lO1xufVxuY29uc3QgZ2V0VGltZVpvbmVDYWNoZWQgPSBjYWNoZShnZXRUaW1lWm9uZUNhY2hlZEltcGwpO1xuYXN5bmMgZnVuY3Rpb24gZ2V0VGltZVpvbmUob3B0cykge1xuICByZXR1cm4gZ2V0VGltZVpvbmVDYWNoZWQob3B0cz8ubG9jYWxlKTtcbn1cblxuZXhwb3J0IHsgZ2V0VGltZVpvbmUgYXMgZGVmYXVsdCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getTimeZone.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getTranslations.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/getTranslations.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getTranslations$1)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js\");\n/* harmony import */ var _getServerTranslator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getServerTranslator.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getServerTranslator.js\");\n\n\n\n\n// Maintainer note: `getTranslations` has two different call signatures.\n// We need to define these with function overloads, otherwise TypeScript\n// messes up the return type.\n\n// Call signature 1: `getTranslations(namespace)`\n\n// Call signature 2: `getTranslations({locale, namespace})`\n\n// Implementation\nasync function getTranslations(namespaceOrOpts) {\n  let namespace;\n  let locale;\n  if (typeof namespaceOrOpts === 'string') {\n    namespace = namespaceOrOpts;\n  } else if (namespaceOrOpts) {\n    locale = namespaceOrOpts.locale;\n    namespace = namespaceOrOpts.namespace;\n  }\n  const config = await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(locale);\n  return (0,_getServerTranslator_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(config, namespace);\n}\nvar getTranslations$1 = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(getTranslations);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0VHJhbnNsYXRpb25zLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBOEI7QUFDUztBQUNvQjs7QUFFM0Q7QUFDQTtBQUNBOztBQUVBOztBQUVBLHVDQUF1QyxrQkFBa0I7O0FBRXpEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIseURBQVM7QUFDaEMsU0FBUyxtRUFBbUI7QUFDNUI7QUFDQSx3QkFBd0IsNENBQUs7O0FBRVciLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0XFxZRVpfVGVjaFxcWUVaX0hvbWVcXFlFWkhvbWVfRkVcXG5vZGVfbW9kdWxlc1xcbmV4dC1pbnRsXFxkaXN0XFxlc21cXGRldmVsb3BtZW50XFxzZXJ2ZXJcXHJlYWN0LXNlcnZlclxcZ2V0VHJhbnNsYXRpb25zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNhY2hlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IGdldENvbmZpZyBmcm9tICcuL2dldENvbmZpZy5qcyc7XG5pbXBvcnQgZ2V0U2VydmVyVHJhbnNsYXRvciBmcm9tICcuL2dldFNlcnZlclRyYW5zbGF0b3IuanMnO1xuXG4vLyBNYWludGFpbmVyIG5vdGU6IGBnZXRUcmFuc2xhdGlvbnNgIGhhcyB0d28gZGlmZmVyZW50IGNhbGwgc2lnbmF0dXJlcy5cbi8vIFdlIG5lZWQgdG8gZGVmaW5lIHRoZXNlIHdpdGggZnVuY3Rpb24gb3ZlcmxvYWRzLCBvdGhlcndpc2UgVHlwZVNjcmlwdFxuLy8gbWVzc2VzIHVwIHRoZSByZXR1cm4gdHlwZS5cblxuLy8gQ2FsbCBzaWduYXR1cmUgMTogYGdldFRyYW5zbGF0aW9ucyhuYW1lc3BhY2UpYFxuXG4vLyBDYWxsIHNpZ25hdHVyZSAyOiBgZ2V0VHJhbnNsYXRpb25zKHtsb2NhbGUsIG5hbWVzcGFjZX0pYFxuXG4vLyBJbXBsZW1lbnRhdGlvblxuYXN5bmMgZnVuY3Rpb24gZ2V0VHJhbnNsYXRpb25zKG5hbWVzcGFjZU9yT3B0cykge1xuICBsZXQgbmFtZXNwYWNlO1xuICBsZXQgbG9jYWxlO1xuICBpZiAodHlwZW9mIG5hbWVzcGFjZU9yT3B0cyA9PT0gJ3N0cmluZycpIHtcbiAgICBuYW1lc3BhY2UgPSBuYW1lc3BhY2VPck9wdHM7XG4gIH0gZWxzZSBpZiAobmFtZXNwYWNlT3JPcHRzKSB7XG4gICAgbG9jYWxlID0gbmFtZXNwYWNlT3JPcHRzLmxvY2FsZTtcbiAgICBuYW1lc3BhY2UgPSBuYW1lc3BhY2VPck9wdHMubmFtZXNwYWNlO1xuICB9XG4gIGNvbnN0IGNvbmZpZyA9IGF3YWl0IGdldENvbmZpZyhsb2NhbGUpO1xuICByZXR1cm4gZ2V0U2VydmVyVHJhbnNsYXRvcihjb25maWcsIG5hbWVzcGFjZSk7XG59XG52YXIgZ2V0VHJhbnNsYXRpb25zJDEgPSBjYWNoZShnZXRUcmFuc2xhdGlvbnMpO1xuXG5leHBvcnQgeyBnZXRUcmFuc2xhdGlvbnMkMSBhcyBkZWZhdWx0IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getTranslations.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/validateLocale.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-server/validateLocale.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ validateLocale)\n/* harmony export */ });\nfunction validateLocale(locale) {\n  try {\n    const constructed = new Intl.Locale(locale);\n    if (!constructed.language) {\n      throw new Error('Language is required');\n    }\n  } catch {\n    console.error(`An invalid locale was provided: \"${locale}\"\\nPlease ensure you're using a valid Unicode locale identifier (e.g. \"en-US\").`);\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NlcnZlci9yZWFjdC1zZXJ2ZXIvdmFsaWRhdGVMb2NhbGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSixzREFBc0QsT0FBTztBQUM3RDtBQUNBOztBQUVxQyIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXFlFWl9UZWNoXFxZRVpfSG9tZVxcWUVaSG9tZV9GRVxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcZGV2ZWxvcG1lbnRcXHNlcnZlclxccmVhY3Qtc2VydmVyXFx2YWxpZGF0ZUxvY2FsZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiB2YWxpZGF0ZUxvY2FsZShsb2NhbGUpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBjb25zdHJ1Y3RlZCA9IG5ldyBJbnRsLkxvY2FsZShsb2NhbGUpO1xuICAgIGlmICghY29uc3RydWN0ZWQubGFuZ3VhZ2UpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignTGFuZ3VhZ2UgaXMgcmVxdWlyZWQnKTtcbiAgICB9XG4gIH0gY2F0Y2gge1xuICAgIGNvbnNvbGUuZXJyb3IoYEFuIGludmFsaWQgbG9jYWxlIHdhcyBwcm92aWRlZDogXCIke2xvY2FsZX1cIlxcblBsZWFzZSBlbnN1cmUgeW91J3JlIHVzaW5nIGEgdmFsaWQgVW5pY29kZSBsb2NhbGUgaWRlbnRpZmllciAoZS5nLiBcImVuLVVTXCIpLmApO1xuICB9XG59XG5cbmV4cG9ydCB7IHZhbGlkYXRlTG9jYWxlIGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/validateLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\node_modules\\\\next-intl\\\\dist\\\\esm\\\\development\\\\shared\\\\NextIntlClientProvider.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome_FE\\node_modules\\next-intl\\dist\\esm\\development\\shared\\NextIntlClientProvider.js",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/shared/constants.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/constants.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HEADER_LOCALE_NAME: () => (/* binding */ HEADER_LOCALE_NAME)\n/* harmony export */ });\n// Used to read the locale from the middleware\nconst HEADER_LOCALE_NAME = 'X-NEXT-INTL-LOCALE';\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NoYXJlZC9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7O0FBRThCIiwic291cmNlcyI6WyJEOlxcUHJvamVjdFxcWUVaX1RlY2hcXFlFWl9Ib21lXFxZRVpIb21lX0ZFXFxub2RlX21vZHVsZXNcXG5leHQtaW50bFxcZGlzdFxcZXNtXFxkZXZlbG9wbWVudFxcc2hhcmVkXFxjb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gVXNlZCB0byByZWFkIHRoZSBsb2NhbGUgZnJvbSB0aGUgbWlkZGxld2FyZVxuY29uc3QgSEVBREVSX0xPQ0FMRV9OQU1FID0gJ1gtTkVYVC1JTlRMLUxPQ0FMRSc7XG5cbmV4cG9ydCB7IEhFQURFUl9MT0NBTEVfTkFNRSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/shared/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/shared/use.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/use.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ use)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n\n\n// @ts-expect-error -- Ooof, Next.js doesn't make this easy.\n// `use` is only available in React 19 canary, but we can\n// use it in Next.js already as Next.js \"vendors\" a fixed\n// version of React. However, if we'd simply put `use` in\n// ESM code, then the build doesn't work since React does\n// not export `use` officially. Therefore, we have to use\n// something that is not statically analyzable. Once React\n// 19 is out, we can remove this in the next major version.\nvar use = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))['use'.trim()];\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NoYXJlZC91c2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQStCOztBQUUvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSx5TEFBSzs7QUFFVyIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXFlFWl9UZWNoXFxZRVpfSG9tZVxcWUVaSG9tZV9GRVxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcZGV2ZWxvcG1lbnRcXHNoYXJlZFxcdXNlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIHJlYWN0IGZyb20gJ3JlYWN0JztcblxuLy8gQHRzLWV4cGVjdC1lcnJvciAtLSBPb29mLCBOZXh0LmpzIGRvZXNuJ3QgbWFrZSB0aGlzIGVhc3kuXG4vLyBgdXNlYCBpcyBvbmx5IGF2YWlsYWJsZSBpbiBSZWFjdCAxOSBjYW5hcnksIGJ1dCB3ZSBjYW5cbi8vIHVzZSBpdCBpbiBOZXh0LmpzIGFscmVhZHkgYXMgTmV4dC5qcyBcInZlbmRvcnNcIiBhIGZpeGVkXG4vLyB2ZXJzaW9uIG9mIFJlYWN0LiBIb3dldmVyLCBpZiB3ZSdkIHNpbXBseSBwdXQgYHVzZWAgaW5cbi8vIEVTTSBjb2RlLCB0aGVuIHRoZSBidWlsZCBkb2Vzbid0IHdvcmsgc2luY2UgUmVhY3QgZG9lc1xuLy8gbm90IGV4cG9ydCBgdXNlYCBvZmZpY2lhbGx5LiBUaGVyZWZvcmUsIHdlIGhhdmUgdG8gdXNlXG4vLyBzb21ldGhpbmcgdGhhdCBpcyBub3Qgc3RhdGljYWxseSBhbmFseXphYmxlLiBPbmNlIFJlYWN0XG4vLyAxOSBpcyBvdXQsIHdlIGNhbiByZW1vdmUgdGhpcyBpbiB0aGUgbmV4dCBtYWpvciB2ZXJzaW9uLlxudmFyIHVzZSA9IHJlYWN0Wyd1c2UnLnRyaW0oKV07XG5cbmV4cG9ydCB7IHVzZSBhcyBkZWZhdWx0IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/shared/use.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/development/shared/utils.js":
/*!*********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/utils.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLocaleAsPrefix: () => (/* binding */ getLocaleAsPrefix),\n/* harmony export */   getLocalePrefix: () => (/* binding */ getLocalePrefix),\n/* harmony export */   getLocalizedTemplate: () => (/* binding */ getLocalizedTemplate),\n/* harmony export */   getSortedPathnames: () => (/* binding */ getSortedPathnames),\n/* harmony export */   hasPathnamePrefixed: () => (/* binding */ hasPathnamePrefixed),\n/* harmony export */   isLocalizableHref: () => (/* binding */ isLocalizableHref),\n/* harmony export */   isPromise: () => (/* binding */ isPromise),\n/* harmony export */   matchesPathname: () => (/* binding */ matchesPathname),\n/* harmony export */   normalizeTrailingSlash: () => (/* binding */ normalizeTrailingSlash),\n/* harmony export */   prefixPathname: () => (/* binding */ prefixPathname),\n/* harmony export */   templateToRegex: () => (/* binding */ templateToRegex),\n/* harmony export */   unprefixPathname: () => (/* binding */ unprefixPathname)\n/* harmony export */ });\nfunction isRelativeHref(href) {\n  const pathname = typeof href === 'object' ? href.pathname : href;\n  return pathname != null && !pathname.startsWith('/');\n}\nfunction isLocalHref(href) {\n  if (typeof href === 'object') {\n    return href.host == null && href.hostname == null;\n  } else {\n    const hasProtocol = /^[a-z]+:/i.test(href);\n    return !hasProtocol;\n  }\n}\nfunction isLocalizableHref(href) {\n  return isLocalHref(href) && !isRelativeHref(href);\n}\nfunction unprefixPathname(pathname, prefix) {\n  return pathname.replace(new RegExp(`^${prefix}`), '') || '/';\n}\nfunction prefixPathname(prefix, pathname) {\n  let localizedHref = prefix;\n\n  // Avoid trailing slashes\n  if (/^\\/(\\?.*)?$/.test(pathname)) {\n    pathname = pathname.slice(1);\n  }\n  localizedHref += pathname;\n  return localizedHref;\n}\nfunction hasPathnamePrefixed(prefix, pathname) {\n  return pathname === prefix || pathname.startsWith(`${prefix}/`);\n}\nfunction hasTrailingSlash() {\n  try {\n    // Provided via `env` setting in `next.config.js` via the plugin\n    return process.env._next_intl_trailing_slash === 'true';\n  } catch {\n    return false;\n  }\n}\nfunction getLocalizedTemplate(pathnameConfig, locale, internalTemplate) {\n  return typeof pathnameConfig === 'string' ? pathnameConfig : pathnameConfig[locale] || internalTemplate;\n}\nfunction normalizeTrailingSlash(pathname) {\n  const trailingSlash = hasTrailingSlash();\n  if (pathname !== '/') {\n    const pathnameEndsWithSlash = pathname.endsWith('/');\n    if (trailingSlash && !pathnameEndsWithSlash) {\n      pathname += '/';\n    } else if (!trailingSlash && pathnameEndsWithSlash) {\n      pathname = pathname.slice(0, -1);\n    }\n  }\n  return pathname;\n}\nfunction matchesPathname(/** E.g. `/users/[userId]-[userName]` */\ntemplate, /** E.g. `/users/23-jane` */\npathname) {\n  const normalizedTemplate = normalizeTrailingSlash(template);\n  const normalizedPathname = normalizeTrailingSlash(pathname);\n  const regex = templateToRegex(normalizedTemplate);\n  return regex.test(normalizedPathname);\n}\nfunction getLocalePrefix(locale, localePrefix) {\n  return localePrefix.mode !== 'never' && localePrefix.prefixes?.[locale] ||\n  // We return a prefix even if `mode: 'never'`. It's up to the consumer\n  // to decide to use it or not.\n  getLocaleAsPrefix(locale);\n}\nfunction getLocaleAsPrefix(locale) {\n  return '/' + locale;\n}\nfunction templateToRegex(template) {\n  const regexPattern = template\n  // Replace optional catchall ('[[...slug]]')\n  .replace(/\\[\\[(\\.\\.\\.[^\\]]+)\\]\\]/g, '?(.*)')\n  // Replace catchall ('[...slug]')\n  .replace(/\\[(\\.\\.\\.[^\\]]+)\\]/g, '(.+)')\n  // Replace regular parameter ('[slug]')\n  .replace(/\\[([^\\]]+)\\]/g, '([^/]+)');\n  return new RegExp(`^${regexPattern}$`);\n}\nfunction isOptionalCatchAllSegment(pathname) {\n  return pathname.includes('[[...');\n}\nfunction isCatchAllSegment(pathname) {\n  return pathname.includes('[...');\n}\nfunction isDynamicSegment(pathname) {\n  return pathname.includes('[');\n}\nfunction comparePathnamePairs(a, b) {\n  const pathA = a.split('/');\n  const pathB = b.split('/');\n  const maxLength = Math.max(pathA.length, pathB.length);\n  for (let i = 0; i < maxLength; i++) {\n    const segmentA = pathA[i];\n    const segmentB = pathB[i];\n\n    // If one of the paths ends, prioritize the shorter path\n    if (!segmentA && segmentB) return -1;\n    if (segmentA && !segmentB) return 1;\n    if (!segmentA && !segmentB) continue;\n\n    // Prioritize static segments over dynamic segments\n    if (!isDynamicSegment(segmentA) && isDynamicSegment(segmentB)) return -1;\n    if (isDynamicSegment(segmentA) && !isDynamicSegment(segmentB)) return 1;\n\n    // Prioritize non-catch-all segments over catch-all segments\n    if (!isCatchAllSegment(segmentA) && isCatchAllSegment(segmentB)) return -1;\n    if (isCatchAllSegment(segmentA) && !isCatchAllSegment(segmentB)) return 1;\n\n    // Prioritize non-optional catch-all segments over optional catch-all segments\n    if (!isOptionalCatchAllSegment(segmentA) && isOptionalCatchAllSegment(segmentB)) {\n      return -1;\n    }\n    if (isOptionalCatchAllSegment(segmentA) && !isOptionalCatchAllSegment(segmentB)) {\n      return 1;\n    }\n    if (segmentA === segmentB) continue;\n  }\n\n  // Both pathnames are completely static\n  return 0;\n}\nfunction getSortedPathnames(pathnames) {\n  return pathnames.sort(comparePathnamePairs);\n}\nfunction isPromise(value) {\n  // https://github.com/amannn/next-intl/issues/1711\n  return typeof value.then === 'function';\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/development/shared/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/navigation/react-client/createNavigation.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/react-client/createNavigation.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ createNavigation)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_intl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/esm/development/react.js\");\n/* harmony import */ var _shared_createSharedNavigationFns_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../shared/createSharedNavigationFns.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js\");\n/* harmony import */ var _shared_syncLocaleCookie_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../shared/syncLocaleCookie.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/syncLocaleCookie.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js\");\n/* harmony import */ var _useBasePathname_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useBasePathname.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/react-client/useBasePathname.js\");\n\n\n\n\n\n\n\n\nfunction createNavigation(routing) {\n  const {\n    Link,\n    config,\n    getPathname,\n    ...redirects\n  } = (0,_shared_createSharedNavigationFns_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(use_intl__WEBPACK_IMPORTED_MODULE_3__.useLocale, routing);\n\n  /** @see https://next-intl.dev/docs/routing/navigation#usepathname */\n  function usePathname$1() {\n    const pathname = (0,_useBasePathname_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(config);\n    const locale = (0,use_intl__WEBPACK_IMPORTED_MODULE_3__.useLocale)();\n\n    // @ts-expect-error -- Mirror the behavior from Next.js, where `null` is returned when `usePathname` is used outside of Next, but the types indicate that a string is always returned.\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => pathname &&\n    // @ts-expect-error -- This is fine\n    config.pathnames ? (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_5__.getRoute)(locale, pathname,\n    // @ts-expect-error -- This is fine\n    config.pathnames) : pathname, [locale, pathname]);\n  }\n  function useRouter$1() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    const curLocale = (0,use_intl__WEBPACK_IMPORTED_MODULE_3__.useLocale)();\n    const nextPathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.usePathname)();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => {\n      function createHandler(fn) {\n        return function handler(href, options) {\n          const {\n            locale: nextLocale,\n            ...rest\n          } = options || {};\n          const pathname = getPathname({\n            href,\n            locale: nextLocale || curLocale\n          });\n          const args = [pathname];\n          if (Object.keys(rest).length > 0) {\n            // @ts-expect-error -- This is fine\n            args.push(rest);\n          }\n          fn(...args);\n          (0,_shared_syncLocaleCookie_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(config.localeCookie, nextPathname, curLocale, nextLocale);\n        };\n      }\n      return {\n        ...router,\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        push: createHandler(router.push),\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        replace: createHandler(router.replace),\n        /** @see https://next-intl.dev/docs/routing/navigation#userouter */\n        prefetch: createHandler(router.prefetch)\n      };\n    }, [curLocale, nextPathname, router]);\n  }\n  return {\n    ...redirects,\n    Link,\n    usePathname: usePathname$1,\n    useRouter: useRouter$1,\n    getPathname\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/navigation/react-client/createNavigation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/navigation/react-client/useBasePathname.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/react-client/useBasePathname.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useBasePathname)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/esm/development/react.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/shared/utils.js\");\n\n\n\n\n\nfunction useBasePathname(config) {\n  // The types aren't entirely correct here. Outside of Next.js\n  // `useParams` can be called, but the return type is `null`.\n\n  // Notes on `useNextPathname`:\n  // - Types aren't entirely correct. Outside of Next.js the\n  //   hook will return `null` (e.g. unit tests)\n  // - A base path is stripped from the result\n  // - Rewrites *are* taken into account (i.e. the pathname\n  //   that the user sees in the browser is returned)\n  const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.usePathname)();\n  const locale = (0,use_intl__WEBPACK_IMPORTED_MODULE_2__.useLocale)();\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => {\n    if (!pathname) return pathname;\n    let unlocalizedPathname = pathname;\n    const prefix = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.getLocalePrefix)(locale, config.localePrefix);\n    const isPathnamePrefixed = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.hasPathnamePrefixed)(prefix, pathname);\n    if (isPathnamePrefixed) {\n      unlocalizedPathname = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.unprefixPathname)(pathname, prefix);\n    } else if (config.localePrefix.mode === 'as-needed' && config.localePrefix.prefixes) {\n      // Workaround for https://github.com/vercel/next.js/issues/73085\n      const localeAsPrefix = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.getLocaleAsPrefix)(locale);\n      if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.hasPathnamePrefixed)(localeAsPrefix, pathname)) {\n        unlocalizedPathname = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.unprefixPathname)(pathname, localeAsPrefix);\n      }\n    }\n    return unlocalizedPathname;\n  }, [config.localePrefix, locale, pathname]);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/navigation/react-client/useBasePathname.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BaseLink$1)\n/* harmony export */ });\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/esm/development/react.js\");\n/* harmony import */ var _syncLocaleCookie_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./syncLocaleCookie.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/syncLocaleCookie.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction BaseLink({ href, locale, localeCookie, onClick, prefetch, ...rest }, ref) {\n    const curLocale = (0,use_intl__WEBPACK_IMPORTED_MODULE_4__.useLocale)();\n    const isChangingLocale = locale != null && locale !== curLocale;\n    // The types aren't entirely correct here. Outside of Next.js\n    // `useParams` can be called, but the return type is `null`.\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    function onLinkClick(event) {\n        (0,_syncLocaleCookie_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(localeCookie, pathname, curLocale, locale);\n        if (onClick) onClick(event);\n    }\n    if (isChangingLocale) {\n        if (prefetch && \"development\" !== 'production') {\n            console.error('The `prefetch` prop is currently not supported when using the `locale` prop on `Link` to switch the locale.`');\n        }\n        prefetch = false;\n    }\n    // Somehow the types for `next/link` don't work as expected\n    // when `moduleResolution: \"nodenext\"` is used.\n    const Link = next_link__WEBPACK_IMPORTED_MODULE_0__;\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(Link, {\n        ref: ref,\n        href: href,\n        hrefLang: isChangingLocale ? locale : undefined,\n        onClick: onLinkClick,\n        prefetch: prefetch,\n        ...rest\n    });\n}\nvar BaseLink$1 = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(BaseLink);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ createSharedNavigationFns)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _routing_config_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../routing/config.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/routing/config.js\");\n/* harmony import */ var _shared_use_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../shared/use.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/shared/use.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/shared/utils.js\");\n/* harmony import */ var _BaseLink_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./BaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n\n\n\n\n\n\n/**\n * Shared implementations for `react-server` and `react-client`\n */\nfunction createSharedNavigationFns(getLocale, routing) {\n  const config = (0,_routing_config_js__WEBPACK_IMPORTED_MODULE_3__.receiveRoutingConfig)(routing || {});\n  {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.validateReceivedConfig)(config);\n  }\n  const pathnames = config.pathnames;\n  function Link({\n    href,\n    locale,\n    ...rest\n  }, ref) {\n    let pathname, params;\n    if (typeof href === 'object') {\n      pathname = href.pathname;\n      // @ts-expect-error -- This is ok\n      params = href.params;\n    } else {\n      pathname = href;\n    }\n\n    // @ts-expect-error -- This is ok\n    const isLocalizable = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_5__.isLocalizableHref)(href);\n    const localePromiseOrValue = getLocale();\n    const curLocale = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_5__.isPromise)(localePromiseOrValue) ? (0,_shared_use_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(localePromiseOrValue) : localePromiseOrValue;\n    const finalPathname = isLocalizable ? getPathname({\n      locale: locale || curLocale,\n      // @ts-expect-error -- This is ok\n      href: pathnames == null ? pathname : {\n        pathname,\n        params\n      }\n    }, locale != null || undefined) : pathname;\n    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_BaseLink_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n      ref: ref\n      // @ts-expect-error -- This is ok\n      ,\n      href: typeof href === 'object' ? {\n        ...href,\n        pathname: finalPathname\n      } : finalPathname,\n      locale: locale,\n      localeCookie: config.localeCookie,\n      ...rest\n    });\n  }\n  const LinkWithRef = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(Link);\n  function getPathname(args, /** @private Removed in types returned below */\n  _forcePrefix) {\n    const {\n      href,\n      locale\n    } = args;\n    let pathname;\n    if (pathnames == null) {\n      if (typeof href === 'object') {\n        pathname = href.pathname;\n        if (href.query) {\n          pathname += (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.serializeSearchParams)(href.query);\n        }\n      } else {\n        pathname = href;\n      }\n    } else {\n      pathname = (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.compileLocalizedPathname)({\n        locale,\n        // @ts-expect-error -- This is ok\n        ...(0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.normalizeNameOrNameWithParams)(href),\n        // @ts-expect-error -- This is ok\n        pathnames: config.pathnames\n      });\n    }\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.applyPathnamePrefix)(pathname, locale, config, _forcePrefix);\n  }\n  function getRedirectFn(fn) {\n    /** @see https://next-intl.dev/docs/routing/navigation#redirect */\n    return function redirectFn(args, ...rest) {\n      return fn(getPathname(args), ...rest);\n    };\n  }\n  const redirect$1 = getRedirectFn(next_navigation__WEBPACK_IMPORTED_MODULE_0__.redirect);\n  const permanentRedirect$1 = getRedirectFn(next_navigation__WEBPACK_IMPORTED_MODULE_0__.permanentRedirect);\n  return {\n    config,\n    Link: LinkWithRef,\n    redirect: redirect$1,\n    permanentRedirect: permanentRedirect$1,\n    // Remove `_forcePrefix` from public API\n    getPathname: getPathname\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/syncLocaleCookie.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/shared/syncLocaleCookie.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ syncLocaleCookie)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js\");\n\n\n/**\n * We have to keep the cookie value in sync as Next.js might\n * skip a request to the server due to its router cache.\n * See https://github.com/amannn/next-intl/issues/786.\n */\nfunction syncLocaleCookie(localeCookie, pathname, locale, nextLocale) {\n  const isSwitchingLocale = nextLocale !== locale && nextLocale != null;\n  if (!localeCookie || !isSwitchingLocale ||\n  // Theoretical case, we always have a pathname in a real app,\n  // only not when running e.g. in a simulated test environment\n  !pathname) {\n    return;\n  }\n  const basePath = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.getBasePath)(pathname);\n  const hasBasePath = basePath !== '';\n  const defaultPath = hasBasePath ? basePath : '/';\n  const {\n    name,\n    ...rest\n  } = localeCookie;\n  if (!rest.path) {\n    rest.path = defaultPath;\n  }\n  let localeCookieString = `${name}=${nextLocale};`;\n  for (const [key, value] of Object.entries(rest)) {\n    // Map object properties to cookie properties.\n    // Interestingly, `maxAge` corresponds to `max-age`,\n    // while `sameSite` corresponds to `SameSite`.\n    // Also, keys are case-insensitive.\n    const targetKey = key === 'maxAge' ? 'max-age' : key;\n    localeCookieString += `${targetKey}`;\n    if (typeof value !== 'boolean') {\n      localeCookieString += '=' + value;\n    }\n\n    // A trailing \";\" is allowed by browsers\n    localeCookieString += ';';\n  }\n\n  // Note that writing to `document.cookie` doesn't overwrite all\n  // cookies, but only the ones referenced via the name here.\n  document.cookie = localeCookieString;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/syncLocaleCookie.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyPathnamePrefix: () => (/* binding */ applyPathnamePrefix),\n/* harmony export */   compileLocalizedPathname: () => (/* binding */ compileLocalizedPathname),\n/* harmony export */   getBasePath: () => (/* binding */ getBasePath),\n/* harmony export */   getRoute: () => (/* binding */ getRoute),\n/* harmony export */   normalizeNameOrNameWithParams: () => (/* binding */ normalizeNameOrNameWithParams),\n/* harmony export */   serializeSearchParams: () => (/* binding */ serializeSearchParams),\n/* harmony export */   validateReceivedConfig: () => (/* binding */ validateReceivedConfig)\n/* harmony export */ });\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/shared/utils.js\");\n\n\n// Minor false positive: A route that has both optional and\n// required params will allow optional params.\n\n// For `Link`\n\n// For `getPathname` (hence also its consumers: `redirect`, `useRouter`, …)\n\nfunction normalizeNameOrNameWithParams(href) {\n  return typeof href === 'string' ? {\n    pathname: href\n  } : href;\n}\nfunction serializeSearchParams(searchParams) {\n  function serializeValue(value) {\n    return String(value);\n  }\n  const urlSearchParams = new URLSearchParams();\n  for (const [key, value] of Object.entries(searchParams)) {\n    if (Array.isArray(value)) {\n      value.forEach(cur => {\n        urlSearchParams.append(key, serializeValue(cur));\n      });\n    } else {\n      urlSearchParams.set(key, serializeValue(value));\n    }\n  }\n  return '?' + urlSearchParams.toString();\n}\nfunction compileLocalizedPathname({\n  pathname,\n  locale,\n  params,\n  pathnames,\n  query\n}) {\n  function getNamedPath(value) {\n    let namedPath = pathnames[value];\n    if (!namedPath) {\n      // Unknown pathnames\n      namedPath = value;\n    }\n    return namedPath;\n  }\n  function compilePath(namedPath, internalPathname) {\n    const template = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getLocalizedTemplate)(namedPath, locale, internalPathname);\n    let compiled = template;\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        let regexp, replacer;\n        if (Array.isArray(value)) {\n          regexp = `(\\\\[)?\\\\[...${key}\\\\](\\\\])?`;\n          replacer = value.map(v => String(v)).join('/');\n        } else {\n          regexp = `\\\\[${key}\\\\]`;\n          replacer = String(value);\n        }\n        compiled = compiled.replace(new RegExp(regexp, 'g'), replacer);\n      });\n    }\n\n    // Clean up optional catch-all segments that were not replaced\n    compiled = compiled.replace(/\\[\\[\\.\\.\\..+\\]\\]/g, '');\n    compiled = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.normalizeTrailingSlash)(compiled);\n    if (compiled.includes('[')) {\n      // Next.js throws anyway, therefore better provide a more helpful error message\n      throw new Error(`Insufficient params provided for localized pathname.\\nTemplate: ${template}\\nParams: ${JSON.stringify(params)}`);\n    }\n    if (query) {\n      compiled += serializeSearchParams(query);\n    }\n    return compiled;\n  }\n  if (typeof pathname === 'string') {\n    const namedPath = getNamedPath(pathname);\n    const compiled = compilePath(namedPath, pathname);\n    return compiled;\n  } else {\n    const {\n      pathname: internalPathname,\n      ...rest\n    } = pathname;\n    const namedPath = getNamedPath(internalPathname);\n    const compiled = compilePath(namedPath, internalPathname);\n    const result = {\n      ...rest,\n      pathname: compiled\n    };\n    return result;\n  }\n}\nfunction getRoute(locale, pathname, pathnames) {\n  const sortedPathnames = (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getSortedPathnames)(Object.keys(pathnames));\n  const decoded = decodeURI(pathname);\n  for (const internalPathname of sortedPathnames) {\n    const localizedPathnamesOrPathname = pathnames[internalPathname];\n    if (typeof localizedPathnamesOrPathname === 'string') {\n      const localizedPathname = localizedPathnamesOrPathname;\n      if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)(localizedPathname, decoded)) {\n        return internalPathname;\n      }\n    } else {\n      if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getLocalizedTemplate)(localizedPathnamesOrPathname, locale, internalPathname), decoded)) {\n        return internalPathname;\n      }\n    }\n  }\n  return pathname;\n}\nfunction getBasePath(pathname, windowPathname = window.location.pathname) {\n  if (pathname === '/') {\n    return windowPathname;\n  } else {\n    return windowPathname.replace(pathname, '');\n  }\n}\nfunction applyPathnamePrefix(pathname, locale, routing, force) {\n  const {\n    mode\n  } = routing.localePrefix;\n  let shouldPrefix;\n  if (force !== undefined) {\n    shouldPrefix = force;\n  } else if ((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.isLocalizableHref)(pathname)) {\n    if (mode === 'always') {\n      shouldPrefix = true;\n    } else if (mode === 'as-needed') {\n      shouldPrefix = routing.domains ?\n      // Since locales are unique per domain, any locale that is a\n      // default locale of a domain doesn't require a prefix\n      !routing.domains.some(cur => cur.defaultLocale === locale) : locale !== routing.defaultLocale;\n    }\n  }\n  return shouldPrefix ? (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.prefixPathname)((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getLocalePrefix)(locale, routing.localePrefix), pathname) : pathname;\n}\nfunction validateReceivedConfig(config) {\n  if (config.localePrefix?.mode === 'as-needed' && !('defaultLocale' in config)) {\n    throw new Error(\"`localePrefix: 'as-needed' requires a `defaultLocale`.\");\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/navigation/shared/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/react-client/index.js":
/*!***************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/react-client/index.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntlError: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.IntlError),\n/* harmony export */   IntlErrorCode: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.IntlErrorCode),\n/* harmony export */   IntlProvider: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.IntlProvider),\n/* harmony export */   _createCache: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__._createCache),\n/* harmony export */   _createIntlFormatters: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__._createIntlFormatters),\n/* harmony export */   createFormatter: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.createFormatter),\n/* harmony export */   createTranslator: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.createTranslator),\n/* harmony export */   hasLocale: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.hasLocale),\n/* harmony export */   initializeConfig: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.initializeConfig),\n/* harmony export */   useFormatter: () => (/* binding */ useFormatter),\n/* harmony export */   useLocale: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.useLocale),\n/* harmony export */   useMessages: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.useMessages),\n/* harmony export */   useNow: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.useNow),\n/* harmony export */   useTimeZone: () => (/* reexport safe */ use_intl__WEBPACK_IMPORTED_MODULE_0__.useTimeZone),\n/* harmony export */   useTranslations: () => (/* binding */ useTranslations)\n/* harmony export */ });\n/* harmony import */ var use_intl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/esm/development/react.js\");\n/* harmony import */ var use_intl__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/esm/development/index.js\");\n\n\n\n/**\n * This is the main entry file when non-'react-server'\n * environments import from 'next-intl'.\n *\n * Maintainer notes:\n * - Make sure this mirrors the API from 'react-server'.\n * - Make sure everything exported from this module is\n *   supported in all Next.js versions that are supported.\n */\n\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nfunction callHook(name, hook) {\n  return (...args) => {\n    try {\n      return hook(...args);\n    } catch {\n      throw new Error(`Failed to call \\`${name}\\` because the context from \\`NextIntlClientProvider\\` was not found.\n\nThis can happen because:\n1) You intended to render this component as a Server Component, the render\n   failed, and therefore React attempted to render the component on the client\n   instead. If this is the case, check the console for server errors.\n2) You intended to render this component on the client side, but no context was found.\n   Learn more about this error here: https://next-intl.dev/docs/environments/server-client-components#missing-context` );\n    }\n  };\n}\nconst useTranslations = callHook('useTranslations', use_intl__WEBPACK_IMPORTED_MODULE_1__.useTranslations);\nconst useFormatter = callHook('useFormatter', use_intl__WEBPACK_IMPORTED_MODULE_1__.useFormatter);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/react-client/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/routing/config.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/routing/config.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   receiveRoutingConfig: () => (/* binding */ receiveRoutingConfig)\n/* harmony export */ });\nfunction receiveRoutingConfig(input) {\n  return {\n    ...input,\n    localePrefix: receiveLocalePrefixConfig(input.localePrefix),\n    localeCookie: receiveLocaleCookie(input.localeCookie),\n    localeDetection: input.localeDetection ?? true,\n    alternateLinks: input.alternateLinks ?? true\n  };\n}\nfunction receiveLocaleCookie(localeCookie) {\n  return localeCookie ?? true ? {\n    name: 'NEXT_LOCALE',\n    sameSite: 'lax',\n    ...(typeof localeCookie === 'object' && localeCookie)\n\n    // `path` needs to be provided based on a detected base path\n    // that depends on the environment when setting a cookie\n  } : false;\n}\nfunction receiveLocalePrefixConfig(localePrefix) {\n  return typeof localePrefix === 'object' ? localePrefix : {\n    mode: localePrefix || 'always'\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3JvdXRpbmcvY29uZmlnLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVnQyIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXFlFWl9UZWNoXFxZRVpfSG9tZVxcWUVaSG9tZV9GRVxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcZGV2ZWxvcG1lbnRcXHJvdXRpbmdcXGNvbmZpZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiByZWNlaXZlUm91dGluZ0NvbmZpZyhpbnB1dCkge1xuICByZXR1cm4ge1xuICAgIC4uLmlucHV0LFxuICAgIGxvY2FsZVByZWZpeDogcmVjZWl2ZUxvY2FsZVByZWZpeENvbmZpZyhpbnB1dC5sb2NhbGVQcmVmaXgpLFxuICAgIGxvY2FsZUNvb2tpZTogcmVjZWl2ZUxvY2FsZUNvb2tpZShpbnB1dC5sb2NhbGVDb29raWUpLFxuICAgIGxvY2FsZURldGVjdGlvbjogaW5wdXQubG9jYWxlRGV0ZWN0aW9uID8/IHRydWUsXG4gICAgYWx0ZXJuYXRlTGlua3M6IGlucHV0LmFsdGVybmF0ZUxpbmtzID8/IHRydWVcbiAgfTtcbn1cbmZ1bmN0aW9uIHJlY2VpdmVMb2NhbGVDb29raWUobG9jYWxlQ29va2llKSB7XG4gIHJldHVybiBsb2NhbGVDb29raWUgPz8gdHJ1ZSA/IHtcbiAgICBuYW1lOiAnTkVYVF9MT0NBTEUnLFxuICAgIHNhbWVTaXRlOiAnbGF4JyxcbiAgICAuLi4odHlwZW9mIGxvY2FsZUNvb2tpZSA9PT0gJ29iamVjdCcgJiYgbG9jYWxlQ29va2llKVxuXG4gICAgLy8gYHBhdGhgIG5lZWRzIHRvIGJlIHByb3ZpZGVkIGJhc2VkIG9uIGEgZGV0ZWN0ZWQgYmFzZSBwYXRoXG4gICAgLy8gdGhhdCBkZXBlbmRzIG9uIHRoZSBlbnZpcm9ubWVudCB3aGVuIHNldHRpbmcgYSBjb29raWVcbiAgfSA6IGZhbHNlO1xufVxuZnVuY3Rpb24gcmVjZWl2ZUxvY2FsZVByZWZpeENvbmZpZyhsb2NhbGVQcmVmaXgpIHtcbiAgcmV0dXJuIHR5cGVvZiBsb2NhbGVQcmVmaXggPT09ICdvYmplY3QnID8gbG9jYWxlUHJlZml4IDoge1xuICAgIG1vZGU6IGxvY2FsZVByZWZpeCB8fCAnYWx3YXlzJ1xuICB9O1xufVxuXG5leHBvcnQgeyByZWNlaXZlUm91dGluZ0NvbmZpZyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/routing/config.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/routing/defineRouting.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/routing/defineRouting.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ defineRouting)\n/* harmony export */ });\nfunction defineRouting(config) {\n  if (config.domains) {\n    validateUniqueLocalesPerDomain(config.domains);\n  }\n  return config;\n}\nfunction validateUniqueLocalesPerDomain(domains) {\n  const domainsByLocale = new Map();\n  for (const {\n    domain,\n    locales\n  } of domains) {\n    for (const locale of locales) {\n      const localeDomains = domainsByLocale.get(locale) || new Set();\n      localeDomains.add(domain);\n      domainsByLocale.set(locale, localeDomains);\n    }\n  }\n  const duplicateLocaleMessages = Array.from(domainsByLocale.entries()).filter(([, localeDomains]) => localeDomains.size > 1).map(([locale, localeDomains]) => `- \"${locale}\" is used by: ${Array.from(localeDomains).join(', ')}`);\n  if (duplicateLocaleMessages.length > 0) {\n    console.warn('Locales are expected to be unique per domain, but found overlap:\\n' + duplicateLocaleMessages.join('\\n') + '\\nPlease see https://next-intl.dev/docs/routing#domains');\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3JvdXRpbmcvZGVmaW5lUm91dGluZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxS0FBcUssT0FBTyxnQkFBZ0IscUNBQXFDO0FBQ2pPO0FBQ0E7QUFDQTtBQUNBOztBQUVvQyIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXFlFWl9UZWNoXFxZRVpfSG9tZVxcWUVaSG9tZV9GRVxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcZGV2ZWxvcG1lbnRcXHJvdXRpbmdcXGRlZmluZVJvdXRpbmcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gZGVmaW5lUm91dGluZyhjb25maWcpIHtcbiAgaWYgKGNvbmZpZy5kb21haW5zKSB7XG4gICAgdmFsaWRhdGVVbmlxdWVMb2NhbGVzUGVyRG9tYWluKGNvbmZpZy5kb21haW5zKTtcbiAgfVxuICByZXR1cm4gY29uZmlnO1xufVxuZnVuY3Rpb24gdmFsaWRhdGVVbmlxdWVMb2NhbGVzUGVyRG9tYWluKGRvbWFpbnMpIHtcbiAgY29uc3QgZG9tYWluc0J5TG9jYWxlID0gbmV3IE1hcCgpO1xuICBmb3IgKGNvbnN0IHtcbiAgICBkb21haW4sXG4gICAgbG9jYWxlc1xuICB9IG9mIGRvbWFpbnMpIHtcbiAgICBmb3IgKGNvbnN0IGxvY2FsZSBvZiBsb2NhbGVzKSB7XG4gICAgICBjb25zdCBsb2NhbGVEb21haW5zID0gZG9tYWluc0J5TG9jYWxlLmdldChsb2NhbGUpIHx8IG5ldyBTZXQoKTtcbiAgICAgIGxvY2FsZURvbWFpbnMuYWRkKGRvbWFpbik7XG4gICAgICBkb21haW5zQnlMb2NhbGUuc2V0KGxvY2FsZSwgbG9jYWxlRG9tYWlucyk7XG4gICAgfVxuICB9XG4gIGNvbnN0IGR1cGxpY2F0ZUxvY2FsZU1lc3NhZ2VzID0gQXJyYXkuZnJvbShkb21haW5zQnlMb2NhbGUuZW50cmllcygpKS5maWx0ZXIoKFssIGxvY2FsZURvbWFpbnNdKSA9PiBsb2NhbGVEb21haW5zLnNpemUgPiAxKS5tYXAoKFtsb2NhbGUsIGxvY2FsZURvbWFpbnNdKSA9PiBgLSBcIiR7bG9jYWxlfVwiIGlzIHVzZWQgYnk6ICR7QXJyYXkuZnJvbShsb2NhbGVEb21haW5zKS5qb2luKCcsICcpfWApO1xuICBpZiAoZHVwbGljYXRlTG9jYWxlTWVzc2FnZXMubGVuZ3RoID4gMCkge1xuICAgIGNvbnNvbGUud2FybignTG9jYWxlcyBhcmUgZXhwZWN0ZWQgdG8gYmUgdW5pcXVlIHBlciBkb21haW4sIGJ1dCBmb3VuZCBvdmVybGFwOlxcbicgKyBkdXBsaWNhdGVMb2NhbGVNZXNzYWdlcy5qb2luKCdcXG4nKSArICdcXG5QbGVhc2Ugc2VlIGh0dHBzOi8vbmV4dC1pbnRsLmRldi9kb2NzL3JvdXRpbmcjZG9tYWlucycpO1xuICB9XG59XG5cbmV4cG9ydCB7IGRlZmluZVJvdXRpbmcgYXMgZGVmYXVsdCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/routing/defineRouting.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NextIntlClientProvider)\n/* harmony export */ });\n/* harmony import */ var use_intl_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/react */ \"(ssr)/./node_modules/use-intl/dist/esm/development/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction NextIntlClientProvider({ locale, ...rest }) {\n    if (!locale) {\n        throw new Error(\"Couldn't infer the `locale` prop in `NextIntlClientProvider`, please provide it explicitly.\\n\\nSee https://next-intl.dev/docs/configuration#locale\");\n    }\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(use_intl_react__WEBPACK_IMPORTED_MODULE_1__.IntlProvider, {\n        locale: locale,\n        ...rest\n    });\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NoYXJlZC9OZXh0SW50bENsaWVudFByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs2REFDOEM7QUFDTjtBQUV4QyxTQUFTRSx1QkFBdUIsRUFDOUJDLE1BQU0sRUFDTixHQUFHQyxNQUNKO0lBQ0MsSUFBSSxDQUFDRCxRQUFRO1FBQ1gsTUFBTSxJQUFJRSxNQUFNO0lBQ2xCO0lBQ0EsT0FBTyxXQUFXLEdBQUVKLHNEQUFHQSxDQUFDRCx3REFBWUEsRUFBRTtRQUNwQ0csUUFBUUE7UUFDUixHQUFHQyxJQUFJO0lBQ1Q7QUFDRjtBQUU2QyIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXFlFWl9UZWNoXFxZRVpfSG9tZVxcWUVaSG9tZV9GRVxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcZGV2ZWxvcG1lbnRcXHNoYXJlZFxcTmV4dEludGxDbGllbnRQcm92aWRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcbmltcG9ydCB7IEludGxQcm92aWRlciB9IGZyb20gJ3VzZS1pbnRsL3JlYWN0JztcbmltcG9ydCB7IGpzeCB9IGZyb20gJ3JlYWN0L2pzeC1ydW50aW1lJztcblxuZnVuY3Rpb24gTmV4dEludGxDbGllbnRQcm92aWRlcih7XG4gIGxvY2FsZSxcbiAgLi4ucmVzdFxufSkge1xuICBpZiAoIWxvY2FsZSkge1xuICAgIHRocm93IG5ldyBFcnJvcihcIkNvdWxkbid0IGluZmVyIHRoZSBgbG9jYWxlYCBwcm9wIGluIGBOZXh0SW50bENsaWVudFByb3ZpZGVyYCwgcGxlYXNlIHByb3ZpZGUgaXQgZXhwbGljaXRseS5cXG5cXG5TZWUgaHR0cHM6Ly9uZXh0LWludGwuZGV2L2RvY3MvY29uZmlndXJhdGlvbiNsb2NhbGVcIiApO1xuICB9XG4gIHJldHVybiAvKiNfX1BVUkVfXyovanN4KEludGxQcm92aWRlciwge1xuICAgIGxvY2FsZTogbG9jYWxlLFxuICAgIC4uLnJlc3RcbiAgfSk7XG59XG5cbmV4cG9ydCB7IE5leHRJbnRsQ2xpZW50UHJvdmlkZXIgYXMgZGVmYXVsdCB9O1xuIl0sIm5hbWVzIjpbIkludGxQcm92aWRlciIsImpzeCIsIk5leHRJbnRsQ2xpZW50UHJvdmlkZXIiLCJsb2NhbGUiLCJyZXN0IiwiRXJyb3IiLCJkZWZhdWx0Il0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/shared/use.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/use.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ use)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n// @ts-expect-error -- Ooof, Next.js doesn't make this easy.\n// `use` is only available in React 19 canary, but we can\n// use it in Next.js already as Next.js \"vendors\" a fixed\n// version of React. However, if we'd simply put `use` in\n// ESM code, then the build doesn't work since React does\n// not export `use` officially. Therefore, we have to use\n// something that is not statically analyzable. Once React\n// 19 is out, we can remove this in the next major version.\nvar use = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))['use'.trim()];\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL2RldmVsb3BtZW50L3NoYXJlZC91c2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQStCOztBQUUvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSx5TEFBSzs7QUFFVyIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RcXFlFWl9UZWNoXFxZRVpfSG9tZVxcWUVaSG9tZV9GRVxcbm9kZV9tb2R1bGVzXFxuZXh0LWludGxcXGRpc3RcXGVzbVxcZGV2ZWxvcG1lbnRcXHNoYXJlZFxcdXNlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIHJlYWN0IGZyb20gJ3JlYWN0JztcblxuLy8gQHRzLWV4cGVjdC1lcnJvciAtLSBPb29mLCBOZXh0LmpzIGRvZXNuJ3QgbWFrZSB0aGlzIGVhc3kuXG4vLyBgdXNlYCBpcyBvbmx5IGF2YWlsYWJsZSBpbiBSZWFjdCAxOSBjYW5hcnksIGJ1dCB3ZSBjYW5cbi8vIHVzZSBpdCBpbiBOZXh0LmpzIGFscmVhZHkgYXMgTmV4dC5qcyBcInZlbmRvcnNcIiBhIGZpeGVkXG4vLyB2ZXJzaW9uIG9mIFJlYWN0LiBIb3dldmVyLCBpZiB3ZSdkIHNpbXBseSBwdXQgYHVzZWAgaW5cbi8vIEVTTSBjb2RlLCB0aGVuIHRoZSBidWlsZCBkb2Vzbid0IHdvcmsgc2luY2UgUmVhY3QgZG9lc1xuLy8gbm90IGV4cG9ydCBgdXNlYCBvZmZpY2lhbGx5LiBUaGVyZWZvcmUsIHdlIGhhdmUgdG8gdXNlXG4vLyBzb21ldGhpbmcgdGhhdCBpcyBub3Qgc3RhdGljYWxseSBhbmFseXphYmxlLiBPbmNlIFJlYWN0XG4vLyAxOSBpcyBvdXQsIHdlIGNhbiByZW1vdmUgdGhpcyBpbiB0aGUgbmV4dCBtYWpvciB2ZXJzaW9uLlxudmFyIHVzZSA9IHJlYWN0Wyd1c2UnLnRyaW0oKV07XG5cbmV4cG9ydCB7IHVzZSBhcyBkZWZhdWx0IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/shared/use.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/shared/utils.js":
/*!*********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/utils.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLocaleAsPrefix: () => (/* binding */ getLocaleAsPrefix),\n/* harmony export */   getLocalePrefix: () => (/* binding */ getLocalePrefix),\n/* harmony export */   getLocalizedTemplate: () => (/* binding */ getLocalizedTemplate),\n/* harmony export */   getSortedPathnames: () => (/* binding */ getSortedPathnames),\n/* harmony export */   hasPathnamePrefixed: () => (/* binding */ hasPathnamePrefixed),\n/* harmony export */   isLocalizableHref: () => (/* binding */ isLocalizableHref),\n/* harmony export */   isPromise: () => (/* binding */ isPromise),\n/* harmony export */   matchesPathname: () => (/* binding */ matchesPathname),\n/* harmony export */   normalizeTrailingSlash: () => (/* binding */ normalizeTrailingSlash),\n/* harmony export */   prefixPathname: () => (/* binding */ prefixPathname),\n/* harmony export */   templateToRegex: () => (/* binding */ templateToRegex),\n/* harmony export */   unprefixPathname: () => (/* binding */ unprefixPathname)\n/* harmony export */ });\nfunction isRelativeHref(href) {\n  const pathname = typeof href === 'object' ? href.pathname : href;\n  return pathname != null && !pathname.startsWith('/');\n}\nfunction isLocalHref(href) {\n  if (typeof href === 'object') {\n    return href.host == null && href.hostname == null;\n  } else {\n    const hasProtocol = /^[a-z]+:/i.test(href);\n    return !hasProtocol;\n  }\n}\nfunction isLocalizableHref(href) {\n  return isLocalHref(href) && !isRelativeHref(href);\n}\nfunction unprefixPathname(pathname, prefix) {\n  return pathname.replace(new RegExp(`^${prefix}`), '') || '/';\n}\nfunction prefixPathname(prefix, pathname) {\n  let localizedHref = prefix;\n\n  // Avoid trailing slashes\n  if (/^\\/(\\?.*)?$/.test(pathname)) {\n    pathname = pathname.slice(1);\n  }\n  localizedHref += pathname;\n  return localizedHref;\n}\nfunction hasPathnamePrefixed(prefix, pathname) {\n  return pathname === prefix || pathname.startsWith(`${prefix}/`);\n}\nfunction hasTrailingSlash() {\n  try {\n    // Provided via `env` setting in `next.config.js` via the plugin\n    return process.env._next_intl_trailing_slash === 'true';\n  } catch {\n    return false;\n  }\n}\nfunction getLocalizedTemplate(pathnameConfig, locale, internalTemplate) {\n  return typeof pathnameConfig === 'string' ? pathnameConfig : pathnameConfig[locale] || internalTemplate;\n}\nfunction normalizeTrailingSlash(pathname) {\n  const trailingSlash = hasTrailingSlash();\n  if (pathname !== '/') {\n    const pathnameEndsWithSlash = pathname.endsWith('/');\n    if (trailingSlash && !pathnameEndsWithSlash) {\n      pathname += '/';\n    } else if (!trailingSlash && pathnameEndsWithSlash) {\n      pathname = pathname.slice(0, -1);\n    }\n  }\n  return pathname;\n}\nfunction matchesPathname(/** E.g. `/users/[userId]-[userName]` */\ntemplate, /** E.g. `/users/23-jane` */\npathname) {\n  const normalizedTemplate = normalizeTrailingSlash(template);\n  const normalizedPathname = normalizeTrailingSlash(pathname);\n  const regex = templateToRegex(normalizedTemplate);\n  return regex.test(normalizedPathname);\n}\nfunction getLocalePrefix(locale, localePrefix) {\n  return localePrefix.mode !== 'never' && localePrefix.prefixes?.[locale] ||\n  // We return a prefix even if `mode: 'never'`. It's up to the consumer\n  // to decide to use it or not.\n  getLocaleAsPrefix(locale);\n}\nfunction getLocaleAsPrefix(locale) {\n  return '/' + locale;\n}\nfunction templateToRegex(template) {\n  const regexPattern = template\n  // Replace optional catchall ('[[...slug]]')\n  .replace(/\\[\\[(\\.\\.\\.[^\\]]+)\\]\\]/g, '?(.*)')\n  // Replace catchall ('[...slug]')\n  .replace(/\\[(\\.\\.\\.[^\\]]+)\\]/g, '(.+)')\n  // Replace regular parameter ('[slug]')\n  .replace(/\\[([^\\]]+)\\]/g, '([^/]+)');\n  return new RegExp(`^${regexPattern}$`);\n}\nfunction isOptionalCatchAllSegment(pathname) {\n  return pathname.includes('[[...');\n}\nfunction isCatchAllSegment(pathname) {\n  return pathname.includes('[...');\n}\nfunction isDynamicSegment(pathname) {\n  return pathname.includes('[');\n}\nfunction comparePathnamePairs(a, b) {\n  const pathA = a.split('/');\n  const pathB = b.split('/');\n  const maxLength = Math.max(pathA.length, pathB.length);\n  for (let i = 0; i < maxLength; i++) {\n    const segmentA = pathA[i];\n    const segmentB = pathB[i];\n\n    // If one of the paths ends, prioritize the shorter path\n    if (!segmentA && segmentB) return -1;\n    if (segmentA && !segmentB) return 1;\n    if (!segmentA && !segmentB) continue;\n\n    // Prioritize static segments over dynamic segments\n    if (!isDynamicSegment(segmentA) && isDynamicSegment(segmentB)) return -1;\n    if (isDynamicSegment(segmentA) && !isDynamicSegment(segmentB)) return 1;\n\n    // Prioritize non-catch-all segments over catch-all segments\n    if (!isCatchAllSegment(segmentA) && isCatchAllSegment(segmentB)) return -1;\n    if (isCatchAllSegment(segmentA) && !isCatchAllSegment(segmentB)) return 1;\n\n    // Prioritize non-optional catch-all segments over optional catch-all segments\n    if (!isOptionalCatchAllSegment(segmentA) && isOptionalCatchAllSegment(segmentB)) {\n      return -1;\n    }\n    if (isOptionalCatchAllSegment(segmentA) && !isOptionalCatchAllSegment(segmentB)) {\n      return 1;\n    }\n    if (segmentA === segmentB) continue;\n  }\n\n  // Both pathnames are completely static\n  return 0;\n}\nfunction getSortedPathnames(pathnames) {\n  return pathnames.sort(comparePathnamePairs);\n}\nfunction isPromise(value) {\n  // https://github.com/amannn/next-intl/issues/1711\n  return typeof value.then === 'function';\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/shared/utils.js\n");

/***/ })

};
;