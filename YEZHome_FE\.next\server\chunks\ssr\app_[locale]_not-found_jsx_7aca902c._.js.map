{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/%5Blocale%5D/not-found.jsx"], "sourcesContent": ["import Image from \"next/image\";\r\nimport {Link} from '@/i18n/navigation';;\r\nimport { getTranslations } from \"next-intl/server\";\r\n\r\nexport default async function NotFound() {\r\n  const t = await getTranslations(\"NotFound\");\r\n\r\n  return (\r\n    <div className=\"flex flex-col items-center justify-center min-h-screen bg-slate-50 text-center p-4\">\r\n      <Image\r\n        src=\"/404.png\"\r\n        alt={t(\"title\")}\r\n        width={400}\r\n        height={400}\r\n        className=\"mb-8 max-w-full h-auto\"\r\n      />\r\n      <h1 className=\"text-2xl md:text-4xl font-bold text-gray-800 mb-4\">{t(\"title\")}</h1>\r\n      <p className=\"text-gray-600 mb-8 max-w-md\">{t(\"description\")}</p>\r\n      <Link\r\n        href=\"/\"\r\n        className=\"px-6 py-3 bg-navy-blue text-white text-lg rounded-lg shadow-md hover:bg-teal-700 transition\"\r\n      >\r\n        {t(\"backButton\")}\r\n      </Link>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;;AAEe,eAAe;IAC5B,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;IAEhC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,6HAAA,CAAA,UAAK;gBACJ,KAAI;gBACJ,KAAK,EAAE;gBACP,OAAO;gBACP,QAAQ;gBACR,WAAU;;;;;;0BAEZ,8OAAC;gBAAG,WAAU;0BAAqD,EAAE;;;;;;0BACrE,8OAAC;gBAAE,WAAU;0BAA+B,EAAE;;;;;;0BAC9C,8OAAC,kHAAA,CAAA,OAAI;gBACH,MAAK;gBACL,WAAU;0BAET,EAAE;;;;;;;;;;;;AAIX", "debugId": null}}]}