"use client";

import { useState } from "react";
import { ChevronDown } from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { PropertyStatus } from "@/lib/enum";
import { useTranslations } from "next-intl";
import BadgeStatus from "../layout/BadgeStatus";
import { Badge } from "../ui/badge";

export default function PropertyDescription({ property = {} }) {
  const [showMore, setShowMore] = useState(false);
  const maxLength = 300;
  const tCommon = useTranslations("Common");

  // Extract property data with fallbacks
  const propertyData = {
    name: property.name || "",
    price: property.price || 0,
    address: property.address || "",
    description: property.description || "",
    rooms: property.rooms || 0,
    toilets: property.toilets || 0,
    area: property.area || 0,
    propertyType: property.propertyType || "",
    postType: property.postType || "",
    floors: property.floors || 0,
    direction: property.direction || "--",
    balconyDirection: property.balconyDirection || "--",
    legality: property.legality || "--",
    interior: property.interior || "--",
    width: property.width || 0,
    roadWidth: property.roadWidth || 0,
    createdAt: property.createdAt || new Date().toISOString(),
    updatedAt: property.updatedAt || new Date().toISOString(),
    owner: property.owner || {},
    status: property.status || PropertyStatus.DRAFT,
    isHighlighted: property.isHighlighted || false,
  };

  return (
    <>
      {/* Price, Address and Key Details */}
      <div className="flex flex-col xl:flex-row xl:items-start xl:justify-between mb-6 gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{formatCurrency(propertyData.price)}</h1>
          <p className="text-lg text-gray-700">{propertyData.address}</p>
          <div className="flex gap-3">
            <BadgeStatus status={propertyData.status} statusText={`${tCommon(`propertyStatus_${propertyData.status}`)}` || propertyData.status} />
            {propertyData.isHighlighted && <Badge className="font-medium rounded-md px-2 py-1 text-sm mt-3 bg-yellow-600 text-white ml-3 hover:bg-yellow-600">{tCommon("highlight_status")}</Badge>}
          </div>
        </div>

        {/* Key Details */}
        <div className="grid grid-cols-3 gap-2 xl:w-auto">
          <div className="bg-gray-100 p-4 rounded-md flex flex-col items-center justify-center">
            <span className="text-2xl font-bold">{propertyData.rooms || 0}</span>
            <span className="text-gray-600 text-center text-xs">phòng ngủ</span>
          </div>
          <div className="bg-gray-100 p-4 rounded-md flex flex-col items-center justify-center">
            <span className="text-2xl font-bold">{propertyData.toilets || 0}</span>
            <span className="text-gray-600 text-center text-xs">phòng tắm</span>
          </div>
          <div className="bg-gray-100 p-4 rounded-md flex flex-col items-center justify-center">
            <span className="text-2xl font-bold">{propertyData.area || 0}</span>
            <span className="text-gray-600 text-center text-xs">m²</span>
          </div>
        </div>
      </div>

      <hr className="my-6" />
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-4">Thông tin cơ bản</h2>
        {/* Property Features */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-2 mb-6">
          <div className="bg-gray-100 p-3 rounded-md flex items-center">
            <svg
              className="h-5 w-5 mr-2 text-gray-600"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
            >
              <path d="M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
              <polyline points="9 22 9 12 15 12 15 22" />
            </svg>
            <span className="text-gray-700">{propertyData.propertyType}</span>
          </div>
          <div className="bg-gray-100 p-3 rounded-md flex items-center">
            <svg
              className="h-5 w-5 mr-2 text-gray-600"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
            >
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
              <line x1="16" y1="2" x2="16" y2="6" />
              <line x1="8" y1="2" x2="8" y2="6" />
              <line x1="3" y1="10" x2="21" y2="10" />
            </svg>
            <span className="text-gray-700">Loại giao dịch: {propertyData.postType}</span>
          </div>
          <div className="bg-gray-100 p-3 rounded-md flex items-center">
            <svg
              className="h-5 w-5 mr-2 text-gray-600"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
            >
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0118 0z" />
              <circle cx="12" cy="10" r="3" />
            </svg>
            <span className="text-gray-700">{propertyData.area} m² đất</span>
          </div>
        </div>

        {/* Additional Property Features */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-2 mb-6">
          <div className="bg-gray-100 p-3 rounded-md flex items-center">
            <svg
              className="h-5 w-5 mr-2 text-gray-600"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
            >
              <path d="M3 3v18h18" />
              <path d="M3 12h18" />
              <path d="M12 3v18" />
            </svg>
            <span className="text-gray-700">Số tầng: {propertyData.floors}</span>
          </div>
          <div className="bg-gray-100 p-3 rounded-md flex items-center">
            <svg
              className="h-5 w-5 mr-2 text-gray-600"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
            >
              <circle cx="12" cy="12" r="10" />
              <polyline points="12 6 12 12 16 14" />
            </svg>
            <span className="text-gray-700">Hướng nhà: {propertyData.direction}</span>
          </div>
          <div className="bg-gray-100 p-3 rounded-md flex items-center">
            <svg
              className="h-5 w-5 mr-2 text-gray-600"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
            >
              <path d="M3 12h18" />
              <path d="M12 3v18" />
              <path d="M18 6l-6 6-6-6" />
            </svg>
            <span className="text-gray-700">Hướng ban công: {propertyData.balconyDirection}</span>
          </div>
        </div>

        {/* More Property Features */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-2 mb-6">
          <div className="bg-gray-100 p-3 rounded-md flex items-center">
            <svg
              className="h-5 w-5 mr-2 text-gray-600"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
            >
              <path d="M20 11.08V8l-6-6H6a2 2 0 0 0-2 2v16c0 1.1.9 2 2 2h12a2 2 0 0 0 2-2v-3.08" />
              <path d="M14 3v5h5" />
            </svg>
            <span className="text-gray-700">Pháp lý: {propertyData.legality}</span>
          </div>
          <div className="bg-gray-100 p-3 rounded-md flex items-center">
            <svg
              className="h-5 w-5 mr-2 text-gray-600"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
            >
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
              <line x1="9" y1="9" x2="15" y2="15" />
              <line x1="15" y1="9" x2="9" y2="15" />
            </svg>
            <span className="text-gray-700">Nội thất: {propertyData.interior}</span>
          </div>
          <div className="bg-gray-100 p-3 rounded-md flex items-center">
            <svg
              className="h-5 w-5 mr-2 text-gray-600"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
            >
              <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" />
              <polyline points="3.27 6.96 12 12.01 20.73 6.96" />
              <line x1="12" y1="22.08" x2="12" y2="12" />
            </svg>
            <span className="text-gray-700">Chiều rộng: {propertyData.width}m</span>
          </div>
        </div>

        {/* Last Row of Property Features */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-2 mb-6">
          <div className="bg-gray-100 p-3 rounded-md flex items-center">
            <svg
              className="h-5 w-5 mr-2 text-gray-600"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
            >
              <path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z" />
              <line x1="4" y1="22" x2="4" y2="15" />
            </svg>
            <span className="text-gray-700">Đường rộng: {propertyData.roadWidth}m</span>
          </div>
        </div>
      </div>

      <hr className="my-6" />

      {/* Description */}
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-4">Mô tả</h2>
        <div className="text-gray-700">
          <p className="mb-4 whitespace-pre-line">
            {showMore
              ? propertyData.description
              : `${propertyData.description.substring(0, maxLength)}${
                  propertyData.description.length > maxLength ? "..." : ""
                }`}
          </p>
          {propertyData.description.length > maxLength && (
            <button
              onClick={() => setShowMore(!showMore)}
              className="text-blue-600 flex items-center"
            >
              {showMore ? (
                <>
                  Ẩn bớt <ChevronDown className="h-4 w-4 ml-1 transform rotate-180" />
                </>
              ) : (
                <>
                  Xem thêm <ChevronDown className="h-4 w-4 ml-1" />
                </>
              )}
            </button>
          )}
        </div>
      </div>

      {/* Listing Details */}
      <div className="mb-6">
        <div className="mt-4 text-sm text-gray-700">
          <p>Cập nhật lần cuối: {new Date(propertyData.updatedAt).toLocaleDateString("vi-VN")}</p>
          <p>Ngày đăng: {new Date(propertyData.createdAt).toLocaleDateString("vi-VN")}</p>
        </div>
      </div>
      <hr></hr>
    </>
  );
}
