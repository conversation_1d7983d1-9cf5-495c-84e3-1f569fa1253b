"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { createContactRequest } from "@/app/actions/server/contactRequest";
import { Mail, Phone, User, MessageSquare, Check } from "lucide-react";

export default function PropertyContactForm({ isOpen, onClose, propertyId, ownerId }) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    mobile: "",
    note: ""
  });
  const { toast } = useToast();

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleContactSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      const form = new FormData();
      form.append("name", formData.name);
      form.append("email", formData.email);
      form.append("mobile", formData.mobile);
      form.append("note", formData.note);
      form.append("propertyId", propertyId);
      form.append("ownerId", ownerId);
      
      const response = await createContactRequest(null, form);
      
      if (response.success) {
        setShowSuccessMessage(true);
      } else {
        toast({
          title: "Lỗi",
          description: response.message || "Không thể gửi yêu cầu liên hệ. Vui lòng thử lại sau.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error submitting contact request:", error);
      toast({
        title: "Lỗi",
        description: "Đã xảy ra lỗi khi gửi yêu cầu liên hệ. Vui lòng thử lại sau.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const resetContactForm = () => {
    setFormData({
      name: "",
      email: "",
      mobile: "",
      note: ""
    });
    setShowSuccessMessage(false);
  };
  
  const handleContactModalClose = () => {
    onClose();
    // Only reset the form after a delay to avoid visual glitches
    setTimeout(resetContactForm, 300);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleContactModalClose}>
      <DialogContent className="max-w-md">
        {!showSuccessMessage ? (
          <>
            <DialogHeader>
              <DialogTitle>Liên hệ người bán</DialogTitle>
              <DialogDescription>
                Vui lòng điền thông tin của bạn để người bán có thể liên hệ lại với bạn.
              </DialogDescription>
            </DialogHeader>
            
            <form onSubmit={handleContactSubmit} className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="name">Họ và tên</Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <User className="h-4 w-4 text-gray-400" />
                  </div>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Nhập họ và tên của bạn"
                    className="pl-10"
                    required
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-4 w-4 text-gray-400" />
                  </div>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="Nhập địa chỉ email của bạn"
                    className="pl-10"
                    required
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="mobile">Số điện thoại</Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Phone className="h-4 w-4 text-gray-400" />
                  </div>
                  <Input
                    id="mobile"
                    name="mobile"
                    type="tel"
                    value={formData.mobile}
                    onChange={handleInputChange}
                    placeholder="Nhập số điện thoại của bạn"
                    className="pl-10"
                    required
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="note">Ghi chú</Label>
                <div className="relative">
                  <div className="absolute top-3 left-3 pointer-events-none">
                    <MessageSquare className="h-4 w-4 text-gray-400" />
                  </div>
                  <Textarea
                    id="note"
                    name="note"
                    value={formData.note}
                    onChange={handleInputChange}
                    placeholder="Nhập nội dung bạn muốn hỏi"
                    className="pl-10 min-h-[100px]"
                  />
                </div>
              </div>
              
              <DialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
                <Button type="button" variant="outline" onClick={handleContactModalClose} disabled={isSubmitting}>
                  Hủy
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <span className="animate-spin mr-2">⏳</span> Đang gửi...
                    </>
                  ) : (
                    "Gửi yêu cầu"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </>
        ) : (
          <>
            <DialogHeader>
              <DialogTitle>Yêu cầu đã được gửi</DialogTitle>
            </DialogHeader>
            
            <div className="py-6 flex flex-col items-center justify-center text-center">
              <div className="bg-green-100 p-3 rounded-full mb-4">
                <Check className="h-8 w-8 text-green-600" />
              </div>
              <p className="text-lg font-medium mb-2">Cảm ơn bạn đã gửi yêu cầu!</p>
              <p className="text-gray-500 mb-4">Chúng tôi đã nhận được yêu cầu của bạn và sẽ chuyển đến người bán. Người bán sẽ liên hệ với bạn trong thời gian sớm nhất.</p>
              
              <Button onClick={handleContactModalClose}>
                Đóng
              </Button>
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
