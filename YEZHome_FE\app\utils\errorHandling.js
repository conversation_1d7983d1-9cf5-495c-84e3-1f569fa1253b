/**
 * Payment error handling utility functions
 */

// Map payment gateway error codes to user-friendly messages
export const paymentErrorCodes = {
  // MoMo error codes
  momo: {
    1: "Giao dịch không thành công, số dư không đủ",
    2: "Giao dịch đã bị hủy",
    3: "Giao dịch đã hết hạn",
    4: "Sai thông tin thanh toán",
    9: "Giao dịch bị từ chối bởi hệ thống MoMo",
    10: "Giao dịch đang xử lý",
    11: "Yêu cầu xác thực OTP",
    12: "Cổng thanh toán không khả dụng, vui lòng thử lại sau",
    41: "Sai mã đối tác (Partner Code)",
    42: "Vấn đề về bảo mật, giao dịch không hợp lệ",
    default: "Lỗi không xác định từ MoMo",
  },
  
  // Card payment error codes
  card: {
    1: "Thẻ bị từ chối bởi ngân hàng phát hành",
    2: "Số thẻ không hợp lệ",
    3: "Thẻ đã hết hạn",
    4: "M<PERSON> bảo mật CVV không đúng",
    5: "Số dư thẻ không đủ",
    6: "Vượt quá hạn mức thanh toán của thẻ",
    7: "Ngân hàng từ chối giao dịch vì lý do bảo mật",
    default: "Lỗi không xác định khi thanh toán bằng thẻ",
  },
  
  // Bank transfer error codes
  banking: {
    1: "Không tìm thấy giao dịch chuyển khoản phù hợp",
    2: "Số tiền chuyển khoản không khớp",
    3: "Nội dung chuyển khoản không đúng định dạng",
    4: "Giao dịch chuyển khoản đã được sử dụng",
    default: "Lỗi không xác định khi xác minh chuyển khoản",
  },
  
  // General error codes
  general: {
    timeout: "Quá thời gian xử lý giao dịch",
    network: "Lỗi kết nối, vui lòng kiểm tra đường truyền internet",
    server: "Lỗi máy chủ, vui lòng thử lại sau",
    permission: "Bạn không có quyền thực hiện thao tác này",
    validation: "Dữ liệu không hợp lệ",
    default: "Đã xảy ra lỗi không xác định",
  },
};

/**
 * Get user-friendly error message based on payment gateway and error code
 * @param {string} gateway - Payment gateway (momo, card, banking)
 * @param {string|number} code - Error code from the payment gateway
 * @returns {string} - User-friendly error message
 */
export function getPaymentErrorMessage(gateway, code) {
  // Get the error codes for the specified gateway or general errors
  const errorCodesForGateway = paymentErrorCodes[gateway] || paymentErrorCodes.general;
  
  // Return the message for the specific code or default message if not found
  return errorCodesForGateway[code] || errorCodesForGateway.default || paymentErrorCodes.general.default;
}

/**
 * Log payment error to analytics or monitoring system
 * @param {string} gateway - Payment gateway
 * @param {string|number} code - Error code
 * @param {Object} details - Additional error details
 */
export function logPaymentError(gateway, code, details = {}) {
  // In a production app, this would log to a monitoring system like Sentry
  console.error(`Payment Error [${gateway}]: Code ${code}`, details);
  
  // You could add additional logic to notify administrators of critical errors
}

/**
 * Handle payment error and return appropriate response
 * @param {string} gateway - Payment gateway
 * @param {string|number} code - Error code
 * @param {Object} details - Additional error details
 * @returns {Object} - Response object with error information
 */
export function handlePaymentError(gateway, code, details = {}) {
  // Log the error
  logPaymentError(gateway, code, details);
  
  // Get user-friendly message
  const message = getPaymentErrorMessage(gateway, code);
  
  // Create response object
  const response = {
    success: false,
    message,
    errorCode: code,
    errorSource: gateway,
    timestamp: new Date().toISOString(),
  };
  
  // Add recommendation based on the error
  switch (gateway) {
    case 'momo':
      if (code === 1) {
        response.recommendation = "Vui lòng kiểm tra số dư ví MoMo của bạn và thử lại.";
      } else if (code === 3) {
        response.recommendation = "Vui lòng tạo giao dịch mới.";
      }
      break;
    
    case 'banking':
      if (code === 1 || code === 2 || code === 3) {
        response.recommendation = "Vui lòng kiểm tra thông tin chuyển khoản và đảm bảo rằng bạn đã chuyển đúng số tiền với nội dung chính xác.";
      }
      break;
      
    case 'card':
      if ([1, 5, 6].includes(Number(code))) {
        response.recommendation = "Vui lòng liên hệ ngân hàng phát hành thẻ của bạn để được hỗ trợ.";
      } else if ([2, 3, 4].includes(Number(code))) {
        response.recommendation = "Vui lòng kiểm tra lại thông tin thẻ của bạn.";
      }
      break;
      
    default:
      response.recommendation = "Vui lòng thử lại sau hoặc chọn phương thức thanh toán khác.";
  }
  
  return response;
}

/**
 * Determine if the payment error is recoverable (can be retried)
 * @param {string} gateway - Payment gateway
 * @param {string|number} code - Error code
 * @returns {boolean} - True if error is recoverable
 */
export function isRecoverablePaymentError(gateway, code) {
  // Define which error codes are recoverable for each gateway
  const recoverableErrors = {
    momo: [10, 12], // Processing, Gateway unavailable
    card: [],       // Most card errors require user action
    banking: [1],   // Transaction not found (might succeed later)
    general: ['timeout', 'network', 'server'],
  };
  
  // Get recoverable errors for the gateway
  const recoverable = recoverableErrors[gateway] || recoverableErrors.general;
  
  // Check if the code is in the list of recoverable errors
  return recoverable.includes(code);
} 