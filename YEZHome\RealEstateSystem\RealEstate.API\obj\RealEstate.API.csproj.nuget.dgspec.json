{"format": 1, "restore": {"D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.API\\RealEstate.API.csproj": {}}, "projects": {"D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.API\\RealEstate.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.API\\RealEstate.API.csproj", "projectName": "RealEstate.API", "projectPath": "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.API\\RealEstate.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Application\\RealEstate.Application.csproj": {"projectPath": "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Application\\RealEstate.Application.csproj"}, "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Infrastructure\\RealEstate.Infrastructure.csproj": {"projectPath": "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Infrastructure\\RealEstate.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Application\\RealEstate.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Application\\RealEstate.Application.csproj", "projectName": "RealEstate.Application", "projectPath": "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Application\\RealEstate.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Domain\\RealEstate.Domain.csproj": {"projectPath": "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Domain\\RealEstate.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "EPPlus": {"target": "Package", "version": "[7.0.10, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.IdentityModel.Tokens": {"target": "Package", "version": "[8.3.0, )"}, "SixLabors.ImageSharp": {"target": "Package", "version": "[3.1.7, )"}, "SixLabors.ImageSharp.Drawing": {"target": "Package", "version": "[2.1.5, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Domain\\RealEstate.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Domain\\RealEstate.Domain.csproj", "projectName": "RealEstate.Domain", "projectPath": "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Domain\\RealEstate.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Infrastructure\\RealEstate.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Infrastructure\\RealEstate.Infrastructure.csproj", "projectName": "RealEstate.Infrastructure", "projectPath": "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Infrastructure\\RealEstate.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Domain\\RealEstate.Domain.csproj": {"projectPath": "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\RealEstate.Domain\\RealEstate.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}