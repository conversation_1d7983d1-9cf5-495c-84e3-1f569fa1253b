"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(protected)/user/bds/[propertyId]/page",{

/***/ "(app-pages-browser)/./components/user-property/CreatePropertyDetailInformation.jsx":
/*!**********************************************************************!*\
  !*** ./components/user-property/CreatePropertyDetailInformation.jsx ***!
  \**********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreatePropertyDetailInformation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./components/ui/switch.jsx\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_format_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/addDays.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_format_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/vi.js\");\n/* harmony import */ var _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-dropdown-menu */ \"(app-pages-browser)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs\");\n/* harmony import */ var _lib_enum__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/enum */ \"(app-pages-browser)/./lib/enum.js\");\n/* harmony import */ var _layout_BadgeStatus__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../layout/BadgeStatus */ \"(app-pages-browser)/./components/layout/BadgeStatus.jsx\");\n/* harmony import */ var _layout_BadgeUserRank__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../layout/BadgeUserRank */ \"(app-pages-browser)/./components/layout/BadgeUserRank.jsx\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* harmony import */ var _contexts_ProfileContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/ProfileContext */ \"(app-pages-browser)/./contexts/ProfileContext.jsx\");\n/* harmony import */ var _lib_memberRankUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/memberRankUtils */ \"(app-pages-browser)/./lib/memberRankUtils.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CreatePropertyDetailInformation(param) {\n    let { status, statusText, isFormDisabled, createdAt, expiresAt, highlight, setHighlight, autoRenew, setAutoRenew, basePostPrice = _lib_enum__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_POST_PRICE, onRankChange = ()=>{}, onRefreshRef = null } = param;\n    var _profile_user;\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations)(\"CreatePropertyDetailInformation\");\n    const { profile } = (0,_contexts_ProfileContext__WEBPACK_IMPORTED_MODULE_6__.useProfile)();\n    const displayDuration = 10; // days\n    // Get the user's rank from profile context\n    const userRankFromProfile = (profile === null || profile === void 0 ? void 0 : (_profile_user = profile.user) === null || _profile_user === void 0 ? void 0 : _profile_user.memberRank) || _lib_enum__WEBPACK_IMPORTED_MODULE_3__.MemberRank.DEFAULT;\n    // State to track the current rank (can be updated by BadgeUserRank component)\n    const [currentRank, setCurrentRank] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(userRankFromProfile);\n    const onCheckedChange = (checked)=>{\n        if (isFormDisabled) return;\n        console.log(\"Checked:\", checked);\n        setHighlight(checked);\n    };\n    // Update currentRank when profile changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreatePropertyDetailInformation.useEffect\": ()=>{\n            var _profile_user;\n            if (profile === null || profile === void 0 ? void 0 : (_profile_user = profile.user) === null || _profile_user === void 0 ? void 0 : _profile_user.memberRank) {\n                setCurrentRank(profile.user.memberRank);\n            }\n        }\n    }[\"CreatePropertyDetailInformation.useEffect\"], [\n        profile\n    ]);\n    // Calculate highlight price using the utility function\n    const highlightPrice = (0,_lib_memberRankUtils__WEBPACK_IMPORTED_MODULE_7__.getHighlightPriceNumber)(currentRank);\n    const totalPrice = basePostPrice + (highlight ? highlightPrice : 0);\n    const expirationDate = expiresAt || (0,_barrel_optimize_names_addDays_format_date_fns__WEBPACK_IMPORTED_MODULE_9__.addDays)(createdAt, displayDuration);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                className: \"text-muted-foreground\",\n                                children: t('customerType')\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"font-medium\",\n                                children: t('individualCustomer')\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                className: \"text-muted-foreground\",\n                                children: t('memberRank')\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_BadgeUserRank__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                showRefreshButton: true,\n                                onRankChange: (rankData)=>{\n                                    // Only update if the rank has actually changed\n                                    if (rankData.currentRank !== currentRank) {\n                                        setCurrentRank(rankData.currentRank);\n                                        onRankChange(rankData);\n                                    }\n                                },\n                                onRefreshRef: onRefreshRef\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t pt-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                    htmlFor: \"highlight\",\n                                    className: \"font-medium\",\n                                    children: t('highlightPost')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: t('highlightPostDescription')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        !isFormDisabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_2__.Switch, {\n                            id: \"highlight\",\n                            checked: highlight,\n                            onCheckedChange: onCheckedChange\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t pt-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                    htmlFor: \"highlight\",\n                                    className: \"font-medium\",\n                                    children: t('autoRenew')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: t('autoRenewDescription')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this),\n                        !isFormDisabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_2__.Switch, {\n                            id: \"auto-renew\",\n                            checked: autoRenew,\n                            onCheckedChange: setAutoRenew\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t pt-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                    className: \"text-muted-foreground\",\n                                    children: t('postStatus')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_BadgeStatus__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    status: status,\n                                    statusText: statusText\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                    className: \"text-muted-foreground\",\n                                    children: t('expirationTime')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-medium\",\n                                    children: [\n                                        displayDuration,\n                                        \" \",\n                                        t('days')\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                    className: \"text-muted-foreground\",\n                                    children: t('postDate')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium\",\n                                            children: (0,_barrel_optimize_names_addDays_format_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(createdAt, \"dd/MM/yyyy\", {\n                                                locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_13__.vi\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                    className: \"text-muted-foreground\",\n                                    children: t('expirationDate')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium\",\n                                            children: (0,_barrel_optimize_names_addDays_format_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(expirationDate, \"dd/MM/yyyy\", {\n                                                locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_13__.vi\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t pt-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: t('postFee')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        basePostPrice.toLocaleString(\"vi-VN\"),\n                                        \" đ\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this),\n                        highlight && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between mb-3 pb-3 \",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        t('highlightFee'),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_BadgeUserRank__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            externalRank: currentRank\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 41\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        highlightPrice.toLocaleString(\"vi-VN\"),\n                                        \" đ\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                            lineNumber: 147,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between font-bold text-lg pt-2 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: t('total')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        totalPrice.toLocaleString(\"vi-VN\"),\n                                        \" đ\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Project\\\\YEZ_Tech\\\\YEZ_Home\\\\YEZHome_FE\\\\components\\\\user-property\\\\CreatePropertyDetailInformation.jsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(CreatePropertyDetailInformation, \"wz+jXHbRngcVpsv8ztwmBoFOiRo=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations,\n        _contexts_ProfileContext__WEBPACK_IMPORTED_MODULE_6__.useProfile\n    ];\n});\n_c = CreatePropertyDetailInformation;\nvar _c;\n$RefreshReg$(_c, \"CreatePropertyDetailInformation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvdXNlci1wcm9wZXJ0eS9DcmVhdGVQcm9wZXJ0eURldGFpbEluZm9ybWF0aW9uLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDNEM7QUFDSTtBQUNKO0FBQ0Q7QUFDTjtBQUNpQjtBQUNNO0FBQ1o7QUFDSTtBQUNSO0FBQ1c7QUFDUztBQUVqRCxTQUFTZSxnQ0FBZ0MsS0FhdkQ7UUFidUQsRUFDdERDLE1BQU0sRUFDTkMsVUFBVSxFQUNWQyxjQUFjLEVBQ2RDLFNBQVMsRUFDVEMsU0FBUyxFQUNUQyxTQUFTLEVBQ1RDLFlBQVksRUFDWkMsU0FBUyxFQUNUQyxZQUFZLEVBQ1pDLGdCQUFnQmpCLHlEQUFrQixFQUNsQ2tCLGVBQWUsS0FBTyxDQUFDLEVBQ3ZCQyxlQUFlLElBQUksRUFDcEIsR0FidUQ7UUFtQjFCQzs7SUFMNUIsTUFBTUMsSUFBSWpCLDBEQUFlQSxDQUFDO0lBQzFCLE1BQU0sRUFBRWdCLE9BQU8sRUFBRSxHQUFHZixvRUFBVUE7SUFDOUIsTUFBTWlCLGtCQUFrQixJQUFJLE9BQU87SUFFbkMsMkNBQTJDO0lBQzNDLE1BQU1DLHNCQUFzQkgsQ0FBQUEsb0JBQUFBLCtCQUFBQSxnQkFBQUEsUUFBU0ksSUFBSSxjQUFiSixvQ0FBQUEsY0FBZUssVUFBVSxLQUFJeEIsaURBQVVBLENBQUN5QixPQUFPO0lBRTNFLDhFQUE4RTtJQUM5RSxNQUFNLENBQUNDLGFBQWFDLGVBQWUsR0FBR3BDLCtDQUFRQSxDQUFDK0I7SUFFL0MsTUFBTU0sa0JBQWtCLENBQUNDO1FBQ3ZCLElBQUlwQixnQkFBZ0I7UUFDcEJxQixRQUFRQyxHQUFHLENBQUMsWUFBWUY7UUFDeEJoQixhQUFhZ0I7SUFDZjtJQUVBLDBDQUEwQztJQUMxQ3JDLGdEQUFTQTtxREFBQztnQkFDSjJCO1lBQUosSUFBSUEsb0JBQUFBLCtCQUFBQSxnQkFBQUEsUUFBU0ksSUFBSSxjQUFiSixvQ0FBQUEsY0FBZUssVUFBVSxFQUFFO2dCQUM3QkcsZUFBZVIsUUFBUUksSUFBSSxDQUFDQyxVQUFVO1lBQ3hDO1FBQ0Y7b0RBQUc7UUFBQ0w7S0FBUTtJQUVaLHVEQUF1RDtJQUN2RCxNQUFNYSxpQkFBaUIzQiw2RUFBdUJBLENBQUNxQjtJQUMvQyxNQUFNTyxhQUFhakIsZ0JBQWlCSixDQUFBQSxZQUFZb0IsaUJBQWlCO0lBRWpFLE1BQU1FLGlCQUFpQnZCLGFBQWFmLHVGQUFPQSxDQUFDYyxXQUFXVztJQUV2RCxxQkFDRTs7MEJBQ0UsOERBQUNjO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7OzBDQUNDLDhEQUFDckMsaUVBQUtBO2dDQUFDc0MsV0FBVTswQ0FBeUJoQixFQUFFOzs7Ozs7MENBQzVDLDhEQUFDaUI7Z0NBQUVELFdBQVU7MENBQWVoQixFQUFFOzs7Ozs7Ozs7Ozs7a0NBRWhDLDhEQUFDZTs7MENBQ0MsOERBQUNyQyxpRUFBS0E7Z0NBQUNzQyxXQUFVOzBDQUF5QmhCLEVBQUU7Ozs7OzswQ0FDNUMsOERBQUNsQiw2REFBYUE7Z0NBQ1pvQyxtQkFBbUI7Z0NBQ25CckIsY0FBYyxDQUFDc0I7b0NBQ2IsK0NBQStDO29DQUMvQyxJQUFJQSxTQUFTYixXQUFXLEtBQUtBLGFBQWE7d0NBQ3hDQyxlQUFlWSxTQUFTYixXQUFXO3dDQUNuQ1QsYUFBYXNCO29DQUNmO2dDQUNGO2dDQUNBckIsY0FBY0E7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFLcEIsOERBQUNpQjtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs7OENBQ0MsOERBQUNyQyxpRUFBS0E7b0NBQUMwQyxTQUFRO29DQUFZSixXQUFVOzhDQUNsQ2hCLEVBQUU7Ozs7Ozs4Q0FFTCw4REFBQ2lCO29DQUFFRCxXQUFVOzhDQUNWaEIsRUFBRTs7Ozs7Ozs7Ozs7O3dCQUdOLENBQUNYLGdDQUNBLDhEQUFDaEIseURBQU1BOzRCQUFDZ0QsSUFBRzs0QkFBWVosU0FBU2pCOzRCQUFXZ0IsaUJBQWlCQTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBS2xFLDhEQUFDTztnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs7OENBQ0MsOERBQUNyQyxpRUFBS0E7b0NBQUMwQyxTQUFRO29DQUFZSixXQUFVOzhDQUNsQ2hCLEVBQUU7Ozs7Ozs4Q0FFTCw4REFBQ2lCO29DQUFFRCxXQUFVOzhDQUNWaEIsRUFBRTs7Ozs7Ozs7Ozs7O3dCQUdOLENBQUNYLGdDQUNBLDhEQUFDaEIseURBQU1BOzRCQUFDZ0QsSUFBRzs0QkFBYVosU0FBU2Y7NEJBQVdjLGlCQUFpQmI7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUtuRSw4REFBQ29CO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzs4Q0FDQyw4REFBQ3JDLGlFQUFLQTtvQ0FBQ3NDLFdBQVU7OENBQXlCaEIsRUFBRTs7Ozs7OzhDQUM1Qyw4REFBQ25CLDJEQUFXQTtvQ0FBQ00sUUFBUUE7b0NBQVFDLFlBQVlBOzs7Ozs7Ozs7Ozs7c0NBRTNDLDhEQUFDMkI7OzhDQUNDLDhEQUFDckMsaUVBQUtBO29DQUFDc0MsV0FBVTs4Q0FBeUJoQixFQUFFOzs7Ozs7OENBQzVDLDhEQUFDaUI7b0NBQUVELFdBQVU7O3dDQUFlZjt3Q0FBZ0I7d0NBQUVELEVBQUU7Ozs7Ozs7Ozs7Ozs7c0NBRWxELDhEQUFDZTs7OENBQ0MsOERBQUNyQyxpRUFBS0E7b0NBQUNzQyxXQUFVOzhDQUF5QmhCLEVBQUU7Ozs7Ozs4Q0FDNUMsOERBQUNlO29DQUFJQyxXQUFVOztzREFDYiw4REFBQzFDLHlGQUFZQTs0Q0FBQzBDLFdBQVU7Ozs7OztzREFDeEIsOERBQUNDOzRDQUFFRCxXQUFVO3NEQUFlekMsdUZBQU1BLENBQUNlLFdBQVcsY0FBYztnREFBRWdDLFFBQVE3QyxnREFBRUE7NENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FHN0UsOERBQUNzQzs7OENBQ0MsOERBQUNyQyxpRUFBS0E7b0NBQUNzQyxXQUFVOzhDQUF5QmhCLEVBQUU7Ozs7Ozs4Q0FDNUMsOERBQUNlO29DQUFJQyxXQUFVOztzREFDYiw4REFBQzFDLHlGQUFZQTs0Q0FBQzBDLFdBQVU7Ozs7OztzREFDeEIsOERBQUNDOzRDQUFFRCxXQUFVO3NEQUFlekMsdUZBQU1BLENBQUN1QyxnQkFBZ0IsY0FBYztnREFBRVEsUUFBUTdDLGdEQUFFQTs0Q0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTXRGLDhEQUFDc0M7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDTzs4Q0FBTXZCLEVBQUU7Ozs7Ozs4Q0FDVCw4REFBQ3VCOzt3Q0FBTTNCLGNBQWM0QixjQUFjLENBQUM7d0NBQVM7Ozs7Ozs7Ozs7Ozs7d0JBRTlDaEMsMkJBQ0MsOERBQUN1Qjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNPOzt3Q0FBTXZCLEVBQUU7d0NBQWdCO3NEQUFDLDhEQUFDbEIsNkRBQWFBOzRDQUFDMkMsY0FBY25COzs7Ozs7Ozs7Ozs7OENBQ3ZELDhEQUFDaUI7O3dDQUFNWCxlQUFlWSxjQUFjLENBQUM7d0NBQVM7Ozs7Ozs7Ozs7Ozs7c0NBR2xELDhEQUFDVDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNPOzhDQUFNdkIsRUFBRTs7Ozs7OzhDQUNULDhEQUFDdUI7O3dDQUFNVixXQUFXVyxjQUFjLENBQUM7d0NBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXREO0dBakp3QnRDOztRQWNaSCxzREFBZUE7UUFDTEMsZ0VBQVVBOzs7S0FmUkUiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0XFxZRVpfVGVjaFxcWUVaX0hvbWVcXFlFWkhvbWVfRkVcXGNvbXBvbmVudHNcXHVzZXItcHJvcGVydHlcXENyZWF0ZVByb3BlcnR5RGV0YWlsSW5mb3JtYXRpb24uanN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IFN3aXRjaCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvc3dpdGNoXCI7XHJcbmltcG9ydCB7IENhbGVuZGFySWNvbiB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIjtcclxuaW1wb3J0IHsgZm9ybWF0LCBhZGREYXlzIH0gZnJvbSBcImRhdGUtZm5zXCI7XHJcbmltcG9ydCB7IHZpIH0gZnJvbSBcImRhdGUtZm5zL2xvY2FsZVwiO1xyXG5pbXBvcnQgeyBMYWJlbCB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtZHJvcGRvd24tbWVudVwiO1xyXG5pbXBvcnQgeyBERUZBVUxUX1BPU1RfUFJJQ0UsIE1lbWJlclJhbmsgfSBmcm9tIFwiQC9saWIvZW51bVwiO1xyXG5pbXBvcnQgQmFkZ2VTdGF0dXMgZnJvbSBcIi4uL2xheW91dC9CYWRnZVN0YXR1c1wiO1xyXG5pbXBvcnQgQmFkZ2VVc2VyUmFuayBmcm9tIFwiLi4vbGF5b3V0L0JhZGdlVXNlclJhbmtcIjtcclxuaW1wb3J0IHsgdXNlVHJhbnNsYXRpb25zIH0gZnJvbSBcIm5leHQtaW50bFwiO1xyXG5pbXBvcnQgeyB1c2VQcm9maWxlIH0gZnJvbSBcIkAvY29udGV4dHMvUHJvZmlsZUNvbnRleHRcIjtcclxuaW1wb3J0IHsgZ2V0SGlnaGxpZ2h0UHJpY2VOdW1iZXIgfSBmcm9tIFwiQC9saWIvbWVtYmVyUmFua1V0aWxzXCI7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDcmVhdGVQcm9wZXJ0eURldGFpbEluZm9ybWF0aW9uKHtcclxuICBzdGF0dXMsXHJcbiAgc3RhdHVzVGV4dCxcclxuICBpc0Zvcm1EaXNhYmxlZCxcclxuICBjcmVhdGVkQXQsXHJcbiAgZXhwaXJlc0F0LFxyXG4gIGhpZ2hsaWdodCxcclxuICBzZXRIaWdobGlnaHQsXHJcbiAgYXV0b1JlbmV3LFxyXG4gIHNldEF1dG9SZW5ldyxcclxuICBiYXNlUG9zdFByaWNlID0gREVGQVVMVF9QT1NUX1BSSUNFLFxyXG4gIG9uUmFua0NoYW5nZSA9ICgpID0+IHt9LFxyXG4gIG9uUmVmcmVzaFJlZiA9IG51bGwsIC8vIFJlZiB0byBleHBvc2UgcmVmcmVzaCBmdW5jdGlvblxyXG59KSB7XHJcbiAgY29uc3QgdCA9IHVzZVRyYW5zbGF0aW9ucyhcIkNyZWF0ZVByb3BlcnR5RGV0YWlsSW5mb3JtYXRpb25cIik7XHJcbiAgY29uc3QgeyBwcm9maWxlIH0gPSB1c2VQcm9maWxlKCk7XHJcbiAgY29uc3QgZGlzcGxheUR1cmF0aW9uID0gMTA7IC8vIGRheXNcclxuXHJcbiAgLy8gR2V0IHRoZSB1c2VyJ3MgcmFuayBmcm9tIHByb2ZpbGUgY29udGV4dFxyXG4gIGNvbnN0IHVzZXJSYW5rRnJvbVByb2ZpbGUgPSBwcm9maWxlPy51c2VyPy5tZW1iZXJSYW5rIHx8IE1lbWJlclJhbmsuREVGQVVMVDtcclxuXHJcbiAgLy8gU3RhdGUgdG8gdHJhY2sgdGhlIGN1cnJlbnQgcmFuayAoY2FuIGJlIHVwZGF0ZWQgYnkgQmFkZ2VVc2VyUmFuayBjb21wb25lbnQpXHJcbiAgY29uc3QgW2N1cnJlbnRSYW5rLCBzZXRDdXJyZW50UmFua10gPSB1c2VTdGF0ZSh1c2VyUmFua0Zyb21Qcm9maWxlKTtcclxuXHJcbiAgY29uc3Qgb25DaGVja2VkQ2hhbmdlID0gKGNoZWNrZWQpID0+IHtcclxuICAgIGlmIChpc0Zvcm1EaXNhYmxlZCkgcmV0dXJuO1xyXG4gICAgY29uc29sZS5sb2coXCJDaGVja2VkOlwiLCBjaGVja2VkKTtcclxuICAgIHNldEhpZ2hsaWdodChjaGVja2VkKTtcclxuICB9O1xyXG5cclxuICAvLyBVcGRhdGUgY3VycmVudFJhbmsgd2hlbiBwcm9maWxlIGNoYW5nZXNcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKHByb2ZpbGU/LnVzZXI/Lm1lbWJlclJhbmspIHtcclxuICAgICAgc2V0Q3VycmVudFJhbmsocHJvZmlsZS51c2VyLm1lbWJlclJhbmspO1xyXG4gICAgfVxyXG4gIH0sIFtwcm9maWxlXSk7XHJcblxyXG4gIC8vIENhbGN1bGF0ZSBoaWdobGlnaHQgcHJpY2UgdXNpbmcgdGhlIHV0aWxpdHkgZnVuY3Rpb25cclxuICBjb25zdCBoaWdobGlnaHRQcmljZSA9IGdldEhpZ2hsaWdodFByaWNlTnVtYmVyKGN1cnJlbnRSYW5rKTtcclxuICBjb25zdCB0b3RhbFByaWNlID0gYmFzZVBvc3RQcmljZSArIChoaWdobGlnaHQgPyBoaWdobGlnaHRQcmljZSA6IDApO1xyXG5cclxuICBjb25zdCBleHBpcmF0aW9uRGF0ZSA9IGV4cGlyZXNBdCB8fCBhZGREYXlzKGNyZWF0ZWRBdCwgZGlzcGxheUR1cmF0aW9uKTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDw+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XHJcbiAgICAgICAgPGRpdj5cclxuICAgICAgICAgIDxMYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmRcIj57dCgnY3VzdG9tZXJUeXBlJyl9PC9MYWJlbD5cclxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e3QoJ2luZGl2aWR1YWxDdXN0b21lcicpfTwvcD5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgPExhYmVsIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPnt0KCdtZW1iZXJSYW5rJyl9PC9MYWJlbD5cclxuICAgICAgICAgIDxCYWRnZVVzZXJSYW5rXHJcbiAgICAgICAgICAgIHNob3dSZWZyZXNoQnV0dG9uPXt0cnVlfVxyXG4gICAgICAgICAgICBvblJhbmtDaGFuZ2U9eyhyYW5rRGF0YSkgPT4ge1xyXG4gICAgICAgICAgICAgIC8vIE9ubHkgdXBkYXRlIGlmIHRoZSByYW5rIGhhcyBhY3R1YWxseSBjaGFuZ2VkXHJcbiAgICAgICAgICAgICAgaWYgKHJhbmtEYXRhLmN1cnJlbnRSYW5rICE9PSBjdXJyZW50UmFuaykge1xyXG4gICAgICAgICAgICAgICAgc2V0Q3VycmVudFJhbmsocmFua0RhdGEuY3VycmVudFJhbmspO1xyXG4gICAgICAgICAgICAgICAgb25SYW5rQ2hhbmdlKHJhbmtEYXRhKTtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgIG9uUmVmcmVzaFJlZj17b25SZWZyZXNoUmVmfVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci10IHB0LTNcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJoaWdobGlnaHRcIiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPlxyXG4gICAgICAgICAgICAgIHt0KCdoaWdobGlnaHRQb3N0Jyl9XHJcbiAgICAgICAgICAgIDwvTGFiZWw+XHJcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XHJcbiAgICAgICAgICAgICAge3QoJ2hpZ2hsaWdodFBvc3REZXNjcmlwdGlvbicpfVxyXG4gICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIHshaXNGb3JtRGlzYWJsZWQgJiYgKFxyXG4gICAgICAgICAgICA8U3dpdGNoIGlkPVwiaGlnaGxpZ2h0XCIgY2hlY2tlZD17aGlnaGxpZ2h0fSBvbkNoZWNrZWRDaGFuZ2U9e29uQ2hlY2tlZENoYW5nZX0gLz5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItdCBwdC0zXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiaGlnaGxpZ2h0XCIgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj5cclxuICAgICAgICAgICAgICB7dCgnYXV0b1JlbmV3Jyl9XHJcbiAgICAgICAgICAgIDwvTGFiZWw+XHJcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XHJcbiAgICAgICAgICAgICAge3QoJ2F1dG9SZW5ld0Rlc2NyaXB0aW9uJyl9XHJcbiAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgeyFpc0Zvcm1EaXNhYmxlZCAmJiAoXHJcbiAgICAgICAgICAgIDxTd2l0Y2ggaWQ9XCJhdXRvLXJlbmV3XCIgY2hlY2tlZD17YXV0b1JlbmV3fSBvbkNoZWNrZWRDaGFuZ2U9e3NldEF1dG9SZW5ld30gLz5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItdCBwdC0zXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cclxuICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgIDxMYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmRcIj57dCgncG9zdFN0YXR1cycpfTwvTGFiZWw+XHJcbiAgICAgICAgICAgIDxCYWRnZVN0YXR1cyBzdGF0dXM9e3N0YXR1c30gc3RhdHVzVGV4dD17c3RhdHVzVGV4dH0+PC9CYWRnZVN0YXR1cz5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgPExhYmVsIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPnt0KCdleHBpcmF0aW9uVGltZScpfTwvTGFiZWw+XHJcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e2Rpc3BsYXlEdXJhdGlvbn0ge3QoJ2RheXMnKX08L3A+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgIDxMYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmRcIj57dCgncG9zdERhdGUnKX08L0xhYmVsPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgPENhbGVuZGFySWNvbiBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiIC8+XHJcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57Zm9ybWF0KGNyZWF0ZWRBdCwgXCJkZC9NTS95eXl5XCIsIHsgbG9jYWxlOiB2aSB9KX08L3A+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICA8TGFiZWwgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+e3QoJ2V4cGlyYXRpb25EYXRlJyl9PC9MYWJlbD5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgIDxDYWxlbmRhckljb24gY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIiAvPlxyXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e2Zvcm1hdChleHBpcmF0aW9uRGF0ZSwgXCJkZC9NTS95eXl5XCIsIHsgbG9jYWxlOiB2aSB9KX08L3A+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItdCBwdC0zXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTFcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICAgICAgPHNwYW4+e3QoJ3Bvc3RGZWUnKX08L3NwYW4+XHJcbiAgICAgICAgICAgIDxzcGFuPntiYXNlUG9zdFByaWNlLnRvTG9jYWxlU3RyaW5nKFwidmktVk5cIil9IMSRPC9zcGFuPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICB7aGlnaGxpZ2h0ICYmIChcclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBtYi0zIHBiLTMgXCI+XHJcbiAgICAgICAgICAgICAgPHNwYW4+e3QoJ2hpZ2hsaWdodEZlZScpfSA8QmFkZ2VVc2VyUmFuayBleHRlcm5hbFJhbms9e2N1cnJlbnRSYW5rfSAvPjwvc3Bhbj5cclxuICAgICAgICAgICAgICA8c3Bhbj57aGlnaGxpZ2h0UHJpY2UudG9Mb2NhbGVTdHJpbmcoXCJ2aS1WTlwiKX0gxJE8L3NwYW4+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgKX1cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gZm9udC1ib2xkIHRleHQtbGcgcHQtMiBib3JkZXItdFwiPlxyXG4gICAgICAgICAgICA8c3Bhbj57dCgndG90YWwnKX08L3NwYW4+XHJcbiAgICAgICAgICAgIDxzcGFuPnt0b3RhbFByaWNlLnRvTG9jYWxlU3RyaW5nKFwidmktVk5cIil9IMSRPC9zcGFuPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC8+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJTd2l0Y2giLCJDYWxlbmRhckljb24iLCJmb3JtYXQiLCJhZGREYXlzIiwidmkiLCJMYWJlbCIsIkRFRkFVTFRfUE9TVF9QUklDRSIsIk1lbWJlclJhbmsiLCJCYWRnZVN0YXR1cyIsIkJhZGdlVXNlclJhbmsiLCJ1c2VUcmFuc2xhdGlvbnMiLCJ1c2VQcm9maWxlIiwiZ2V0SGlnaGxpZ2h0UHJpY2VOdW1iZXIiLCJDcmVhdGVQcm9wZXJ0eURldGFpbEluZm9ybWF0aW9uIiwic3RhdHVzIiwic3RhdHVzVGV4dCIsImlzRm9ybURpc2FibGVkIiwiY3JlYXRlZEF0IiwiZXhwaXJlc0F0IiwiaGlnaGxpZ2h0Iiwic2V0SGlnaGxpZ2h0IiwiYXV0b1JlbmV3Iiwic2V0QXV0b1JlbmV3IiwiYmFzZVBvc3RQcmljZSIsIm9uUmFua0NoYW5nZSIsIm9uUmVmcmVzaFJlZiIsInByb2ZpbGUiLCJ0IiwiZGlzcGxheUR1cmF0aW9uIiwidXNlclJhbmtGcm9tUHJvZmlsZSIsInVzZXIiLCJtZW1iZXJSYW5rIiwiREVGQVVMVCIsImN1cnJlbnRSYW5rIiwic2V0Q3VycmVudFJhbmsiLCJvbkNoZWNrZWRDaGFuZ2UiLCJjaGVja2VkIiwiY29uc29sZSIsImxvZyIsImhpZ2hsaWdodFByaWNlIiwidG90YWxQcmljZSIsImV4cGlyYXRpb25EYXRlIiwiZGl2IiwiY2xhc3NOYW1lIiwicCIsInNob3dSZWZyZXNoQnV0dG9uIiwicmFua0RhdGEiLCJodG1sRm9yIiwiaWQiLCJsb2NhbGUiLCJzcGFuIiwidG9Mb2NhbGVTdHJpbmciLCJleHRlcm5hbFJhbmsiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/user-property/CreatePropertyDetailInformation.jsx\n"));

/***/ })

});