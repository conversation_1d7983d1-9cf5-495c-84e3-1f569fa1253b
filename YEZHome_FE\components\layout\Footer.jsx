import { Mail, Phone } from "lucide-react";
import Image from "next/image";
import {Link} from '@/i18n/navigation';;

export default function Footer() {
  return (
    <footer className="bg-navy-blue p-8">
      <div className="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Left Section - Company Info */}
        <div>
          <div className="flex items-center space-x-2">
            <Image src="/yezhome_logo.png" alt="Logo YezHome" width={150} height={150} className="h-15 p-1" />
          </div>
          <p className="text-coral-500 mt-2">
            <strong>YEZHOME VIỆT NAM</strong>
          </p>
          <p className="text-sm text-white">Tầng 100, Landmark 100, Hồ Chí Minh city</p>
          <p className="text-sm text-white">(084) 1234 1234 - (084) 2345 2345</p>
          {/* QR Code & App Links */}
          <div className="flex items-center space-x-4 mt-4">
            <img src="/qrcode.png" alt="QR Code" className="h-16 bg-white rounded-sm" />
            <div className="flex flex-col space-y-2">
              <img src="/google-play.png" alt="Google Play" className="h-8" />
              <img src="/app-store.png" alt="App Store" className="h-8" />
            </div>
          </div>
        </div>

        {/* Middle Section - Support & Policies */}
        <div className="grid grid-cols-2 gap-6">
          <div>
            <h3 className="text-coral-500 font-semibold">HƯỚNG DẪN</h3>
            <ul className="text-sm text-white space-y-1 mt-2">
              <li>
                <Link href="/about" className="hover:text-teal-600">
                  Về chúng tôi
                </Link>
              </li>
              <li>
                <Link href="/pricing" className="hover:text-teal-600">
                  Báo giá và hỗ trợ
                </Link>
              </li>
              <li>
                <Link href="/faq" className="hover:text-teal-600">
                  Câu hỏi thường gặp
                </Link>
              </li>
              <li>
                <Link href="/feedback" className="hover:text-teal-600">
                  Góp ý báo lỗi
                </Link>
              </li>
              <li>
                <Link href="/sitemap" className="hover:text-teal-600">
                  Sitemap
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="text-coral-500 font-semibold">QUY ĐỊNH</h3>
            <ul className="text-sm text-white space-y-1 mt-2">
              <li>
                <Link href="/rules/posting" className="hover:text-teal-600">
                  Quy định đăng tin
                </Link>
              </li>
              <li>
                <Link href="/rules/operation" className="hover:text-teal-600">
                  Quy chế hoạt động
                </Link>
              </li>
              <li>
                <Link href="/terms" className="hover:text-teal-600">
                  Điều khoản thỏa thuận
                </Link>
              </li>
              <li>
                <Link href="/privacy" className="hover:text-teal-600">
                  Chính sách bảo mật
                </Link>
              </li>
              <li>
                <Link href="/complaints" className="hover:text-teal-600">
                  Giải quyết khiếu nại
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Right Section - Contact & Subscription */}
        <div>
          <div className="space-y-2">
            <div className="flex items-center space-x-2 text-white">
              <Phone />
              <span className="font-semibold">Hotline: 1900 1234</span>
            </div>
            <div className="flex items-center space-x-2 text-white">
              <Mail />
              <a href="mailto:trogiup.batdongsan.com.vn" className="text-sm">
                trogiup.yezhome.com.vn
              </a>
            </div>
            <div className="flex items-center space-x-2 text-white">
              <Mail />
              <a href="mailto:<EMAIL>" className="text-sm">
                <EMAIL>
              </a>
            </div>
          </div>

          {/* Email Subscription */}
          <div className="mt-4">
            <h3 className="text-coral-500 font-semibold">ĐĂNG KÝ NHẬN TIN</h3>
            <div className="flex mt-2">
              <input
                type="email"
                placeholder="Nhập email của bạn"
                className="p-2 border rounded-l w-full"
              />
              <button className="bg-red-500 text-white px-4 rounded-r">➤</button>
            </div>
          </div>
        </div>
      </div>
      <div className="text-center text-sm text-white mt-8">
        © 2025 YEZHOME. Tất cả các quyền được bảo lưu.
      </div>
    </footer>
  );
}
