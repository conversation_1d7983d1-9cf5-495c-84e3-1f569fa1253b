{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/api/map/districts/route.jsx"], "sourcesContent": ["export async function GET(request) {\n  try {\n    const API_URL = process.env.API_URL;\n    const { searchParams } = new URL(request.url);\n    const cityId = searchParams.get('cityId');\n    \n    if (!cityId) {\n      return Response.json(\n        { error: 'City ID is required' },\n        { status: 400 }\n      );\n    }\n\n    const response = await fetch(`${API_URL}/api/Address/cities/${cityId}/districts`, {\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    if (!response.ok) {\n      throw new Error(`Failed to fetch districts: ${response.status}`);\n    }\n\n    const data = await response.json();\n    return Response.json(data);\n  } catch (error) {\n    console.error('Error fetching districts:', error);\n    return Response.json(\n      { error: 'Failed to fetch districts' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAO,eAAe,IAAI,OAAO;IAC/B,IAAI;QACF,MAAM,UAAU,QAAQ,GAAG,CAAC,OAAO;QACnC,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,IAAI,CAAC,QAAQ;YACX,OAAO,SAAS,IAAI,CAClB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,oBAAoB,EAAE,OAAO,UAAU,CAAC,EAAE;YAChF,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,SAAS,MAAM,EAAE;QACjE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,SAAS,IAAI,CAAC;IACvB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,SAAS,IAAI,CAClB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}