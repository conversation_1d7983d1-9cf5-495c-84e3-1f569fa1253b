import { RotateCw } from "lucide-react";

export default function ButtonLoading({ ...props }) {
  const { showLoading, type, title } = props;
  return (
    <button
      type={type}
      disabled={showLoading}
      className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-navy-blue hover:border-teal-700  hover:bg-teal-700 transition-all
      disabled:opacity-50 disabled:cursor-not-allowed"
    >
      {showLoading ? (
        <>
          <RotateCw className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" />
          <PERSON>ang xử lý ...
        </>
      ) : (
        <>{title}</>
      )}
    </button>
  );
}
