"use client";

import { useState, useCallback, useEffect, memo } from "react";
import { useDropzone } from "react-dropzone";
import {
  uploadPropertyImages,
  updatePropertyMediaCaption,
  updatePropertyMediaIsAvatar,
  deletePropertyMedia
} from "@/app/actions/server/property";
import { useToast } from "@/hooks/use-toast";
import { Upload, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useTranslations } from "next-intl";
import ImageCard from "@/components/ui/ImageCard";

const PropertyImageUploader = ({ propertyId, onUploadComplete, initialImages = [], isFormDisabled }) => {
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [isUploading, setIsUploading] = useState(false);
  const [avatarImageId, setAvatarImageId] = useState(null);
  const [captions, setCaptions] = useState({});
  const [showImageGallery, setShowImageGallery] = useState(false);
  const { toast } = useToast();
  const t = useTranslations("PropertyImageUploader");

  const [debouncedCaptionId, setDebouncedCaptionId] = useState(null);

  useEffect(() => {
    if (initialImages && initialImages.length > 0) {
      // Only update if the arrays are different
      if (JSON.stringify(initialImages) !== JSON.stringify(uploadedFiles)) {
        setUploadedFiles(initialImages);

        // Initialize captions
        const initialCaptions = {};
        initialImages.forEach((img) => {
          if (img.caption) {
            initialCaptions[img.id] = img.caption;
          }
          // Set avatar image if found
          if (img.isAvatar) {
            setAvatarImageId(img.id);
          }
        });

        setCaptions(initialCaptions);
      }
    }
  }, [initialImages]);

  const onDrop = useCallback(
    async (acceptedFiles) => {
      setIsUploading(true);

      // Validate files before uploading
      const MAX_FILE_SIZE = 1048576; // 1MB in bytes
      const validFiles = [];
      const invalidFiles = [];

      // Check each file for type and size
      for (const file of acceptedFiles) {
        // Check if file is an image
        if (!file.type.startsWith('image/')) {
          invalidFiles.push({ file, reason: 'type' });
          continue;
        }

        // Check file size
        if (file.size > MAX_FILE_SIZE) {
          invalidFiles.push({ file, reason: 'size' });
          continue;
        }

        // File passed all checks
        validFiles.push(file);
      }

      // Show error messages for invalid files
      if (invalidFiles.length > 0) {
        const typeErrors = invalidFiles.filter(item => item.reason === 'type');
        const sizeErrors = invalidFiles.filter(item => item.reason === 'size');

        if (typeErrors.length > 0) {
          toast({
            title: t('error') || 'Error',
            description: typeErrors.length === 1
              ? `${typeErrors[0].file.name} is not a valid image file.`
              : `${typeErrors.length} files are not valid image files.`,
            variant: "destructive",
            className: "bg-red-600 text-white",
          });
        }

        if (sizeErrors.length > 0) {
          toast({
            title: t('error') || 'Error',
            description: sizeErrors.length === 1
              ? `${sizeErrors[0].file.name} exceeds the 1MB size limit.`
              : `${sizeErrors.length} files exceed the 1MB size limit.`,
            variant: "destructive",
            className: "bg-red-600 text-white",
          });
        }

        // If no valid files, stop the upload process
        if (validFiles.length === 0) {
          setIsUploading(false);
          return;
        }
      }

      const formData = new FormData();
      formData.append("propertyId", propertyId ?? "");
      validFiles.forEach((file) => formData.append("files", file));

      try {
        const result = await uploadPropertyImages(null, formData);

        if (!result.success) {
          throw new Error(result.message);
        }

        // Add unique IDs to the uploaded files
        const filesWithIds = result?.data?.map((file) => ({
          ...file,
          caption: captions[file.id] || null,
          isAvatar: file.id === avatarImageId,
        }));

        // Update state with new files
        const updatedFiles = [...uploadedFiles, ...filesWithIds];
        setUploadedFiles(updatedFiles);

        toast({
          description: "Tải lên thành công!",
          className: "bg-teal-600 text-white",
        });

        // Call the parent callback with the updated files
        if (onUploadComplete) {
          onUploadComplete(updatedFiles);
        }
      } catch (error) {
        // Check if it's the size limit error
        if (error.message && (error.message.includes('Body exceeded') || error.message.includes('limit'))) {
          toast({
            title: t('submissionErrorTitle'),
            description: t('submissionErrorSizeLimit'),
            variant: "destructive",
            className: "bg-red-600 text-white",
          });
        } else {
          // Show generic error for other issues
          toast({
            title: t('error'),
            description: error.message || "An unknown error occurred during upload.",
            variant: "destructive",
            className: "bg-red-600 text-white",
          });
        }
      } finally {
        setIsUploading(false);
      }
    },
    [propertyId, toast, uploadedFiles, captions, avatarImageId, onUploadComplete, t]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: true,
    accept: {
      "image/*": [".jpeg", ".png", ".jpg", ".webp"],
    },
    disabled: isFormDisabled,
  });

  const handleSetAvatar = (fileId) => {
    setAvatarImageId(fileId);

    // Update the isAvatar property for all files
    const updatedFiles = uploadedFiles.map((file) => ({
      ...file,
      isAvatar: file.id === fileId,
    }));

    setUploadedFiles(updatedFiles);

    // Notify parent component
    if (onUploadComplete) {
      onUploadComplete(updatedFiles);
    }
  };

  const handleDeleteImage = async (fileId) => {
    try {
      // Call the API to delete the image
      const result = await deletePropertyMedia(fileId);

      if (!result.success) {
        throw new Error(result.message || 'Failed to delete image');
      }

      const updatedFiles = uploadedFiles.filter((file) => file.id !== fileId);
      setUploadedFiles(updatedFiles);

      // If we're deleting the avatar image, clear the avatar
      if (avatarImageId === fileId) {
        setAvatarImageId(null);
      }

      // Remove the caption for this image
      const newCaptions = { ...captions };
      delete newCaptions[fileId];
      setCaptions(newCaptions);

      // Notify parent component
      if (onUploadComplete) {
        onUploadComplete(updatedFiles);
      }

      toast({
        description: t('deleteImageSuccess'),
        className: "bg-teal-600 text-white",
      });
    } catch (error) {
      toast({
        title: t('error') || 'Error',
        description: error.message || 'Failed to delete image',
        variant: "destructive",
        className: "bg-red-600 text-white",
      });
    }
  };

  const handleCaptionChange = (fileId, caption) => {
    // Only update the captions state (lightweight operation)
    setCaptions((prev) => ({
      ...prev,
      [fileId]: caption,
    }));

    // Set a debounce identifier
    if (debouncedCaptionId) clearTimeout(debouncedCaptionId);

    // Debounce the heavy operations
    const timeoutId = setTimeout(() => {
      // Update uploadedFiles and notify parent only after typing stops
      const updatedFiles = uploadedFiles.map((file) => {
        if (file.id === fileId) {
          return { ...file, caption };
        }
        return file;
      });

      setUploadedFiles(updatedFiles);

      // Notify parent component
      if (onUploadComplete) {
        onUploadComplete(updatedFiles);
      }
    }, 500); // 500ms debounce

    setDebouncedCaptionId(timeoutId);
  };

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (debouncedCaptionId) clearTimeout(debouncedCaptionId);
    };
  }, [debouncedCaptionId]);

  return (
    <div className="space-y-6">
      {/* Conditionally render the dropzone */}
      {!isFormDisabled && (
        <div
          {...getRootProps()}
          className={`
            relative flex flex-col items-center justify-center
            border-2 border-dashed rounded-lg p-8
            transition-colors duration-200 ease-in-out
            ${
              isDragActive
                ? "border-primary bg-primary/5"
                : "border-gray-300 hover:border-primary hover:bg-gray-50"
            }
          `}
        >
          <div className="flex flex-col items-center text-center space-y-2">
            <div className="p-3 rounded-full bg-primary/10">
              <Upload className="w-8 h-8 text-primary" />
            </div>
            <input {...getInputProps()} />

            <div className="space-y-1">
              <h3 className="text-lg font-medium">
                {isDragActive ? t('dropHere') : t('uploadPropertyImage')}
              </h3>
              <p className="text-sm text-muted-foreground">
                {t('dragAndDrop')}
              </p>
            </div>

            <div className="mt-2 text-xs text-muted-foreground">
              {t('supportedFormats')}
            </div>
            <div className="text-xs text-muted-foreground">
              {t('maxFileSize') || 'Maximum file size: 1MB'}
            </div>
          </div>
        </div>
      )}

      {isUploading && (
        <div className="flex items-center justify-center p-4 bg-primary/10 rounded-lg">
          <Loader2 className="w-5 h-5 text-primary mr-2 animate-spin" />
          <span className="font-medium text-primary">{t('uploadingImage')}</span>
        </div>
      )}

      {uploadedFiles.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">{t('uploadedImages')}</h2>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="px-2 py-1">
                {uploadedFiles.length} {uploadedFiles.length === 1 ? t('image') : t('images')}
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  setShowImageGallery(true);
                }}
                disabled={isFormDisabled}
              >
                {t('edit')} ({uploadedFiles.length})
              </Button>
            </div>
          </div>

          {/* Combined Image Gallery and Management */}
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            {uploadedFiles.slice(0, 8).map((file, index) => (
              <ImageCard
                key={file.id}
                file={file}
                caption={captions[file.id] || file.caption || ""}
                isAvatar={avatarImageId === file.id}
                onCaptionChange={handleCaptionChange}
                onSetAvatar={handleSetAvatar}
                onDelete={handleDeleteImage}
                onClick={() => {
                  setShowImageGallery(true);
                }}
                showMoreCount={index === 7 && uploadedFiles.length > 8 ? uploadedFiles.length - 8 : 0}
                isFormDisabled={isFormDisabled}
              />
            ))}
          </div>
        </div>
      )}

      {/* Updated Image Gallery Dialog */}
      <Dialog open={showImageGallery} onOpenChange={setShowImageGallery}>
        <DialogContent className="max-w-5xl h-[80vh] flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex justify-between items-center">
              <span>{t('manageImages')} ({uploadedFiles.length})</span>
            </DialogTitle>
          </DialogHeader>

          <div className="flex-grow overflow-y-auto p-4">
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
              {uploadedFiles.map((file) => (
                <ImageCard
                  key={file.id}
                  file={file}
                  caption={captions[file.id] || file.caption || ""}
                  isAvatar={avatarImageId === file.id}
                  onCaptionChange={handleCaptionChange}
                  onSetAvatar={handleSetAvatar}
                  onDelete={handleDeleteImage}
                  showMoreCount={0}
                  isFormDisabled={isFormDisabled}
                />
              ))}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PropertyImageUploader;
