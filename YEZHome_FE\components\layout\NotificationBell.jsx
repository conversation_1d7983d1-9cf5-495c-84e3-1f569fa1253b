"use client";

import { memo, useState, useEffect } from "react";
import { Bell, FileText, Wallet, Gift, MessageCircle, ExternalLink } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Link } from "@/i18n/navigation";
import { useTranslations, useLocale } from "next-intl";
import { getLatestNotifications, getUnreadCount, markAsRead } from "@/app/actions/server/notification";
import { NOTIFICATION_TYPE } from "@/lib/enum";

// Global variables to ensure only one instance runs
// This is outside the component to ensure it's truly global
let globalTimeoutId = null;
let isInitialFetchDone = false;

// NotificationBell component
const NotificationBell = memo(() => {
  const t = useTranslations("Navbar");
  const locale = useLocale();
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [lastFetchTime, setLastFetchTime] = useState(0);

  const fetchNotifications = async () => {
    // Prevent duplicate fetches within a short time period (500ms)
    const now = Date.now();
    if (now - lastFetchTime < 500) {
      return;
    }

    setLastFetchTime(now);
    setLoading(true);
    try {
      const countResponse = await getUnreadCount();
      console.log("countResponse", countResponse);
      if (countResponse.success) {
        setUnreadCount(countResponse.data.count || 0);
      }
      const notifyResponse = await getLatestNotifications(10);
      console.log("notifyResponse", notifyResponse);
      if (notifyResponse.success) {
        setNotifications(notifyResponse.data || []);
      }
    } catch (error) {
      console.error("Error fetching notifications:", error);
      // Optionally show a toast error here using a translated message
    } finally {
      setLoading(false);
    }
  };

  // This effect runs once on mount and sets up the initial fetch
  useEffect(() => {
    // Only do the initial fetch if it hasn't been done yet
    if (!isInitialFetchDone) {
      fetchNotifications();
      isInitialFetchDone = true;
    }

    // Set up a single timeout instead of an interval
    const setupNextFetch = () => {
      // Only set up a new timeout if there isn't one already
      if (globalTimeoutId === null) {
        // Set a new timeout
        globalTimeoutId = setTimeout(() => {
          fetchNotifications();

          // Clear the global timeout ID before setting up the next one
          globalTimeoutId = null;

          // Set up the next timeout after this one completes
          setupNextFetch();
        }, 120000); // 2 minutes
      }
    };

    // Only start the chain of timeouts if there isn't one already
    if (globalTimeoutId === null) {
      setupNextFetch();
    }

    // Clean up on unmount
    return () => {
      // We don't clear the global timeout here because we want it to continue
      // even if this component instance unmounts
    };
  }, []);

  useEffect(() => {
    if (open) {
      fetchNotifications();
    }
  }, [open]);

  const handleMarkAsRead = async (id, e) => {
    e.preventDefault(); // Prevent link navigation
    e.stopPropagation();
    try {
      const response = await markAsRead({ ids: [id] });
      if (response.success) {
        setNotifications((prev) => prev.map((n) => (n.id === id ? { ...n, read: true } : n)));
        setUnreadCount((prev) => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error("Error marking notification as read:", error);
      // Optionally show a toast error here using a translated message
    }
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case NOTIFICATION_TYPE.SYSTEM:
        return <FileText className="h-4 w-4 text-blue-500" />;
      case NOTIFICATION_TYPE.TRANSACTION:
        return <Wallet className="h-4 w-4 text-green-500" />;
      case NOTIFICATION_TYPE.PROMOTION:
        return <Gift className="h-4 w-4 text-purple-500" />;
      case NOTIFICATION_TYPE.CONTACT:
        return <MessageCircle className="h-4 w-4 text-orange-500" />;
      default:
        return <Bell className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatNotificationTime = (dateString) => {
    if (!dateString) return "";
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffMs = now - date;
      const diffSeconds = Math.floor(diffMs / 1000);
      const diffMinutes = Math.floor(diffSeconds / 60);
      const diffHours = Math.floor(diffMinutes / 60);
      const diffDays = Math.floor(diffHours / 24);

      if (diffMinutes < 60) {
        // Use ICU message format for plurals if available in next-intl setup
        return t("notificationTimeMinutes", { count: diffMinutes });
      } else if (diffHours < 24) {
        return t("notificationTimeHours", { count: diffHours });
      } else if (diffDays < 7) {
        return t("notificationTimeDays", { count: diffDays });
      } else {
        return date.toLocaleDateString(locale, { day: "2-digit", month: "2-digit", year: "numeric" });
      }
    } catch (error) {
      console.error("Error formatting notification time:", error);
      return "";
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <div className="relative cursor-pointer">
          <Bell className="h-6 w-6 hover:text-coral-600" />
          {unreadCount > 0 && (
            <span className="absolute -top-2 -right-2 bg-coral-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
              {unreadCount > 99 ? "99+" : unreadCount}
            </span>
          )}
        </div>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <div className="p-4 border-b">
          <h3 className="font-semibold">{t("notificationTitle")}</h3>
        </div>

        <div className="max-h-72 overflow-auto">
          {loading && notifications.length === 0 ? (
            <div className="flex justify-center items-center h-20">
              <div className="animate-spin h-5 w-5 border-2 border-primary rounded-full border-t-transparent"></div>
            </div>
          ) : notifications.length > 0 ? (
            notifications.map((notification) => (
              <Link
                key={notification.id}
                href="/user/notifications" // Link to the main notifications page
                className={`block border-b last:border-0 p-3 hover:bg-gray-50 ${!notification.read ? "bg-blue-50" : ""}`}
                onClick={() => {
                  if (!notification.read) handleMarkAsRead(notification.id, { preventDefault: () => {}, stopPropagation: () => {} });
                }} // Mark read on click if unread
              >
                <div className="flex gap-3">
                  <div className={`flex-shrink-0 mt-0.5 p-1.5 rounded-full ${notification.read ? "bg-gray-100" : "bg-blue-100"}`}>
                    {getNotificationIcon(notification.type)}
                  </div>
                  <div className="flex-1 overflow-hidden">
                    <div className="flex justify-between items-start gap-2">
                      <p className={`text-sm line-clamp-2 ${notification.read ? "font-normal" : "font-semibold"}`}>
                        {notification.title || t("notificationDefaultTitle")}
                      </p>
                      {!notification.read && (
                        <button
                          onClick={(e) => handleMarkAsRead(notification.id, e)}
                          className="text-xs text-blue-500 hover:underline flex-shrink-0 whitespace-nowrap"
                          aria-label={t("notificationMarkRead")}
                        >
                          {t("notificationMarkRead")}
                        </button>
                      )}
                    </div>
                    <p className="text-xs text-gray-600 mt-1 line-clamp-2">{notification.message}</p>
                    <p className="text-xs text-gray-500 mt-1">{formatNotificationTime(notification.createdAt)}</p>
                  </div>
                </div>
              </Link>
            ))
          ) : (
            <div className="flex flex-col items-center justify-center p-6 text-center">
              <Bell className="h-8 w-8 text-gray-300 mb-2" />
              <p className="text-sm text-gray-500">{t("notificationNone")}</p>
            </div>
          )}
        </div>

        <div className="p-3 border-t text-center">
          <Link href="/user/notifications" className="text-sm text-blue-500 hover:underline flex items-center justify-center gap-1">
            {t("notificationViewAll")}
            <ExternalLink className="h-3.5 w-3.5" />
          </Link>
        </div>
      </PopoverContent>
    </Popover>
  );
});

NotificationBell.displayName = "NotificationBell";

export default NotificationBell;
