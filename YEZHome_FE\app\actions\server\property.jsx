"use server";

import { handleErrorResponse, logError } from "@/lib/apiUtils";
import { fetchWithAuth, getSession, fetchWithoutAuth } from "@/lib/sessionUtils";
import { parseEmptyStringsToNull } from "@/lib/utils";

const API_BASE_URL = `${process.env.API_URL}/api/Property`;

// New function to search properties with filters
export async function searchProperties(filterCriteria) {
  try {
    // Build query parameters from filter criteria
    const queryParams = new URLSearchParams();

    // Transaction Type (postType)
    if (filterCriteria.transactionType && filterCriteria.transactionType.length > 0) {
      filterCriteria.transactionType.forEach(type => {
        queryParams.append('postType', type);
      });
    }

    // Property Type
    if (filterCriteria.propertyType && filterCriteria.propertyType.length > 0) {
      filterCriteria.propertyType.forEach(type => {
        queryParams.append('propertyType', type);
      });
    }

    // Location
    if (filterCriteria.location) {
      if (filterCriteria.location.province) {
        queryParams.append('cityId', filterCriteria.location.province);
      }
      if (filterCriteria.location.district) {
        queryParams.append('districtId', filterCriteria.location.district);
      }
      if (filterCriteria.location.address) {
        queryParams.append('address', filterCriteria.location.address);
      }
    }

    // Price Range
    if (filterCriteria.priceRange) {
      if (filterCriteria.priceRange.min) {
        queryParams.append('minPrice', filterCriteria.priceRange.min);
      }
      if (filterCriteria.priceRange.max) {
        queryParams.append('maxPrice', filterCriteria.priceRange.max);
      }
    }

    // Area Range
    if (filterCriteria.areaRange) {
      if (filterCriteria.areaRange.min) {
        queryParams.append('minArea', filterCriteria.areaRange.min);
      }
      if (filterCriteria.areaRange.max) {
        queryParams.append('maxArea', filterCriteria.areaRange.max);
      }
    }

    // Bedrooms
    if (filterCriteria.bedrooms) {
      queryParams.append('minRooms', filterCriteria.bedrooms);
    }

    // Bathrooms
    if (filterCriteria.bathrooms) {
      queryParams.append('minToilets', filterCriteria.bathrooms);
    }

    // Direction
    if (filterCriteria.direction) {
      queryParams.append('direction', filterCriteria.direction);
    }

    // Legal Status
    if (filterCriteria.legalStatus) {
      queryParams.append('legality', filterCriteria.legalStatus);
    }

    // Road Width
    if (filterCriteria.roadWidth) {
      queryParams.append('minRoadWidth', filterCriteria.roadWidth);
    }

    // User location for proximity search
    if (filterCriteria.sw_lat && filterCriteria.sw_lng && filterCriteria.ne_lat && filterCriteria.ne_lng) {
      queryParams.append('swLat', filterCriteria.sw_lat);
      queryParams.append('swLng', filterCriteria.sw_lng);
      queryParams.append('neLat', filterCriteria.ne_lat);
      queryParams.append('neLng', filterCriteria.ne_lng);

    }

    // Build the URL with query parameters
    const url = `${API_BASE_URL}/search?${queryParams.toString()}`;

    // Use fetchWithoutAuth for public API call
    const response = await fetchWithoutAuth(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    return response; // fetchWithoutAuth already returns the standard response format

  } catch (error) {
    // Handle errors outside fetchWithoutAuth
    console.error(`Lỗi khi gọi fetchWithoutAuth cho searchProperties:`, error);
    return {
      success: false,
      message: "Đã xảy ra lỗi không mong muốn khi tìm kiếm bất động sản",
      errorType: "internal_error",
    };
  }
}

export async function getPropertyById(propertyId) {
  const url = `${API_BASE_URL}/${propertyId}`;
  try {
    // Use fetchWithoutAuth for public API call
    const response = await fetchWithoutAuth(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    return response; // fetchWithoutAuth already returns the standard { success, data, message, errorType } object

  } catch (error) {
    // This catch block might handle errors occurring outside the fetchWithoutAuth call itself
    console.error(`Lỗi khi gọi fetchWithoutAuth cho ${url}:`, error);
    return {
      success: false,
      message: "Đã xảy ra lỗi không mong muốn khi lấy thông tin bất động sản",
      errorType: "network_error",
    };
  }
}

export async function updatePropertyById(prevState, formData) {
  try {
    // Get token from formData
    const propertyId = formData.get("propertyId");

    // Convert FormData to a plain object for easier handling
    const formDataObject = Object.fromEntries(formData.entries());
    const payload = {
      ...formDataObject,
    };

    const userSession = await getSession("UserProfile");
    if (userSession) {
      const user = JSON.parse(userSession);
      payload.ownerId = user.id;
    }

    // ✅ Parse the uploadedFiles JSON string back into an array
    if (formDataObject.UploadedFiles) {
      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);
    }

    payload.IsHighlighted = formDataObject.IsHighlighted === "true" ? true : false;
    payload.IsAutoRenew = formDataObject.IsAutoRenew === "true" ? true : false;

    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {
      method: "PUT",
      body: JSON.stringify(parseEmptyStringsToNull(payload)),
      headers: {
        "Content-Type": "application/json",
        "accept": "*/*"
      },
    });
  } catch (error) {
    logError("PropertyService", error, {
      action: "createProperty",
      formData,
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi tạo bất động sản");
  }
}

export async function deletePropertyById(propertyId) {
  try {
    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    console.error("Lỗi khi xóa bất động sản:", error);
    return {
      success: false,
      message: "Đã xảy ra lỗi khi xóa bất động sản",
      errorType: "network_error",
    };
  }
}

export async function getAllProperties() {
  try {
    // Use fetchWithoutAuth for public API call
    const response = await fetchWithoutAuth(API_BASE_URL, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    return response; // fetchWithoutAuth already returns the standard response format

  } catch (error) {
    // Handle errors outside fetchWithoutAuth
    console.error(`Lỗi khi gọi fetchWithoutAuth cho getAllProperties:`, error);
    return {
      success: false,
      message: "Đã xảy ra lỗi không mong muốn khi lấy danh sách bất động sản",
      errorType: "internal_error",
    };
  }
}

export async function createProperty(prevState, formData) {
  try {
    // Convert FormData to a plain object for easier handling
    const formDataObject = Object.fromEntries(formData.entries());

    const payload = {
      ...formDataObject,
    };

    const userSession = await getSession("UserProfile");
    if (userSession) {
      const user = JSON.parse(userSession);
      payload.ownerId = user.id;
    }

    // ✅ Parse the uploadedFiles JSON string back into an array
    if (formDataObject.UploadedFiles) {
      payload.UploadedFiles = JSON.parse(formDataObject.UploadedFiles);
    }

    payload.IsHighlighted = formDataObject.IsHighlighted === "true" ? true : false;
    payload.IsAutoRenew = formDataObject.IsAutoRenew === "true" ? true : false;

    return await fetchWithAuth(API_BASE_URL, {
      method: "POST",
      body: JSON.stringify(parseEmptyStringsToNull(payload)),
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("PropertyService", error, {
      action: "createProperty",
      formData,
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi tạo bất động sản");
  }
}

export async function updatePropertyStatus(formData) {
  try {
    const propertyId = formData.get("propertyId");

    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/status`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        status: formData.get("status"),
        comment: formData.get("comment") || ""
      }),
    });
  } catch (error) {
    logError("PropertyService", error, {
      action: "updatePropertyStatus",
      formData,
    });
    return handleErrorResponse(false, null, "Không thể cập nhật trạng thái bất động sản");
  }
}

export async function getPropertyByUser(status, page = 1, pageSize = 10) {
  try {
    // If status is 'counts', call the stats endpoint instead
    if (status === 'counts') {
      return await getPropertyStats();
    }

    let url = `${API_BASE_URL}/me`;
    const queryParams = new URLSearchParams();

    // Add parameters if provided
    if (status) {
      queryParams.append('status', status);
    }

    queryParams.append('page', page);
    queryParams.append('pageSize', pageSize);

    // Append query parameters to URL if any exist
    if (queryParams.toString()) {
      url += `?${queryParams.toString()}`;
    }

    const response = await fetchWithAuth(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    return response;
  } catch (error) {
    console.error("Lỗi khi lấy thông tin bất động sản của người dùng:", error);
    return {
      success: false,
      message: "Đã xảy ra lỗi khi lấy thông tin bất động sản của người dùng",
      errorType: "network_error",
    };
  }
}

/**
 * Get property statistics by status
 * @returns {Promise<Object>} Response with property stats data
 */
export async function getPropertyStats() {
  try {
    const response = await fetchWithAuth(`${API_BASE_URL}/stats`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (response.success) {
      // Transform the response to match the expected format for the UI
      const statsData = response.data;
      return {
        success: true,
        data: {
          total: statsData.totalProperties || 0,
          approved: statsData.propertiesByStatus?.Approved || 0,
          pendingApproval: statsData.propertiesByStatus?.PendingApproval || 0,
          rejectedByAdmin: statsData.propertiesByStatus?.RejectedByAdmin || 0,
          rejectedDueToUnpaid: statsData.propertiesByStatus?.RejectedDueToUnpaid || 0,
          waitingPayment: statsData.propertiesByStatus?.WaitingPayment || 0,
          expired: statsData.propertiesByStatus?.Expired || 0,
          draft: statsData.propertiesByStatus?.Draft || 0,
          sold: statsData.propertiesByStatus?.Sold || 0,
        }
      };
    }

    return response;
  } catch (error) {
    console.error("Lỗi khi lấy thống kê bất động sản:", error);
    return {
      success: false,
      message: "Đã xảy ra lỗi khi lấy thống kê bất động sản",
      errorType: "network_error",
    };
  }
}

export async function uploadPropertyImages(prevState, formData) {
  try {
    const files = formData.getAll("files"); // Get all files from FormData

    if (files.length === 0) {
      return handleErrorResponse(false, null, "Thiếu tập tin");
    }

    const formDataToSend = new FormData();
    files.forEach((file) => formDataToSend.append("files", file));

    return await fetchWithAuth(`${API_BASE_URL}/upload-images`, {
      method: "POST",
      body: formDataToSend,
    });
  } catch (error) {
    logError("PropertyService", error, { action: "uploadPropertyImages", formData });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi tải lên hình ảnh");
  }
}

export async function verifyPropertyRemainingTimes(propertyId) {
  try {
    return await fetchWithAuth(`${API_BASE_URL}/edit-remaining/${propertyId}`, {
      method: "GET",
    });
  } catch (error) {
    console.error("Error checking remaining verification times:", error);
    return {
      success: false,
      message: "Đã xảy ra lỗi khi kiểm tra số lần xác thực.",
      errorType: "network_error",
    };
  }
}

export async function getHistoryByPropertyId(propertyId) {
  try {
    return await fetchWithAuth(`${API_BASE_URL}/history/${propertyId}`, {
      method: "GET",
    });
  } catch (error) {
    console.error("Error fetching property history:", error);
    return {
      success: false,
      message: "Đã xảy ra lỗi khi lấy lịch sử hoạt động",
      errorType: "network_error",
    };
  }
}

export async function getNearbyProperties(latitude, longitude, radius = 5000) {
  try {
    // Use fetchWithoutAuth for public API call
    return await fetchWithoutAuth(
      `${API_BASE_URL}/nearby?latitude=${latitude}&longitude=${longitude}&radius=${radius}`, {
        method: "GET",
      }
    );
  } catch (error) {
    console.error("Error fetching nearby properties:", error);
    return {
        success: false,
        message: "Đã xảy ra lỗi khi tìm kiếm bất động sản lân cận",
        errorType: "network_error",
    };
  }
}

// New function to get property report data (Placeholder)
export async function getPropertyReportById(propertyId) {
  "use server"; // Ensure this runs on the server

  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 750));

  try {
    // --- FAKE DATA ---
    // In a real scenario, you would fetch this from your API:
    // const response = await fetchWithAuth(`${API_BASE_URL}/report/${propertyId}`);
    // if (!response.success) return response;
    // const reportData = response.data;

    // For now, generate some fake data:
    const fakeReportData = {
      views: 30 + Math.floor(Math.random() * 100),           // Lượt xem
      impressions: 33 + Math.floor(Math.random() * 150),    // Lượt hiển thị
      cartAdds: 5 + Math.floor(Math.random() * 40),         // Lượt bỏ vào giỏ hàng (Example)
      contactRequests: 10 + Math.floor(Math.random() * 20), // Lượt liên hệ (Example - added based on previous comment)
      highlightsCount: 1 + Math.floor(Math.random() * 15),  // Số lần highlight
      renewalsCount: 0 + Math.floor(Math.random() * 5),     // Số lần gia hạn
      postCost: 55000,                                      // Tiền bài đăng
      highlightCost: (1 + Math.floor(Math.random() * 15)) * 40000, // Tổng tiền highlight
      renewalCost: (0 + Math.floor(Math.random() * 5)) * 300000, // Tổng tiền gia hạn
    };
    fakeReportData.totalCost = fakeReportData.postCost + fakeReportData.highlightCost + fakeReportData.renewalCost; 

    return {
      success: true,
      data: fakeReportData,
    };

  } catch (error) {
    console.error(`Error fetching report for propertyId ${propertyId}:`, error);
    return {
      success: false,
      message: "Đã xảy ra lỗi khi lấy dữ liệu báo cáo.",
      errorType: "internal_error",
    };
  }
}

// Update property media caption
export async function updatePropertyMediaCaption(mediaId, caption) {
  try {
    const payload = {
      id: mediaId,
      caption: caption
    };

    return await fetchWithAuth(`${process.env.API_URL}/Media/update-caption`, {
      method: "PUT",
      body: JSON.stringify(payload),
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("PropertyService", error, {
      action: "updatePropertyMediaCaption",
      mediaId,
      caption,
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi cập nhật chú thích hình ảnh");
  }
}

// Update property media isAvatar status
export async function updatePropertyMediaIsAvatar(mediaId, isAvatar) {
  try {
    const payload = {
      id: mediaId,
      isAvatar: isAvatar
    };

    return await fetchWithAuth(`${process.env.API_URL}/Media/update-is-avatar`, {
      method: "PUT",
      body: JSON.stringify(payload),
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("PropertyService", error, {
      action: "updatePropertyMediaIsAvatar",
      mediaId,
      isAvatar,
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi cập nhật ảnh đại diện");
  }
}

// Delete property media
export async function deletePropertyMedia(mediaId) {
  try {
    return await fetchWithAuth(`${process.env.API_URL}/Media/${mediaId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logError("PropertyService", error, {
      action: "deletePropertyMedia",
      mediaId,
    });
    return handleErrorResponse(false, null, "Đã xảy ra lỗi khi xóa hình ảnh");
  }
}

/**
 * Bulk delete multiple properties
 * @param {Array<string>} propertyIds - Array of property IDs to delete
 * @returns {Promise<Object>} Response with success/error information
 */
export async function bulkDeleteProperties(propertyIds) {
  try {
    return await fetchWithAuth(`${API_BASE_URL}/bulk`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        propertyIds: propertyIds
      }),
    });
  } catch (error) {
    console.error("Lỗi khi xóa nhiều bất động sản:", error);
    return {
      success: false,
      message: "Đã xảy ra lỗi khi xóa nhiều bất động sản",
      errorType: "network_error",
    };
  }
}

/**
 * Bulk update status for multiple properties
 * @param {Array<string>} propertyIds - Array of property IDs to update
 * @param {string} status - New status to set
 * @param {string} comment - Optional comment for the status update
 * @returns {Promise<Object>} Response with success/error information
 */
export async function bulkUpdatePropertyStatus(propertyIds, status, comment = "") {
  try {
    return await fetchWithAuth(`${API_BASE_URL}/bulk/status`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        propertyIds: propertyIds,
        status: status,
        comment: comment
      }),
    });
  } catch (error) {
    console.error("Lỗi khi cập nhật trạng thái nhiều bất động sản:", error);
    return {
      success: false,
      message: "Đã xảy ra lỗi khi cập nhật trạng thái nhiều bất động sản",
      errorType: "network_error",
    };
  }
}

/**
 * Update highlight status for a property
 * @param {string} propertyId - ID of the property to update
 * @param {boolean} isHighlighted - Whether the property should be highlighted
 * @returns {Promise<Object>} Response with success/error information
 */
export async function updatePropertyHighlight(propertyId, isHighlighted) {
  try {
    return await fetchWithAuth(`${API_BASE_URL}/${propertyId}/highlight`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        isHighlighted: isHighlighted
      }),
    });
  } catch (error) {
    console.error("Lỗi khi cập nhật trạng thái nổi bật của bất động sản:", error);
    return {
      success: false,
      message: "Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của bất động sản",
      errorType: "network_error",
    };
  }
}

/**
 * Bulk update highlight status for multiple properties
 * @param {Array<string>} propertyIds - Array of property IDs to update
 * @param {boolean} isHighlighted - Whether the properties should be highlighted
 * @returns {Promise<Object>} Response with success/error information
 */
export async function bulkUpdatePropertyHighlight(propertyIds, isHighlighted) {
  try {
    return await fetchWithAuth(`${API_BASE_URL}/bulk/highlight`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        propertyIds: propertyIds,
        isHighlighted: isHighlighted
      }),
    });
  } catch (error) {
    console.error("Lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản:", error);
    return {
      success: false,
      message: "Đã xảy ra lỗi khi cập nhật trạng thái nổi bật của nhiều bất động sản",
      errorType: "network_error",
    };
  }
}
