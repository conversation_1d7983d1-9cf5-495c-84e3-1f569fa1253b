import {Link} from '@/i18n/navigation';
import Image from "next/image";
import { getTranslations } from 'next-intl/server';
import LoginForm from "@/components/auth/LoginForm";

const LoginPage = async () => {
  const t = await getTranslations('LoginPage');

  return (
    <div className="min-h-screen flex pt-8 justify-center bg-background px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <Image
            className="mx-auto w-auto"
            src="/yezhome_logo.png"
            alt={t('logoAlt')}
            width={130}
            height={130}
            priority
          />
          <h2 className="mt-6 text-center text-3xl font-extrabold text-navy-blue">{t('title')}</h2>
        </div>

        <LoginForm />

        <div className="mt-6">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-background text-charcoal">
                {t('signUpPrompt')} <Link href="/dang-ki" className="font-semibold text-coral-500 hover:text-coral-600">{t('signUpLink')}</Link>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
