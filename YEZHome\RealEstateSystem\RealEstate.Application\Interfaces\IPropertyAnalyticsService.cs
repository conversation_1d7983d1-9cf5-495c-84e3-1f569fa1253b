using RealEstate.Application.DTO.Analytics;

namespace RealEstate.Application.Interfaces
{
    public interface IPropertyAnalyticsService
    {
        // Log events
        Task LogPropertyViewAsync(Guid propertyId, Guid? viewerId, string viewerIp, string userAgent, string referrerUrl);
        Task LogPropertySpendingAsync(Guid propertyId, Guid userId, decimal amount, string spendingType, Guid? transactionId, string details);
        
        // Get analytics
        Task<PropertyAnalyticsDto> GetPropertyAnalyticsAsync(Guid propertyId, DateTime? startDate = null, DateTime? endDate = null);
        Task<PagedResultDto<PropertyAnalyticsDto>> GetUserPropertiesAnalyticsAsync(Guid userId, PropertyAnalyticsFilterDto filter);
        
        // Export analytics
        Task<byte[]> ExportPropertyAnalyticsToExcelAsync(Guid propertyId, DateTime? startDate = null, DateTime? endDate = null);
        Task<byte[]> ExportUserPropertiesAnalyticsToExcelAsync(Guid userId, PropertyAnalyticsFilterDto filter);
        
        // Update engagement summary (can be called by a background job)
        Task UpdatePropertyEngagementSummaryAsync(Guid propertyId);
        Task UpdateAllPropertiesEngagementSummaryAsync();
    }
}
