"use client";

import { useProfile } from "@/contexts/ProfileContext";
import { useEffect, useState } from "react";

export default function ProfileContextTest() {
  const { profile, isAuthenticated, refreshUserData } = useProfile();
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [logMessages, setLogMessages] = useState([]);

  // Add log message with timestamp
  const addLog = (message) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogMessages(prev => [`${timestamp}: ${message}`, ...prev.slice(0, 9)]);
  };

  useEffect(() => {
    addLog(`ProfileContextTest mounted`);
    addLog(`Authentication status: ${isAuthenticated ? "Authenticated" : "Not Authenticated"}`);
    addLog(`Profile status: ${profile ? "Loaded" : "Not Loaded"}`);

    // Set up a timer to update the last updated time
    const timer = setInterval(() => {
      setLastUpdated(new Date());
    }, 1000);

    return () => {
      addLog("ProfileContextTest unmounted");
      clearInterval(timer);
    };
  }, []);

  // Log when authentication or profile changes
  useEffect(() => {
    addLog(`Authentication changed: ${isAuthenticated ? "Authenticated" : "Not Authenticated"}`);
  }, [isAuthenticated]);

  useEffect(() => {
    addLog(`Profile changed: ${profile ? "Loaded" : "Not Loaded"}`);
  }, [profile]);

  const handleRefresh = () => {
    addLog("Manual refresh requested");
    refreshUserData();
  };

  return (
    <div className="p-4 border rounded">
      <h2 className="text-lg font-bold mb-2">Profile Context Test</h2>
      <p className="mb-2">Last Updated: {lastUpdated.toLocaleTimeString()}</p>

      <div className="mb-4 p-3 bg-gray-100 rounded">
        <p><strong>Authentication Status:</strong> {isAuthenticated ? "✅ Authenticated" : "❌ Not Authenticated"}</p>
        <p><strong>Profile Status:</strong> {profile ? "✅ Loaded" : "❌ Not Loaded"}</p>

        {profile && (
          <div className="mt-2 p-2 bg-white rounded">
            <p><strong>Name:</strong> {profile.fullName}</p>
            <p><strong>Email:</strong> {profile.email}</p>
            <p><strong>User Type:</strong> {profile.userType}</p>
            {profile.wallet && (
              <p><strong>Wallet Balance:</strong> {profile.wallet.balance}</p>
            )}
          </div>
        )}
      </div>

      <button
        onClick={handleRefresh}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 mb-4"
      >
        Refresh Profile Data
      </button>

      <div className="mt-4">
        <h3 className="font-bold mb-2">Log Messages:</h3>
        <div className="bg-black text-green-400 p-2 rounded font-mono text-sm h-40 overflow-y-auto">
          {logMessages.map((msg, i) => (
            <div key={i}>{msg}</div>
          ))}
        </div>
      </div>
    </div>
  );
}
