{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/api/map/cities/route.jsx"], "sourcesContent": ["export async function GET() {\n  try {\n    const API_URL = process.env.API_URL;\n    \n    const response = await fetch(`${API_URL}/api/Address/cities`, {\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    if (!response.ok) {\n      throw new Error(`Failed to fetch cities: ${response.status}`);\n    }\n\n    const data = await response.json();\n    return Response.json(data);\n  } catch (error) {\n    console.error('Error fetching cities:', error);\n    return Response.json(\n      { error: 'Failed to fetch cities' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAO,eAAe;IACpB,IAAI;QACF,MAAM,UAAU,QAAQ,GAAG,CAAC,OAAO;QAEnC,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,mBAAmB,CAAC,EAAE;YAC5D,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,MAAM,EAAE;QAC9D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,SAAS,IAAI,CAAC;IACvB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,SAAS,IAAI,CAClB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}