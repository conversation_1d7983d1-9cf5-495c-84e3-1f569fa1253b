﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RealEstate.Application.Services
{
    public class UserFavoriteService : IUserFavoriteService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IPropertyAnalyticsService _propertyAnalyticsService;

        public UserFavoriteService(IUnitOfWork unitOfWork, IMapper mapper, IPropertyAnalyticsService propertyAnalyticsService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _propertyAnalyticsService = propertyAnalyticsService;
        }

        public async Task<IEnumerable<UserFavoriteDto>> GetUserFavoritesAsync(Guid userId)
        {
            var favorites = await _unitOfWork.UserFavorites.GetQueryable()
                .Where(f => f.UserID == userId)
                .ToListAsync();

            return _mapper.Map<IEnumerable<UserFavoriteDto>>(favorites);
        }

        public async Task<bool> AddToFavoritesAsync(Guid userId, Guid propertyId)
        {
            // Check if already favorited
            var existingFavorite = await _unitOfWork.UserFavorites.GetQueryable()
                .FirstOrDefaultAsync(f => f.UserID == userId && f.PropertyID == propertyId);

            if (existingFavorite != null)
            {
                // Already favorited
                return true;
            }

            // Verify property exists
            var property = await _unitOfWork.Properties.GetByIdAsync(propertyId);
            if (property == null || property.IsDeleted)
            {
                throw new KeyNotFoundException($"Property with ID {propertyId} not found");
            }

            // Verify user exists
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null)
            {
                throw new KeyNotFoundException($"User with ID {userId} not found");
            }

            // Create new favorite
            var favorite = new UserFavorite
            {
                UserID = userId,
                PropertyID = propertyId,
                CreatedAt = DateTime.UtcNow,
            };

            await _unitOfWork.UserFavorites.AddAsync(favorite);
            await _unitOfWork.SaveChangesAsync();

            // Update property engagement summary
            try
            {
                await _propertyAnalyticsService.UpdatePropertyEngagementSummaryAsync(propertyId);
            }
            catch (Exception)
            {
                // Log the error but don't fail the request
            }

            return true;
        }

        public async Task<bool> RemoveFromFavoritesAsync(Guid userId, Guid propertyId)
        {
            var favorite = await _unitOfWork.UserFavorites.GetQueryable()
                .FirstOrDefaultAsync(f => f.UserID == userId && f.PropertyID == propertyId);

            if (favorite == null)
            {
                // Not found, consider it already removed
                return true;
            }

            _unitOfWork.UserFavorites.Remove(favorite);
            await _unitOfWork.SaveChangesAsync();

            // Update property engagement summary
            try
            {
                await _propertyAnalyticsService.UpdatePropertyEngagementSummaryAsync(propertyId);
            }
            catch (Exception)
            {
                // Log the error but don't fail the request
            }

            return true;
        }

        public async Task<List<FavoriteStatusDto>> CheckFavoriteStatusAsync(Guid userId, List<Guid> propertyIds)
        {
            var userFavorites = await _unitOfWork.UserFavorites.GetQueryable()
                .Where(f => f.UserID == userId && propertyIds.Contains(f.PropertyID))
                .Select(f => f.PropertyID)
                .ToListAsync();

            var result = propertyIds.Select(id => new FavoriteStatusDto
            {
                PropertyId = id,
                IsFavorite = userFavorites.Contains(id)
            }).ToList();

            return result;
        }

        public async Task<int> GetFavoritesCountAsync(Guid userId)
        {
            return await _unitOfWork.UserFavorites.GetQueryable()
                .CountAsync(f => f.UserID == userId);
        }
    }
}
