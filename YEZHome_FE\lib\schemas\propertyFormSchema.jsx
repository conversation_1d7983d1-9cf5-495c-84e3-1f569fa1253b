import { z } from "zod";

export const propertyFormSchema = z.object({
  videoUrl: z.string(),
  postType: z.enum(["sell", "rent"], {
    errorMap: (issue, ctx) => ({ message: '<PERSON>ần chọn loại tin đăng.' })
  }),
  propertyType: z.enum(["nha_rieng", "can_ho", "nha_tro"], {
    errorMap: (issue, ctx) => ({ message: 'Cần chọn loại bất động sản.' })
  }),
  price: z
    .number({
      coerce: true,
      required_error: "<PERSON>i<PERSON> tiền bắt buộc phải điền",
      invalid_type_error: "Gi<PERSON> tiền không hợp lệ",
    })
    .positive("Giá phải là số dương"),
  cityId: z.string().min(1, "Cần chọn Tỉnh/Thành phố"),
  districtId: z.string().min(1, "<PERSON>ần chọn <PERSON>/Huyện"),
  wardId: z.string().min(1, "<PERSON>ần chọn Phường/Xã"),
  address: z.string().min(1, "Thông tin địa chỉ bắt buộc phải điền."),
  name: z
    .string({ required_error: "Vui lòng nhập tối thiểu 30 ký tự." })
    .min(30, "Vui lòng nhập tối thiểu 30 ký tự.")
    .max(99, "Vui lòng nhập tối đa 99 ký tự."),
  description: z
    .string({ required_error: "Vui lòng nhập tối thiểu 30 ký tự." })
    .min(30, "Vui lòng nhập tối thiểu 30 ký tự.")
    .max(3000, "Vui lòng nhập tối đa 3000 ký tự."),
  floors: z
    .number({ coerce: true })
    .optional(),
  rooms: z
    .number({ coerce: true })
    .optional(),
  toilets: z
    .number({ coerce: true })
    .optional(),
  direction: z.string().optional(),
  balconyDirection: z.string().optional(),
  legality: z.string().optional(),
  interior: z.string().optional(),
  width: z
    .number({ coerce: true })
    .optional(),
  roadWidth: z
    .number({ coerce: true })
    .optional(),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
  placeData: z.string().optional(),
});
