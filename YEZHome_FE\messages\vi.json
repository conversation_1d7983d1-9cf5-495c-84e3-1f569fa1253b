{"ErrorMessage": {"default": "<PERSON><PERSON> xảy ra lỗi. <PERSON><PERSON> lòng thử lại sau.", "locationErrorTitle": "Lỗi", "locationErrorDescription": "Lỗi lấy vị trí: {message}"}, "Common": {"greeting": "<PERSON><PERSON> ch<PERSON>o", "diamond": "<PERSON>", "platinum": "<PERSON><PERSON><PERSON> kim", "silver": "Bạc", "gold": "<PERSON><PERSON><PERSON>", "bronze": "Đồng", "default": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requirementDiamond": "Sử dụng từ 20.000.000đ", "requirementPlatinum": "Sử dụng từ 10.000.000đ", "requirementGold": "Sử dụng từ 5.000.000đ", "requirementSilver": "Sử dụng từ 2.000.000đ", "requirementBronze": "Sử dụng từ 500.000đ", "requirementNormal": "Sử dụng dưới 500.000đ", "propertyStatus_Approved": "Đ<PERSON>", "propertyStatus_PendingApproval": "<PERSON>ờ <PERSON>", "propertyStatus_RejectedByAdmin": "<PERSON><PERSON> từ chối", "propertyStatus_RejectedDueToUnpaid": "Bị từ chối do chưa thanh toán", "propertyStatus_WaitingPayment": "Chờ thanh toán", "propertyStatus_Expired": "<PERSON><PERSON><PERSON>", "propertyStatus_Draft": "<PERSON>", "propertyStatus_Sold": "<PERSON><PERSON> bán", "propertyPostType_sell": "Bán", "propertyPostType_rent": "<PERSON> thuê", "highlight_status": "<PERSON> n<PERSON> bật", "east": "<PERSON><PERSON><PERSON>", "west": "Tây", "south": "Nam", "north": "Bắc", "northeast": "Đông <PERSON>", "northwest": "<PERSON><PERSON><PERSON>", "southeast": "Đông Nam", "southwest": "Tây Nam", "propertyType_nha_rieng": "Nhà riêng", "propertyType_can_ho": "<PERSON><PERSON><PERSON>", "propertyType_nha_tro": "Nhà trọ", "legality_so_do": "Sổ đỏ", "legality_so_hong": "<PERSON><PERSON> hồng", "legality_hdmb": "<PERSON><PERSON><PERSON> đồng mua bán", "legality_khac": "<PERSON><PERSON><PERSON>", "interior_day_du": "<PERSON><PERSON><PERSON> đ<PERSON>", "interior_co_ban": "<PERSON><PERSON> bản", "interior_khong_noi_that": "<PERSON><PERSON><PERSON><PERSON> nội thất", "button_ok": "OK", "button_cancel": "<PERSON><PERSON><PERSON>", "button_confirm": "<PERSON><PERSON><PERSON>", "button_save": "<PERSON><PERSON><PERSON>", "button_delete": "Xóa", "button_close": "Đ<PERSON><PERSON>", "button_next": "<PERSON><PERSON><PERSON><PERSON>", "button_previous": "Trở lại"}, "NoData": {"defaultMessage": "Chưa có dữ liệu", "defaultCreateMessage": "Bạn chưa có tin đăng bài nào.", "defaultCreateButtonTitle": "Đăng tin BĐS"}, "SearchFilter": {"addressPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ, quận/huyện, tỉnh/thành phố...", "searchButton": "<PERSON><PERSON><PERSON>", "transactionTypeButtonDefault": "<PERSON><PERSON><PERSON> tin đăng", "transactionTypeButtonSell": "Bán", "transactionTypeButtonRent": "<PERSON> thuê", "popoverTitleTransactionType": "<PERSON><PERSON><PERSON> tin đăng", "transactionSellLabel": "Bán", "transactionRentLabel": "<PERSON> thuê", "propertyTypeButton": "Loại nhà đất", "popoverTitlePropertyType": "Loại nhà đất", "propertyTypeHouse": "Nhà riêng", "propertyTypeApartment": "<PERSON><PERSON><PERSON>", "propertyTypeVilla": "<PERSON><PERSON><PERSON><PERSON> thự", "priceButton": "<PERSON><PERSON><PERSON> gi<PERSON>", "popoverTitlePrice": "<PERSON><PERSON><PERSON> gi<PERSON>", "pricePlaceholderFrom": "Từ", "pricePlaceholderTo": "<PERSON><PERSON><PERSON>", "priceDisplayFrom": "Từ", "priceDisplayTo": "<PERSON><PERSON><PERSON>", "priceUnitBillion": " tỉ", "priceUnitMillion": " tri<PERSON><PERSON>", "advancedFilterButton": "<PERSON><PERSON><PERSON> n<PERSON> cao", "popoverTitleAdvanced": "<PERSON><PERSON><PERSON> n<PERSON> cao", "areaLabel": "Diện tích (m²)", "bedroomsLabel": "Phòng ngủ", "bedroomsPlaceholder": "Số phòng ngủ", "bedroomsOption1": "1+", "bedroomsOption2": "2+", "bedroomsOption3": "3+", "bedroomsOption4": "4+", "bathroomsLabel": "<PERSON>ò<PERSON> tắm", "bathroomsPlaceholder": "Số phòng tắm", "bathroomsOption1": "1+", "bathroomsOption2": "2+", "bathroomsOption3": "3+", "directionLabel": "Hướng nhà", "directionPlaceholder": "Hướng nhà", "directionEast": "<PERSON><PERSON><PERSON>", "directionWest": "Tây", "directionSouth": "Nam", "directionNorth": "Bắc", "directionSouthEast": "Đông Nam", "directionSouthWest": "Tây Nam", "directionNorthEast": "Đông <PERSON>", "directionNorthWest": "<PERSON><PERSON><PERSON>", "legalStatusLabel": "<PERSON><PERSON><PERSON>", "legalStatusPlaceholder": "<PERSON><PERSON><PERSON>", "legalStatusRedBook": "Sổ đỏ", "legalStatusPinkBook": "<PERSON><PERSON> hồng", "legalStatusHandwritten": "<PERSON><PERSON><PERSON><PERSON> vi<PERSON><PERSON> tay", "legalStatusOther": "K<PERSON><PERSON><PERSON>", "roadWidthLabel": "Đường trư<PERSON><PERSON> nhà", "roadWidthPlaceholder": "Đường trư<PERSON><PERSON> nhà", "roadWidthOption1": "Dưới 3m", "roadWidthOption2": "3-5m", "roadWidthOption3": "5-7m", "roadWidthOption4": "<PERSON><PERSON><PERSON><PERSON> 7m", "resetButton": "Đặt lại", "applyButton": "<PERSON><PERSON>", "activeFiltersLabel": "<PERSON><PERSON> lọc đang á<PERSON> dụng:", "filterLabelTransactionType": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "filterLabelPropertyType": "Loại BĐS", "filterLabelPrice": "Giá", "filterLabelArea": "<PERSON><PERSON><PERSON> t<PERSON>ch", "filterLabelBedrooms": "Phòng ngủ", "filterLabelBathrooms": "<PERSON>ò<PERSON> tắm", "filterLabelDirection": "Hướng nhà", "filterLabelLegalStatus": "<PERSON><PERSON><PERSON>", "filterLabelRoadWidth": "Đường trư<PERSON><PERSON> nhà", "filterLabelAddress": "Địa chỉ", "unlimited": "kh<PERSON>ng gi<PERSON>i hạn", "resetAllButton": "Đặt lại tất cả", "provincePlaceholder": "Chọn Tỉnh/Thành", "provincePlaceholderAll": "Tất cả Tỉnh/Thành", "loadingProvinces": "<PERSON><PERSON> tải Tỉnh/Thành...", "districtPlaceholder": "<PERSON><PERSON><PERSON>/Huyện", "districtPlaceholderAll": "<PERSON><PERSON><PERSON>/Huyện", "loadingDistricts": "<PERSON><PERSON> t<PERSON>/Huyện...", "filterLabelLocation": "<PERSON><PERSON> v<PERSON>", "addressSearchPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ"}, "HomePage": {"fetchErrorTitle": "Lỗi", "fetchErrorDescription": "<PERSON><PERSON><PERSON><PERSON> thể tải dữ liệu bất động sản."}, "NotFound": {"title": "<PERSON><PERSON><PERSON><PERSON> thể tải trang này", "description": "<PERSON>rang mà bạn truy cập không tồn tại, vui lòng thử lại.", "backButton": "Quay lại trang chủ"}, "Loading": {"message": "<PERSON><PERSON> lòng chờ một chút..."}, "LoginPage": {"title": "<PERSON><PERSON><PERSON>", "signUpPrompt": "Bạn chưa có tài khoản YEZHome?", "signUpLink": "<PERSON><PERSON>ng kí tại đây", "logoAlt": "Logo YEZHome", "loginErrorTitle": "<PERSON><PERSON><PERSON> nhập không thành công", "emailLabel": "Email", "passwordLabel": "<PERSON><PERSON><PERSON>", "emailPlaceholder": "Đ<PERSON>a chỉ email", "passwordPlaceholder": "<PERSON><PERSON><PERSON>", "rememberMeLabel": "<PERSON><PERSON> nhớ đăng nhập", "forgotPasswordLink": "<PERSON>uên mật khẩu?", "loginButton": "<PERSON><PERSON><PERSON>"}, "RegisterPage": {"title": "<PERSON><PERSON><PERSON> kí tài k<PERSON>n", "loginPrompt": "Bạn đã có tài k<PERSON>n?", "loginLink": "<PERSON><PERSON><PERSON>", "registerErrorTitle": "<PERSON><PERSON><PERSON> kí không thành công", "fullNameLabel": "<PERSON><PERSON> tên", "emailLabel": "Đ<PERSON>a chỉ Email", "passwordLabel": "<PERSON><PERSON><PERSON>", "confirmPasswordLabel": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u", "phoneLabel": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "usernamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> họ tên", "emailPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ email", "passwordPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u (ít nhất 6 ký tự)", "confirmPasswordPlaceholder": "<PERSON><PERSON><PERSON><PERSON> lại mật kh<PERSON>u", "phonePlaceholder": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "userTypeQuestion": "Bạn chủ yếu sử dụng YEZHome để làm gì?", "userTypeBuyer": "<PERSON><PERSON>", "userTypeSeller": "Bán", "registerButton": "Đăng kí"}, "ForgotPasswordPage": {"title": "<PERSON><PERSON><PERSON><PERSON> mật k<PERSON>u", "description": "Nhập email của bạn để nhận hướng dẫn đặt lại mật khẩu", "errorTitle": "Đ<PERSON> xảy ra lỗi", "emailLabel": "Email", "emailPlaceholder": "<EMAIL>", "submitButton": "<PERSON><PERSON><PERSON> thông tin", "backToLoginLink": "Quay lại đăng nh<PERSON>p"}, "PasswordEmailSentPage": {"title": "<PERSON><PERSON><PERSON>", "message": "<PERSON>úng tôi đã gửi hướng dẫn khôi phục mật khẩu đến địa chỉ email của bạn. Vui lòng kiểm tra hộp thư và làm theo liên kết được cung cấp.", "backToLoginLink": "Quay Lại T<PERSON>"}, "UserProfilePage": {"loadingMessage": "<PERSON><PERSON> tả<PERSON> hồ sơ...", "userNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy người dùng. <PERSON><PERSON> lòng đăng nhập.", "mainTitle": "<PERSON><PERSON><PERSON>n của tôi", "contactInfoTitle": "Thông tin liên lạc", "fullNameLabel": "<PERSON><PERSON> tên", "emailLabel": "Đ<PERSON>a chỉ Email", "phoneLabel": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "userTypeLabel": "<PERSON><PERSON><PERSON> người dùng", "passwordSuccessTitle": "<PERSON><PERSON><PERSON> mật kh<PERSON>u thành công", "passwordSuccessMessage": "<PERSON><PERSON><PERSON> kh<PERSON>u của bạn đã đư<PERSON><PERSON> thay đổi thành công. <PERSON>ui lòng đăng nhập lại.", "passwordSectionTitle": "<PERSON><PERSON><PERSON> mật kh<PERSON>u", "passwordSectionDescription": "Thay đổi mật khẩu để bảo vệ tài khoản của bạn", "passwordErrorTitle": "<PERSON><PERSON><PERSON> mật khẩu không thành công", "oldPasswordLabel": "<PERSON><PERSON><PERSON><PERSON> hiện tại", "newPasswordLabel": "<PERSON><PERSON><PERSON> mới", "confirmNewPasswordLabel": "<PERSON><PERSON><PERSON> nh<PERSON>n mật khẩu mới", "saveButton": "<PERSON><PERSON><PERSON> thay đổi", "editButton": "Chỉnh sửa", "cancelButton": "<PERSON><PERSON><PERSON>", "taxInfoTitle": "<PERSON>h<PERSON><PERSON> tin thuế và hóa đơn", "taxInfoLoadingError": "Lỗi khi tải thông tin thuế", "taxInfoUpdateSuccess": "<PERSON><PERSON><PERSON> nhật thông tin thuế thành công", "taxInfoUpdateError": "Lỗi khi cập nhật thông tin thuế", "personalTaxCodeLabel": "Mã số thuế cá nhân", "personalTaxCodePlaceholder": "Ví dụ: 1234567890 hoặc 1234567890-123", "personalTaxCodeHelperText": "MST gồm 10 hoặc 13 chữ số", "invoiceInfoTitle": "Thông tin xuất hóa đơn", "buyerNameLabel": "<PERSON><PERSON><PERSON> mua", "buyerNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên ng<PERSON> mua", "invoiceEmailLabel": "<PERSON><PERSON>n hóa đơn", "invoiceEmailPlaceholder": "<PERSON>hập email nhận hóa đơn", "companyNameLabel": "<PERSON><PERSON>n công ty", "companyNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên công ty", "taxCodeLabel": "<PERSON><PERSON> số thuế", "taxCodePlaceholder": "<PERSON><PERSON><PERSON><PERSON> mã số thuế", "addressLabel": "Địa chỉ", "addressPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "notProvided": "<PERSON><PERSON><PERSON> cung cấp", "deactivateAccountTitle": "<PERSON><PERSON><PERSON> c<PERSON>u kh<PERSON>a tài k<PERSON>n", "deactivateAccountButton": "<PERSON><PERSON><PERSON><PERSON> tà<PERSON>n", "deactivateAccountDescription": "<PERSON><PERSON> lòng nhập mật khẩu để xác nhận khóa tài kho<PERSON>n", "warningTitle": "<PERSON><PERSON><PERSON>", "deactivateWarning1": "<PERSON><PERSON><PERSON> khách sẽ không thể đăng nhập lại vào tài khoản này sau khi khóa.", "deactivateWarning2": "<PERSON><PERSON><PERSON> tin đăng đang hiển thị của quý khách sẽ tiếp tục được hiển thị tới hết thời gian đăng tin đã chọn.", "deactivateWarning3": "<PERSON><PERSON> dư tiền (nếu có) trong các tài khoản của quý khách sẽ không được hoàn lại.", "deactivateWarning4": "<PERSON><PERSON><PERSON> kho<PERSON>n dịch vụ của quý khách chỉ có thể được khóa khi không còn số dư nợ.", "deactivateWarning5": "Số điện thoại chính đăng ký tài khoản này và các số điện thoại đăng tin của quý khách sẽ không thể được sử dụng lại để đăng ký tài khoản mới.", "deleteAccountTitle": "<PERSON><PERSON><PERSON> c<PERSON>u x<PERSON>a tài <PERSON>n", "deleteAccountButton": "<PERSON><PERSON><PERSON> t<PERSON>", "deleteAccountDescription": "<PERSON><PERSON> lòng nhập mật khẩu để xác nhận yêu cầu xóa tài kho<PERSON>n", "deleteAccountWarning": "<PERSON><PERSON><PERSON> yêu cầu xoá toàn bộ thông tin của tài khoản. <PERSON><PERSON> khi được xử lý, toàn bộ thông tin sẽ được xoá và không thể hoàn tác.", "passwordLabel": "<PERSON><PERSON><PERSON>", "passwordPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u của bạn", "reasonLabel": "Lý do", "reasonPlaceholder": "<PERSON><PERSON> lòng cung cấp lý do (không bắt buộc)", "confirmDeactivateButton": "<PERSON><PERSON><PERSON> kh<PERSON>a tài k<PERSON>n", "confirmDeleteButton": "<PERSON><PERSON><PERSON>n x<PERSON>a tài <PERSON>n", "passwordRequired": "<PERSON><PERSON><PERSON> kh<PERSON>u là bắt buộc", "accountDeactivateSuccess": "<PERSON><PERSON><PERSON> cầu khóa tài khoản đã đư<PERSON><PERSON> gửi thành công", "accountDeactivateError": "Lỗi khi gửi yêu cầu khóa tài kho<PERSON>n", "accountDeleteRequestSuccess": "<PERSON><PERSON><PERSON> cầu xóa tài khoản đã đư<PERSON><PERSON> gửi thành công", "accountDeleteRequestError": "Lỗi khi gửi yêu cầu xóa tài kho<PERSON>n", "avatarTitle": "Ảnh đại diện", "uploadAvatarButton": "<PERSON><PERSON><PERSON>", "removeAvatarButton": "<PERSON><PERSON><PERSON>", "avatarRequirements": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> tối đa: 200KB. Định dạng: JPG, PNG", "avatarSizeError": "<PERSON><PERSON><PERSON> thư<PERSON><PERSON>nh không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 200KB", "avatarUploadSuccess": "<PERSON><PERSON><PERSON> lên ảnh đại diện thành công", "avatarUploadError": "<PERSON><PERSON> xảy ra lỗi khi tải lên ảnh đại diện", "avatarDeleteSuccess": "<PERSON><PERSON><PERSON>nh đại diện thành công", "avatarDeleteError": "<PERSON><PERSON> xảy ra lỗi khi xóa ảnh đại diện", "personalTab": "Thông tin cá nhân", "accountSettingsTab": "Cài đặt tài k<PERSON>n"}, "UserDashboardPage": {"loadingMessage": "<PERSON><PERSON> tải dữ liệu...", "fetchError": "<PERSON><PERSON><PERSON><PERSON> thể tải dữ liệu", "genericFetchError": "<PERSON><PERSON> xảy ra lỗi khi tải dữ liệu", "noData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "greeting": "<PERSON><PERSON> ch<PERSON>, {fullName}", "lastLoginLabel": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> lần <PERSON>:", "postListingButton": "Đăng tin mới", "depositButton": "<PERSON><PERSON><PERSON> t<PERSON>", "walletCardTitle": "Thông tin ví", "propertyStatsCardTitle": "<PERSON><PERSON><PERSON><PERSON> kê bất động sản", "recentTransactionsTitle": "<PERSON><PERSON><PERSON><PERSON> gần đ<PERSON>y", "memberRankingTitle": "<PERSON><PERSON><PERSON> thành viên"}, "UserPropertiesPage": {"title": "<PERSON><PERSON><PERSON><PERSON> lý BDS", "description": "<PERSON><PERSON><PERSON><PERSON> lý danh sách bất động sản của bạn", "createButton": "Đăng BĐS", "viewDetailsButton": "<PERSON>em chi tiết", "moreActionsButton": "<PERSON><PERSON><PERSON><PERSON> hành động", "verifyLoading": "<PERSON><PERSON> kiểm tra...", "verifyDisabledTooltip": "<PERSON><PERSON><PERSON><PERSON> thể yêu cầu kiểm du<PERSON>", "verifyButton": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> k<PERSON> du<PERSON> lại", "contactsButton": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> liên lạc", "historyButton": "<PERSON><PERSON><PERSON> sử hoạt động", "deleteLoading": "Đang xóa...", "deleteButton": "Xóa", "deleteConfirmTitle": "<PERSON><PERSON><PERSON>n x<PERSON>a", "deleteConfirmMessage": "Bạn có chắc chắn muốn xóa bất động sản này?", "deleteSuccessToast": "<PERSON><PERSON><PERSON> bất động sản thành công", "deleteErrorToast": "<PERSON><PERSON> xảy ra lỗi khi xóa bất động sản", "verifyConfirmTitle": "<PERSON><PERSON><PERSON>n yêu cầu kiể<PERSON>", "verifyConfirmMessage": "Bạn đang yêu cầu kiểm duyệt lại thông tin bài đăng. Bạn còn lại {remainingTimes} lần kiểm duyệt cho bài đăng này. Bạn có chắc chắn muốn gửi cho bộ phận duyệt bài không?", "verifySuccessToast": "<PERSON><PERSON> g<PERSON>i yêu cầu kiểm duyệt thành công.", "verifyErrorToast": "<PERSON><PERSON><PERSON><PERSON> thể gửi yêu cầu kiểm du<PERSON>.", "verifyGenericErrorToast": "<PERSON><PERSON> xảy ra lỗi khi gửi yêu cầu kiểm du<PERSON>.", "verifyCheckErrorToast": "<PERSON><PERSON><PERSON><PERSON> thể kiểm tra số lần kiểm duyệt còn lại.", "verifyCheckGenericErrorToast": "<PERSON><PERSON> xảy ra lỗi khi kiểm tra số lần kiểm duyệt."}, "PropertyForm": {"mediaSection": "Ảnh và video", "basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "selectPropertyType": "<PERSON><PERSON><PERSON> lo<PERSON>i bất động sản", "addressSection": "Địa chỉ", "requiredInfo": "<PERSON><PERSON><PERSON>ng tin bắt buộc", "fetchPlaceError": "Lỗi lấy dữ liệu địa chỉ:", "fetchCitiesError": "Lỗi lấy danh sách tỉnh/thành phố", "fetchDistrictsError": "Lỗi lấy danh sách quận/huyện", "fetchWardsError": "Lỗi lấy danh sách phường/xã", "fetchAddressSuggestionsError": "Lỗi tìm kiếm địa chỉ:", "addressDescription": "<PERSON>h<PERSON><PERSON> địa chỉ hoặc vị trí để tìm kiếm, nếu không tìm thấy bạn có thể tìm địa chỉ gần đó và sử dụng chức năng kéo thả checkpoint để xác định vị trí chính xác.", "addressDescription2": "<PERSON><PERSON><PERSON> là địa chỉ dùng để hiển thị lên bài viết của bạn", "locationConflictTitle": "<PERSON><PERSON><PERSON> hi<PERSON>n xung đột khu vực", "locationConflictDescription": "Địa chỉ bạn vừa chọn nằm ở {newLocation}, khác với khu vực {currentLocation} bạn đã chọn trước đó.\n\nBạn có muốn tự động chuyển khu vực sang {newLocation} không?", "switchToNewLocation": "<PERSON><PERSON><PERSON><PERSON> sang {location}", "keepCurrentLocation": "<PERSON><PERSON><PERSON> nguyên khu vực {location}", "mapDescription": "<PERSON> chuyển ghim đến vị trí ch<PERSON>h xác của bất động sản.", "propertyIsBeingReviewedOrApprovedOrSold": "Bất động sản đang trong quá trình xét duyệt hoặc đã được duyệt hoặc đã bán, không thể chỉnh sửa thông tin.", "backToPropertyList": "Quay lại trang danh sách BĐS của tôi", "noChanges": "B<PERSON>n chưa thay đổi thông tin nào.", "saveSuccess": "<PERSON><PERSON><PERSON> thành công.", "saveFailed": "<PERSON><PERSON><PERSON> thất b<PERSON>.", "postInformation": "Thông tin đăng bài", "addressSelected": "Địa chỉ hiển thị trên bài viết", "addressSelectedPlaceholder": "VD: đ<PERSON><PERSON> chỉ hiển thị trên bài viết", "price": "<PERSON><PERSON><PERSON> bán/cho thuê", "pricePlaceholder": "<PERSON><PERSON><PERSON><PERSON> giá", "updatePrice": "<PERSON><PERSON><PERSON> n<PERSON> giá", "continue": "<PERSON><PERSON><PERSON><PERSON>", "announcementChange": "<PERSON><PERSON><PERSON><PERSON> báo thay đổi hạng thành viên", "congratulations": "🎉 Chúc mừng! Bạn đã được nâng hạng", "cancel": "<PERSON><PERSON><PERSON>", "map": "<PERSON><PERSON> trí trên bản đồ", "rankUpgradeMessage": "Chúc mừng! Bạn đã được nâng hạng lên {currentRank}, giá bài đăng đã giảm từ {previousPrice} xuống {currentPrice}! Tiếp tục với giá mới không?", "rankChangeMessage": "Hạng thành viên của bạn đã thay đổi, giá bài đăng hiện tại là {currentPrice} thay vì {previousPrice}. Bạn có muốn tiếp tục không?", "processing": "<PERSON><PERSON> lý", "saveDraft": "<PERSON><PERSON><PERSON>", "saveAndSendForApproval": "<PERSON><PERSON><PERSON> và g<PERSON>", "propertyUpdated": "<PERSON><PERSON>t động sản đã đư<PERSON><PERSON> cập nh<PERSON>t.", "propertyNotUpdated": "<PERSON><PERSON><PERSON> động sản chưa đ<PERSON><PERSON><PERSON> cập nh<PERSON>t.", "createPostFailed": "<PERSON><PERSON><PERSON> bài đăng thất bại", "postCannotBeEdited": "<PERSON><PERSON><PERSON> đăng không thể chỉnh sửa", "propertyCannotBeEdited": "Bất động sản này đang được duyệt hoặc đã duyệt/bán, không thể chỉnh sửa.", "propertyVideoLink": "Link video gi<PERSON>i thiệu", "propertyVideoLinkPlaceholder": "Nhập link Youtube, Tiktok, ...", "postType": "<PERSON><PERSON><PERSON> tin đăng", "sell": "Bán", "rent": "<PERSON> thuê", "propertyType": "<PERSON><PERSON><PERSON> bất động sản", "house": "Nhà riêng", "apartment": "<PERSON><PERSON><PERSON>", "motel": "Nhà trọ", "city": "Tỉnh/Thành phố", "cityPlaceholder": "Chọn tỉnh/thành phố", "district": "Quận/Huyện", "districtPlaceholder": "<PERSON><PERSON><PERSON> quận/huyện", "ward": "Phường/Xã", "wardPlaceholder": "Chọn phường/xã", "address": "<PERSON><PERSON><PERSON><PERSON> vị trí (tên <PERSON><PERSON>, dự án) để tìm vị trí đánh dấu trên bản đồ", "loadingRankChangeInfo": "<PERSON><PERSON> tải thông tin thay đổi hạng...", "addressPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ, tên đ<PERSON>, dự án...", "submissionErrorTitle": "Lỗi g<PERSON>i bài", "submissionErrorSizeLimit": "<PERSON><PERSON><PERSON><PERSON> thể gửi bài đăng. <PERSON><PERSON> lòng kiểm tra lại kích thước tệp hoặc thử lại sau.", "refreshRank": "<PERSON><PERSON><PERSON>", "refreshing": "...loading", "rankUpdated": "<PERSON><PERSON><PERSON> thành viên đã cập nhật", "rankRefreshed": "Thông tin hạng thành viên của bạn đã đư<PERSON><PERSON> làm mới", "error": "Lỗi", "errorRefreshingRank": "Lỗi khi làm mới thông tin hạng", "enterAddressManually": "<PERSON><PERSON><PERSON><PERSON> địa chỉ thủ công", "noteEnterAddress": "Địa chỉ mà bạn chọn phải khớp với thông tin tỉnh thành ở trên", "manualInputAddressToggle": "<PERSON><PERSON><PERSON><PERSON> địa chỉ thủ công"}, "PropertyImageUploader": {"avatar": "Ảnh đại diện", "setAsAvatar": "Đặt làm ảnh đại diện", "deleteImage": "<PERSON><PERSON><PERSON>", "error": "Lỗi", "setAvatarSuccess": "Đặt ảnh đại diện thành công", "deleteImageSuccess": "<PERSON><PERSON><PERSON>nh thành công", "dropHere": "Kéo & thả hình <PERSON>nh vào đây", "uploadPropertyImage": "<PERSON><PERSON><PERSON> bất động sản", "dragAndDrop": "<PERSON><PERSON>o thả hoặc nhấn để chọn ảnh", "supportedFormats": "Định dạng hỗ trợ: JPEG, PNG, JPG, WEBP", "uploadingImage": "<PERSON><PERSON> tải <PERSON>nh lên...", "uploadedImages": "Ảnh đã tải lên", "image": "<PERSON><PERSON>", "images": "<PERSON><PERSON>", "edit": "Chỉnh sửa", "manageImages": "<PERSON><PERSON><PERSON><PERSON>", "maxFileSize": "<PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> tệp tối đa: 1MB", "addCaptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả cho <PERSON>nh"}, "PropertyPostInformation": {"postInformation": "Thông tin bài đăng", "requiredInformation": "<PERSON><PERSON><PERSON>ng tin bắt buộc", "title": "Ti<PERSON>u đề bài đăng", "titlePlaceholder": "VD: <PERSON><PERSON> nh<PERSON> mặt tiền Quận 1, g<PERSON><PERSON> tốt", "titleDescription": "<PERSON><PERSON><PERSON> thiểu 30, tối đa 99 ký tự", "introduction": "<PERSON><PERSON> tả chi tiết", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả chi tiết về bất động sản...", "descriptionDescription": "<PERSON><PERSON><PERSON> thiểu 30, tối đa 3000 ký tự"}, "AdditionalInformation": {"detailedInformation": "Th<PERSON>ng tin chi tiết", "optional": "Thông tin không bắt buộc", "area": "<PERSON><PERSON><PERSON> t<PERSON>ch", "floors": "<PERSON><PERSON> tầng", "rooms": "Số phòng ngủ", "toilets": "Số phòng vệ sinh", "direction": "Hướng nhà", "selectDirection": "<PERSON><PERSON><PERSON> h<PERSON> nhà", "north": "Bắc", "south": "Nam", "east": "<PERSON><PERSON><PERSON>", "west": "Tây", "northeast": "Đông <PERSON>", "northwest": "<PERSON><PERSON><PERSON>", "southeast": "Đông Nam", "southwest": "Tây Nam", "balconyDirection": "Hướng ban công", "selectBalconyDirection": "<PERSON><PERSON><PERSON> h<PERSON> ban công", "width": "<PERSON><PERSON><PERSON> r<PERSON>", "roadWidth": "Đường vào", "legality": "<PERSON><PERSON><PERSON><PERSON> tờ pháp lý", "selectLegality": "<PERSON><PERSON><PERSON> tình trạng pháp lý", "redBook": "Sổ đỏ/ Sổ hồng", "pinkBook": "<PERSON><PERSON> hồng", "purchaseContract": "<PERSON><PERSON><PERSON> đồng mua bán", "other": "K<PERSON><PERSON><PERSON>", "interior": "<PERSON><PERSON><PERSON> trạng nội thất", "selectInterior": "<PERSON><PERSON><PERSON> tình trạng nội thất", "full": "<PERSON><PERSON><PERSON> đ<PERSON>", "basic": "<PERSON><PERSON> bản", "noInterior": "<PERSON><PERSON><PERSON><PERSON> nội thất"}, "CreatePropertyDetailInformation": {"loading": "<PERSON><PERSON> tả<PERSON>...", "customerType": "<PERSON><PERSON><PERSON> k<PERSON> h<PERSON>ng", "individualCustomer": "<PERSON><PERSON><PERSON><PERSON> hàng cá nhân", "memberRank": "<PERSON><PERSON><PERSON> hạng thành viên", "highlightPost": "<PERSON><PERSON><PERSON> bật bài đăng", "highlightPostDescription": "<PERSON><PERSON><PERSON> viết sẽ được nổi bật hơn trong danh sách", "autoRenew": "<PERSON><PERSON> động gia hạn", "autoRenewDescription": "<PERSON><PERSON><PERSON> viết sẽ được tự động gia hạn sau khi hết hạn", "postStatus": "<PERSON>r<PERSON><PERSON> thái bài đăng", "expirationTime": "<PERSON><PERSON><PERSON><PERSON> gian hiển thị", "days": "ng<PERSON>y", "postDate": "<PERSON><PERSON><PERSON>", "expirationDate": "<PERSON><PERSON><PERSON> h<PERSON> hạn", "postFee": "<PERSON><PERSON> đ<PERSON>ng bài", "highlightFee": "<PERSON><PERSON> n<PERSON>i bật", "total": "<PERSON><PERSON><PERSON> cộng"}, "CreatePropertySuccessPage": {"pageTitle": "<PERSON><PERSON><PERSON> qu<PERSON> giao d<PERSON>ch", "successMessage": "<PERSON> đăng đã đư<PERSON><PERSON> ghi <PERSON>ận", "baseCostLabel": "0 đ", "moderationNotice": "Tin của bạn sẽ được kiểm duyệt trong vòng 8h làm việc.", "statusLabel": "<PERSON><PERSON><PERSON><PERSON> thái", "statusPending": "<PERSON>ờ <PERSON>", "listingIdLabel": "Mã tin", "displayTimeLabel": "<PERSON><PERSON><PERSON><PERSON> gian hiển thị", "highlightTitle": "Dùng kèm tính năng higlight checkpoint để nổi bật hơn và tăng khả năng tiếp cận khách hàng tiềm năng", "highlightPackageTitle": "Gói highlight checkpoint", "highlightDuration": "Highlight checkpoint có thời hạn tương đương bài đăng", "highlightOption3": "3 lần <PERSON>y", "highlightOption6": "6 lần <PERSON>y", "autoRenewTitle": "Tự động đăng lại", "autoRenewDescription": "Tin sẽ được đăng lại ngay khi tin vừa hết hạn. Mỗi lần đăng lại, hệ thống chỉ trừ điểm của lần đăng lại đó.", "postAnotherButton": "Đ<PERSON>ng tiếp BĐS", "manageListingsButton": "<PERSON><PERSON><PERSON><PERSON> lý tin đăng"}, "UserTransactionsPage": {"statusCompleted": "<PERSON><PERSON><PERSON> th<PERSON>", "statusPending": "<PERSON><PERSON> lý", "statusFailed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "statusCancelled": "<PERSON><PERSON> hủy", "loadingMessage": "<PERSON><PERSON> tải dữ liệu...", "fetchError": "<PERSON><PERSON><PERSON><PERSON> thể tải lịch sử giao dịch", "genericFetchError": "<PERSON><PERSON> xảy ra lỗi khi tải lịch sử giao dịch", "pageTitle": "<PERSON><PERSON><PERSON> sử giao dịch", "detailPageTitle": "<PERSON> tiết giao dịch", "backButton": "Cancel", "transactionFetchError": "<PERSON><PERSON><PERSON>ng thể tải thông tin giao dịch", "transactionGenericFetchError": "<PERSON><PERSON> xảy ra lỗi khi tải thông tin giao dịch", "transactionNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy giao dịch", "backToListButton": "<PERSON>uay lại danh sách giao d<PERSON>ch", "cardTitleDeposit": "<PERSON>ạp tiền vào ví", "cardTitlePayment": "<PERSON><PERSON> toán", "transactionIdLabel": "Mã giao dịch:", "orderIdLabel": "<PERSON><PERSON> đơn hàng:", "transactionTypeLabel": "Loại giao d<PERSON>ch:", "paymentMethodLabel": "<PERSON><PERSON><PERSON><PERSON> thức:", "paymentMethodBank": "Chuy<PERSON>n kho<PERSON>n ngân hàng", "paymentMethodMomo": "<PERSON><PERSON>", "paymentMethodCard": "Thẻ tín dụng/ghi nợ", "creationDateLabel": "<PERSON><PERSON><PERSON> t<PERSON>:", "completionDateLabel": "<PERSON><PERSON><PERSON> ho<PERSON>n thành:", "descriptionLabel": "Nội dung:", "relatedListingLink": "<PERSON><PERSON> bài đăng liên quan", "verifyButton": "<PERSON><PERSON><PERSON> nh<PERSON>n đã thanh toán", "verifyingButton": "<PERSON><PERSON> x<PERSON>c minh...", "verifySuccessTitle": "<PERSON><PERSON><PERSON><PERSON> công", "verifySuccessMessage": "<PERSON><PERSON> g<PERSON>i yêu cầu xác minh thanh toán", "verifyErrorTitle": "Lỗi", "verifyErrorMessage": "<PERSON><PERSON><PERSON><PERSON> thể xác minh giao dịch", "verifyGenericErrorMessage": "<PERSON><PERSON> xảy ra lỗi khi xác minh giao dịch", "totalDepositLabel": "<PERSON><PERSON><PERSON> n<PERSON>p", "totalSpendingLabel": "T<PERSON>ng chi tiêu", "currentBalanceLabel": "Số dư hiện tại", "filterAll": "<PERSON><PERSON><PERSON> c<PERSON>", "filterDeposit": "<PERSON><PERSON><PERSON> t<PERSON>", "filterSpending": "<PERSON> tiêu", "timeframePlaceholder": "<PERSON><PERSON><PERSON><PERSON> gian", "timeframeAll": "<PERSON><PERSON><PERSON> cả thời gian", "timeframeMonth": "<PERSON><PERSON><PERSON><PERSON>", "timeframeYear": "<PERSON><PERSON><PERSON> nay", "exportButton": "Xuất PDF", "transactionTypeDeposit": "<PERSON><PERSON><PERSON> t<PERSON>", "transactionTypePayment": "<PERSON><PERSON> toán", "transactionTypeHighlight": "Gói highlight", "transactionTypePost": "<PERSON><PERSON> đ<PERSON>ng tin", "viewDetailsLink": "<PERSON>em chi tiết", "noTransactions": "<PERSON><PERSON><PERSON><PERSON> có giao dịch nào."}, "UserWalletPage": {"statusCompleted": "<PERSON><PERSON><PERSON><PERSON> công", "statusPending": "<PERSON><PERSON> lý", "statusFailed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "statusCancelled": "<PERSON><PERSON><PERSON>", "fetchWalletError": "<PERSON><PERSON><PERSON>ng thể tải thông tin ví", "genericFetchError": "Đã xảy ra lỗi khi tải thông tin ví", "copySuccessToastTitle": "Đã sao chép!", "copySuccessToastDesc": "Thông tin đã được sao chép vào clipboard.", "invalidAmountErrorTitle": "<PERSON><PERSON> tiền không hợp lệ", "invalidAmountErrorDesc": "<PERSON><PERSON> tiền tối thiểu cho mỗi lần nạp là 50,000₫", "topUpErrorTitle": "Lỗi", "topUpCreateErrorDesc": "<PERSON><PERSON><PERSON><PERSON> thể tạo giao dịch nạp tiền", "topUpProcessErrorDesc": "<PERSON><PERSON> xảy ra lỗi khi xử lý giao dịch nạp tiền", "checkPaymentErrorTitle": "Lỗi kiểm tra thanh toán", "paymentSuccessTitle": "<PERSON><PERSON><PERSON> tiền thành công!", "paymentSuccessDesc": "<PERSON><PERSON> dư của bạn đã đư<PERSON><PERSON> cập nhật {amount}", "checkPaymentErrorDesc": "<PERSON><PERSON><PERSON><PERSON> thể kiểm tra trạng thái thanh toán", "checkPaymentGenericErrorDesc": "<PERSON><PERSON> xảy ra lỗi khi kiểm tra trạng thái thanh toán", "pageTitle": "<PERSON><PERSON>ản lý ví", "walletInfoTitle": "Thông tin ví", "currentBalanceLabel": "Số dư hiện tại", "memberRankLabel": "<PERSON><PERSON><PERSON> thành viên", "depositSectionTitle": "<PERSON>ạp tiền vào ví", "depositStep1": "<PERSON><PERSON> tiền", "depositStep2": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "depositStep3": "Hóa <PERSON> & <PERSON>ạp tiền", "depositAmount": "Số tiền nạp: ", "depositPaymentMethod": "<PERSON><PERSON><PERSON><PERSON> thức thanh to<PERSON>: ", "depositInvoice": "<PERSON><PERSON><PERSON> c<PERSON>u xuất hóa đơn: ", "transactionHistoryLabel": "<PERSON><PERSON><PERSON> sử giao dịch", "amountLabel": "<PERSON><PERSON><PERSON><PERSON> số tiền muốn nạp", "customAmountPlaceholder": "<PERSON><PERSON><PERSON><PERSON> số tiề<PERSON> (<PERSON><PERSON><PERSON> thiểu 50,000đ)", "paymentMethodLabel": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "methodBank": "Chuy<PERSON>n kho<PERSON>n ngân hàng", "methodMomo": "<PERSON><PERSON>", "methodCard": "Thẻ tín dụng/ghi nợ", "depositButton": "<PERSON><PERSON><PERSON> t<PERSON>", "processingButton": "<PERSON><PERSON> xử lý...", "bankTransferTitle": "Thông tin chuyển khoản", "bankTransferInstruction": "<PERSON><PERSON> lòng chuyển khoản ch<PERSON>h xác theo thông tin dưới đây:", "bankLabel": "<PERSON><PERSON> h<PERSON>ng:", "accountNumberLabel": "Số tài k<PERSON>:", "accountNameLabel": "Chủ tài k<PERSON>n:", "branchLabel": "Chi nhánh:", "transferContentLabel": "<PERSON><PERSON><PERSON> dung chuyển k<PERSON>n:", "copyButton": "Sao chép", "importantNoteTitle": "<PERSON><PERSON><PERSON> ý quan trọng:", "note1": "<PERSON><PERSON><PERSON><PERSON> kho<PERSON>n đúng số tiền cần nạp.", "note2": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>h xác nội dung chuyển khoản được cung cấp. Thự<PERSON> hiện chuyển tiền, sau đó bấm nút xác nhận đã chuyển khoản.", "note3": "<PERSON><PERSON><PERSON><PERSON> có thể mất 5-15 phút để cập nhật vào ví sau khi chuyển khoản thành công.", "note4": "<PERSON><PERSON><PERSON> hệ hỗ trợ nếu sau 30 phút tiền chưa được cộng vào ví.", "confirmTransferButton": "<PERSON><PERSON><PERSON> nhận đã chuyển k<PERSON>n", "dialogTitleSuccess": "<PERSON><PERSON><PERSON> d<PERSON>ch thành công", "dialogTitlePending": "<PERSON><PERSON><PERSON><PERSON> đang chờ", "dialogTitleCreated": "<PERSON><PERSON><PERSON> giao dịch thành công", "dialogTitleFailed": "<PERSON><PERSON><PERSON> d<PERSON>ch thất b<PERSON>i", "dialogMessageSuccess": "Bạn đã nạp thành công {amount} vào ví.", "dialogMessagePendingBank": "<PERSON><PERSON><PERSON> dịch đã đư<PERSON><PERSON> tạo. <PERSON><PERSON> lòng chuyển khoản theo thông tin.", "dialogMessagePendingOther": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> đang đ<PERSON> xử lý.", "dialogMessageFailed": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> không thành công. <PERSON><PERSON> lòng thử lại.", "dialogTransactionIdLabel": "Mã giao dịch:", "dialogAmountLabel": "Số tiền:", "dialogDateLabel": "<PERSON><PERSON><PERSON> giao d<PERSON>:", "dialogPaymentMethodLabel": "<PERSON><PERSON><PERSON><PERSON> thức giao dịch:", "dialogStatusLabel": "Trạng thái:", "dialogCloseButton": "Đ<PERSON><PERSON>", "dialogViewHistoryButton": "<PERSON><PERSON> l<PERSON>ch sử giao dịch", "qrInstruction": "Quét mã QR bằng ứng dụng {methodName}", "qrExpiryLabel": "Mã QR hết hạn sau:", "resultLoadingTitle": "<PERSON><PERSON> x<PERSON> lý thanh toán", "resultLoadingMessage": "<PERSON>ui lòng đợi trong khi chúng tôi xác minh giao dịch của bạn...", "resultSuccessTitle": "<PERSON>h toán thành công!", "resultSuccessMessage": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> của bạn đã được xử lý thành công.", "resultPendingTitle": "<PERSON><PERSON><PERSON> d<PERSON>ch đang xử lý", "resultPendingMessage": "<PERSON><PERSON><PERSON> dịch của bạn đang được xử lý. <PERSON><PERSON><PERSON> tôi sẽ cập nhật số dư của bạn ngay khi nhận được xác nhận từ cổng thanh toán.", "resultFailedTitle": "<PERSON><PERSON> to<PERSON> thất bại", "resultFailedMessage": "<PERSON><PERSON><PERSON> t<PERSON>, giao dịch của bạn không thể hoàn tất. <PERSON><PERSON> lòng thử lại hoặc chọn phương thức thanh to<PERSON> kh<PERSON>.", "resultTransactionIdLabel": "Mã giao dịch:", "resultAmountLabel": "Số tiền:", "resultTimeLabel": "<PERSON>h<PERSON><PERSON> gian:", "resultActionSuccess": "Quay lại ví", "resultActionRetry": "<PERSON><PERSON><PERSON> lại", "resultActionHistory": "<PERSON><PERSON> l<PERSON>ch sử giao dịch", "loadingMessage": "<PERSON><PERSON> tải dữ liệu...", "customAmountLabel": "<PERSON><PERSON><PERSON><PERSON> số tiền kh<PERSON>c", "noTransactionsMessage": "<PERSON><PERSON><PERSON><PERSON> tìm thấy giao dịch nào", "viewAllTransactionsButton": "<PERSON><PERSON> tất cả giao d<PERSON>ch", "cancelButton": "<PERSON><PERSON><PERSON>", "postingFeeLabel": "<PERSON><PERSON> đ<PERSON>ng bài", "postingFeeValue": "55,000₫/10 ngày", "highlightFeeLabel": "Phí highlight", "viewPricingLink": "<PERSON>em chi tiết về bảng giá"}, "UserNotificationsPage": {"title": "<PERSON><PERSON><PERSON><PERSON> lý thông báo", "description": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>o của bạn", "typeSystem": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> hệ thống", "typeTransaction": "<PERSON><PERSON><PERSON>", "typePromotion": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", "typeContactRequest": "<PERSON><PERSON><PERSON> c<PERSON>u liên hệ", "loadingErrorTitle": "Lỗi", "loadingErrorMessage": "<PERSON><PERSON><PERSON><PERSON> thể tải thông báo", "loadingGenericError": "<PERSON><PERSON> xảy ra lỗi khi tải thông báo", "markReadSuccessTitle": "<PERSON><PERSON><PERSON><PERSON> công", "markReadSuccessMessage": "<PERSON><PERSON> đ<PERSON>h dấu thông báo là đã đọc", "markReadErrorTitle": "Lỗi", "markReadErrorMessage": "<PERSON>hông thể đánh dấu thông báo đã đọc", "markReadGenericError": "<PERSON><PERSON> xảy ra lỗi khi đánh dấu thông báo đã đọc", "markAllReadSuccessTitle": "<PERSON><PERSON><PERSON><PERSON> công", "markAllReadSuccessMessage": "<PERSON><PERSON> đ<PERSON>h dấu tất cả thông báo là đã đọc", "markAllReadErrorTitle": "Lỗi", "markAllReadErrorMessage": "<PERSON><PERSON><PERSON>ng thể đánh dấu tất cả thông báo đã đọc", "markAllReadGenericError": "<PERSON><PERSON> xảy ra lỗi khi đánh dấu tất cả thông báo đã đọc", "markAllReadButton": "<PERSON><PERSON><PERSON> dấu tất cả đã đọc", "dateFormatTimeConnector": "'lúc'", "noNotificationsMessage": "Bạn không có thông báo nào loại này.", "previousPageButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nextPageButton": "Sau"}, "PricingPage": {"viewPricing": "Bảng giá", "pricingTable": "Bảng gi<PERSON> d<PERSON>ch vụ", "individualCustomer": "<PERSON><PERSON><PERSON><PERSON> hàng cá nhân", "userRank": "<PERSON><PERSON><PERSON> thành viên", "highlightPost": "<PERSON> n<PERSON> bật", "individualCustomerService": "D<PERSON><PERSON> vụ cho khách hàng cá nhân", "servicePriceIncludes10VAT": "<PERSON><PERSON><PERSON> d<PERSON>ch vụ đã bao gồm 10% VAT.", "price": "Giá", "displayTime": "<PERSON><PERSON><PERSON><PERSON> gian hiển thị", "eachPost": "Mỗi lư<PERSON>t đ<PERSON>ng bài", "10Days": "10 ngày", "memberBenefits": "<PERSON><PERSON><PERSON><PERSON> lợi thành viên", "memberBenefitsDescription": "<PERSON><PERSON><PERSON> quyền lợi khi trở thành thành viên của chúng tôi", "trust": "<PERSON><PERSON> t<PERSON>", "trustDescription": "<PERSON><PERSON>n thị hạng thành viên bên cạnh tên thành viên nhằm tăng độ uy tín.", "quick": "<PERSON><PERSON><PERSON>", "quickDescription": "Th<PERSON>i gian duyệt bài nhanh trong vòng 5 phút kể từ lúc đăng bài.", "save": "<PERSON><PERSON><PERSON><PERSON>", "saveDescription": "Phí highlight checkpoint rẻ hơn.", "userRankDescription": "<PERSON>ố tiền sử dụng, <PERSON>h<PERSON><PERSON> tính số tiền nạp vào. Thời hạn 1 năm tính từ ngày tạo tài khoản thành công.", "highlightCheckpointPrice": "Phí highlight checkpoint", "highlightCheckpointDescription": "Phí highlight checkpoint rẻ hơn.", "pageTitle": "Bảng gi<PERSON> d<PERSON>ch vụ", "vatNotice": "<PERSON><PERSON><PERSON> d<PERSON>ch vụ đã bao gồm 10% VAT.", "individualSectionTitle": "D<PERSON><PERSON> vụ cho khách hàng cá nhân", "serviceHeader": "<PERSON><PERSON><PERSON> v<PERSON>", "priceHeader": "Đơn giá", "durationHeader": "<PERSON><PERSON><PERSON><PERSON> hạn", "postingFeeService": "<PERSON><PERSON> đ<PERSON>ng tin cơ bản", "postingFeeDuration": "10 ngày", "benefitsSectionTitle": "<PERSON><PERSON><PERSON><PERSON> lợi thành viên", "benefitsSectionDesc": "<PERSON><PERSON><PERSON> quyền lợi khi trở thành thành viên của chúng tôi", "benefit1Title": "<PERSON><PERSON> t<PERSON>", "benefit1Desc": "<PERSON><PERSON>n thị hạng thành viên bên cạnh tên thành viên nhằm tăng độ uy tín.", "benefit2Title": "<PERSON><PERSON><PERSON>", "benefit2Desc": "Th<PERSON>i gian duyệt bài nhanh trong vòng 5 phút kể từ lúc đăng bài.", "benefit3Title": "<PERSON><PERSON><PERSON><PERSON>", "benefit3Desc": "Phí highlight checkpoint rẻ hơn.", "rankingSectionTitle": "<PERSON><PERSON><PERSON> hạng thành viên", "rankingSectionDesc": "<PERSON>ố tiền sử dụng, <PERSON>h<PERSON><PERSON> tính số tiền nạp vào. Thời hạn 1 năm tính từ ngày tạo tài khoản thành công.", "highlightFeeSectionTitle": "Phí highlight checkpoint", "highlightFeeSectionDesc": "Phí highlight checkpoint theo hạng thành viên.", "rankHeader": "Hạng", "returnButton": "Quay lại trang tổng quan"}, "UserSidebar": {"profile": "<PERSON><PERSON> sơ của tôi", "payments": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON>h toán", "notifications": "<PERSON><PERSON><PERSON><PERSON> báo", "properties": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "dashboard": "<PERSON><PERSON><PERSON> quan", "setting": "Cài đặt", "transferCode": "<PERSON><PERSON> chuyển k<PERSON>n", "topUpWallet": "<PERSON>ạp tiền vào ví", "walletBalance": "Số dư ví", "userRank": "<PERSON><PERSON><PERSON> thành viên"}, "PropertyList": {"viewDetails": "<PERSON>em chi tiết", "checking": "<PERSON><PERSON> kiểm tra...", "cannotRequestVerification": "<PERSON><PERSON><PERSON><PERSON> thể yêu cầu kiểm du<PERSON>", "requestVerification": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> k<PERSON> du<PERSON> lại", "requestContact": "<PERSON><PERSON><PERSON> c<PERSON>u liên hệ", "activityHistory": "<PERSON><PERSON><PERSON> sử hoạt động", "deleting": "Đang xóa...", "delete": "Xóa", "noProperty": "Bạn chưa có tin đăng bất động sản nào.", "createProperty": "Đăng tin BĐS", "propertyListTitle": "<PERSON><PERSON> s<PERSON>ch bất động sản của bạn", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "deleteConfirmTitle": "<PERSON><PERSON><PERSON>n x<PERSON>a", "deleteConfirmMessage": "Bạn có chắc chắn muốn xóa bất động sản này?", "deleteSuccessToast": "<PERSON><PERSON><PERSON> bất động sản thành công", "deleteErrorToast": "<PERSON><PERSON> xảy ra lỗi khi xóa bất động sản", "verifyConfirmTitle": "<PERSON><PERSON><PERSON>n yêu cầu kiể<PERSON>", "verifyConfirmMessage": "Bạn đang yêu cầu kiểm duyệt lại thông tin bài đăng. Bạn còn lại {remainingTimes} lần kiểm duyệt cho bài đăng này. Bạn có chắc chắn muốn gửi cho bộ phận duyệt bài không?", "verifySuccessToast": "<PERSON><PERSON> g<PERSON>i yêu cầu kiểm duyệt thành công.", "verifyErrorToast": "<PERSON><PERSON><PERSON><PERSON> thể gửi yêu cầu kiểm du<PERSON>.", "verifyCheckErrorToast": "<PERSON><PERSON><PERSON><PERSON> thể kiểm tra số lần kiểm duyệt còn lại.", "verifyGenericErrorToast": "<PERSON><PERSON> xảy ra lỗi khi gửi yêu cầu kiểm du<PERSON>.", "moreAction": "<PERSON><PERSON> lý", "searchPlaceholder": "T<PERSON>m kiếm theo tên bài đăng...", "noResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả", "selectAll": "<PERSON><PERSON><PERSON> tất cả", "renew": "<PERSON><PERSON>", "loading": "<PERSON><PERSON> tải dữ liệu...", "approved": "Đ<PERSON>", "pendingApproval": "<PERSON>ờ <PERSON>", "rejectedByAdmin": "<PERSON><PERSON> từ chối", "rejectedDueToUnpaid": "Từ chối do chưa thanh toán", "waitingPayment": "Chờ thanh toán", "expired": "<PERSON><PERSON><PERSON>", "draft": "<PERSON>", "sold": "<PERSON><PERSON> bán", "bulkHighlightConfirmTitle": "<PERSON><PERSON><PERSON> nhận đ<PERSON>h dấu nổi bật {count} tin", "bulkHighlightConfirmMessage": "Bạn có chắc chắn muốn đánh dấu nổi bật các tin đã chọn không?", "bulkHighlightSuccessToast": "<PERSON><PERSON> đ<PERSON>h dấu nổi bật {count} tin thành công", "bulkHighlightErrorToast": "<PERSON><PERSON><PERSON><PERSON> thể đánh dấu nổi bật các tin đã chọn", "bulkHighlightGenericErrorToast": "<PERSON><PERSON> xảy ra lỗi khi đánh dấu nổi bật", "bulkRenewFeatureInDevelopment": "<PERSON><PERSON><PERSON> năng gia hạn hàng loạt đang đư<PERSON><PERSON> phát triển", "bulkDeleteConfirmTitle": "<PERSON><PERSON><PERSON> nh<PERSON>n xóa {count} tin", "bulkDeleteConfirmMessage": "Bạn có chắc chắn muốn xóa các tin đã chọn không? Hành động này không thể hoàn tác.", "bulkDeleteSuccessToast": "<PERSON><PERSON> xóa {count} tin thành công", "bulkDeleteErrorToast": "<PERSON><PERSON><PERSON><PERSON> thể xóa các tin đã chọn", "bulkDeleteGenericErrorToast": "Đã xảy ra lỗi khi xóa các tin", "previous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON>", "page": "<PERSON><PERSON>", "tryDifferentCriteria": "<PERSON><PERSON> lòng thử lại với các tiêu chí tìm kiếm khác", "searchResults": "<PERSON><PERSON><PERSON> quả tìm kiếm ({count} bất động sản)", "pageInfo": "Trang {current} / {total}", "loginRequired": "<PERSON><PERSON><PERSON> c<PERSON>u đ<PERSON>ng <PERSON>h<PERSON>p", "dontHaveAccount": "Bạn chưa có tài k<PERSON>n?", "signUpHere": "<PERSON><PERSON><PERSON> ký tại đây"}, "AboutPage": {"heroTitle": "GIỚI THIỆU VỀ YEZ HOME", "heroSubtitle": "YEZ Home là ứng dụng chuyên về bất động sản nằm trong hệ sinh thái YEZ Tech. Chúng tôi mang đến giải pháp hiện đại cho việc tìm kiếm và giao dịch bất động sản.", "missionTitle": "<PERSON><PERSON> m<PERSON>nh của chúng tôi", "missionText": "Với nhu cầu về bất động sản ngày càng tăng của các bạn trẻ, đặc biệt là các bạn sinh viên có nhu cầu về thuê nhà trọ khi bước vào năm nhất đại học. Mục đích của YEZ Home mang lại sự thuận tiện cho người có nhu cầu thật sự về mua bán và thuê bất động sản. Biến việc mua bán bất động sản trở nên trẻ trung và bớt khô khan hơn . Rút ngắn khoảng cách giữa người bán, người cho thuê bất động sản với người mua, người thuê. Gi<PERSON>p khách hàng dễ dàng hơn trọng việc tìm kiếm và lựa chọn được bất động sản ưng ý, phù hợp với nhu cầu.", "feature1Title": "Dual View & Checkpoint", "feature1Desc": "Hiển thị ngay trên trang chủ giúp người dùng dễ dàng tìm kiếm bất động sản ở địa điểm mong muốn.", "feature2Title": "<PERSON><PERSON><PERSON> th<PERSON>c ng<PERSON> dùng", "feature2Desc": "<PERSON><PERSON><PERSON> thực qua số điện tho<PERSON>i, g<PERSON><PERSON><PERSON> tăng mức độ tin cậy đối với thông tin được đăng tải.", "feature3Title": "<PERSON><PERSON><PERSON> kiếm thông minh", "feature3Desc": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON> quan, th<PERSON> th<PERSON>, thao tác dễ dàng với ngay cả người không biết nhiều về công nghệ.", "dashboardSectionTitle": "<PERSON><PERSON><PERSON><PERSON> lý hiệu quả", "dashboardSectionDesc": "V<PERSON>i màn hình dashboard hiển thị thông tin rõ ràng, tr<PERSON><PERSON> quan gi<PERSON>p ng<PERSON><PERSON><PERSON> bán, ngư<PERSON>i cho thuê và giao dịch viên bất động sản biết đư<PERSON>c hiệu quả và dễ dàng trong việc quản lý rổ sản phẩm của mình.", "companySectionTitle": "Về YEZ Tech", "companySectionDesc": "YEZ Tech là công ty về công nghệ đư<PERSON><PERSON> thành lập vào tháng 2025. Chuyên tạo ra các ứng dụng trên nền tảng website và điện thoại với mục đích mang lại sự thuận tiện và dễ dàng cho cuộc sống hiện đại.", "contactSectionTitle": "<PERSON><PERSON><PERSON> h<PERSON> với chúng tôi", "contactEmail": "<EMAIL>", "contactPhone": "Hotline: 1900 xxxx"}, "ContactPage": {"headerTitle": "<PERSON><PERSON><PERSON>", "headerSubtitle": "<PERSON><PERSON>g tôi rất sẵn lòng giải đáp mọi thắc mắc của bạn", "formTitle": "<PERSON><PERSON><PERSON> tin nhắn cho chúng tôi", "nameLabel": "Họ và tên", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> họ và tên của bạn", "emailLabel": "Email", "emailPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ email của bạn", "messageLabel": "<PERSON>", "messagePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tin nhắn của bạn ở đây", "submitButton": "<PERSON><PERSON><PERSON>", "infoTitle": "<PERSON><PERSON><PERSON><PERSON>", "infoEmailLabel": "Email", "infoPhoneLabel": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i", "infoAddressLabel": "Địa chỉ", "infoEmailValue": "<EMAIL>", "infoPhoneValue": "+1 (234) 567-890", "infoAddressValue": "123 Đường ABC, Phư<PERSON><PERSON> X, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON>"}, "NewsPage": {"pageTitle": "<PERSON> tức bất động sản mới nhất", "pageDescription": "<PERSON><PERSON><PERSON><PERSON> tin mới, đ<PERSON><PERSON> đủ, hấp dẫn về thị trường bất động sản Việt Nam", "searchPlaceholder": "T<PERSON><PERSON> kiếm theo tiêu đề...", "searchButton": "<PERSON><PERSON><PERSON>", "sortByDate": "<PERSON><PERSON><PERSON>", "sortByTitle": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "pageSizeLabel": "Hi<PERSON>n thị:", "fetchError": "<PERSON><PERSON><PERSON><PERSON> thể tải bài viết", "authorPrefix": "Bởi", "dateConnector": "•", "readMoreButton": "<PERSON><PERSON><PERSON>ê<PERSON>", "paginationShowing": "<PERSON><PERSON><PERSON> thị {start} đến {end} trên {total} kết quả", "paginationPrevious": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paginationNext": "Sau", "backButton": "Quay lại danh s<PERSON>ch", "tagsLabel": "Thẻ:", "commentsTitle": "<PERSON><PERSON><PERSON> lu<PERSON>", "noComments": "<PERSON><PERSON><PERSON> có bình luận nào.", "notFoundTitle": "<PERSON><PERSON><PERSON><PERSON> tìm thấy bài viết", "notFoundDescription": "<PERSON><PERSON><PERSON><PERSON> thể tìm thấy bài viết đ<PERSON><PERSON><PERSON> yêu cầu."}, "Navbar": {"logoAlt": "Logo YEZHome", "linkHome": "Trang chủ", "linkBuy": "<PERSON><PERSON> b<PERSON>", "linkRent": "<PERSON> thuê", "linkNews": "<PERSON> tức", "linkAbout": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "linkContact": "<PERSON><PERSON><PERSON>", "linkPricing": "Bảng giá", "userMenuMyAccount": "<PERSON><PERSON><PERSON>n của tôi", "userMenuMyProperties": "<PERSON><PERSON><PERSON> động sản của tôi", "userMenuMyFavorites": "<PERSON><PERSON> mục đang quan tâm", "notificationTitle": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>o của bạn", "notificationTimeMinutes": "{count} ph<PERSON><PERSON> tr<PERSON>c", "notificationTimeHours": "{count} g<PERSON><PERSON> trước", "notificationTimeDays": "{count} ng<PERSON><PERSON> tr<PERSON>", "notificationDefaultTitle": "<PERSON><PERSON><PERSON><PERSON> báo mới", "notificationMarkRead": "<PERSON><PERSON><PERSON> dấu đã đọc", "notificationNone": "Bạn chưa có thông báo nào", "notificationViewAll": "<PERSON><PERSON> tất cả thông báo", "favoriteTooltip": "<PERSON><PERSON> s<PERSON>ch tin đã lưu", "postProperty": "Đăng tin BĐS", "downloadApp": "<PERSON><PERSON><PERSON> dụng", "login": "<PERSON><PERSON><PERSON>", "register": "Đăng kí", "mobileMenuTitle": "<PERSON><PERSON>", "refreshData": "<PERSON><PERSON><PERSON> mới dữ liệu", "userGreeting": "Chào mừng quay trở lại"}, "VerifyEmail": {"title": "<PERSON><PERSON><PERSON>ủa Bạn", "message": "Cảm ơn bạn đã đăng ký! Chúng tôi đã gửi một liên kết xác thực đến địa chỉ email của bạn. Vui lòng kiểm tra hộp thư đến và nhấp vào liên kết để kích hoạt tài khoản của bạn.", "spamNotice": "<PERSON><PERSON><PERSON> bạn không thấy email, vui lòng kiểm tra thư mục spam.", "loginButton": "<PERSON><PERSON><PERSON>", "homeButton": "Trang chủ"}, "UserInfo": {"refresh": "<PERSON><PERSON><PERSON>", "refreshUserData": "<PERSON><PERSON><PERSON> mới dữ liệu người dùng"}, "PropertyCard": {"postDate": "<PERSON><PERSON><PERSON>", "expiryDate": "<PERSON><PERSON><PERSON> h<PERSON> hạn", "daysRemaining": "Còn {days} ngày", "expired": "<PERSON><PERSON> hết hạn", "edit": "<PERSON><PERSON><PERSON>", "share": "<PERSON><PERSON> sẻ", "more": "<PERSON><PERSON><PERSON><PERSON>", "highlightAddSuccess": "<PERSON><PERSON> đ<PERSON>h dấu nổi bật thành công", "highlightRemoveSuccess": "<PERSON><PERSON> hủy đ<PERSON>h dấu nổi bật thành công", "highlightUpdateError": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật trạng thái nổi bật", "highlightGenericError": "<PERSON><PERSON> xảy ra lỗi khi cập nhật trạng thái nổi bật", "renewFeatureInDevelopment": "<PERSON><PERSON><PERSON> năng gia hạn đang đư<PERSON><PERSON> phát triển", "renew": "<PERSON><PERSON>", "cannotRequestVerification": "<PERSON><PERSON><PERSON><PERSON> thể yêu cầu kiểm du<PERSON>", "requestVerification": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> k<PERSON> du<PERSON> lại", "requestContact": "<PERSON><PERSON><PERSON> c<PERSON>u liên hệ", "activityHistory": "<PERSON><PERSON><PERSON> sử hoạt động", "delete": "Xóa", "deleting": "Đang xóa...", "alreadyHighlighted": "B<PERSON>t động sản đang đư<PERSON>c nổi bật", "highlightConfirmTitle": "<PERSON><PERSON><PERSON> bật bất động sản", "highlightConfirmDescription": "<PERSON><PERSON><PERSON> nhận nổi bật bất động sản này", "highlightConfirmMessage": "Vi<PERSON><PERSON> làm nổi bật bài đăng này sẽ tốn {amount} VND, dựa trên hạng thành viên hiện tại của bạn ({memberRank}). Bạn có muốn tiếp tục không?", "insufficientBalanceTitle": "Số dư không đủ", "insufficientBalanceMessage": "Số dư ví của bạn hiện không đủ để thực hiện hành động này. <PERSON>ui lòng nạp thêm tiền vào ví để tiếp tục.", "currentBalance": "Số dư hiện tại", "highlightFee": "<PERSON><PERSON> n<PERSON>i bật", "balanceAfter": "Số dư sau khi thanh toán", "topUpWallet": "<PERSON>ạp tiền vào ví", "processing": "<PERSON><PERSON> xử lý...", "confirmHighlight": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "forSale": "Bán", "forRent": "<PERSON> thuê", "month": "th<PERSON>g", "bedrooms": "PN", "bathrooms": "PT", "details": "<PERSON> ti<PERSON>", "addedToFavorites": "<PERSON><PERSON> thêm vào yêu thích", "removedFromFavorites": "<PERSON>ã xóa khỏi yêu thích", "addedToFavoritesDesc": "<PERSON><PERSON>t động sản đã đư<PERSON><PERSON> thêm vào danh sách yêu thích", "removedFromFavoritesDesc": "<PERSON><PERSON>t động sản đã được xóa khỏi danh sách yêu thích", "errorOccurred": "<PERSON><PERSON> lỗi xảy ra", "cannotUpdateFavorite": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật trạng thái yêu thích"}, "PropertyShare": {"title": "<PERSON><PERSON> sẻ bất động sản", "description": "<PERSON><PERSON> sẻ thông tin bất động sản này đến bạn bè hoặc mạng xã hội", "viewProperty": "<PERSON><PERSON> b<PERSON>t độ<PERSON> sản", "copySuccess": "Đã sao chép liên kết vào bộ nhớ tạm", "copyError": "<PERSON><PERSON><PERSON><PERSON> thể sao chép liên kết", "copy": "Sao chép"}}