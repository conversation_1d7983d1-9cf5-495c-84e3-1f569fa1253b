﻿namespace RealEstate.Domain.Common
{
    public class EnumValues
    {
        public enum UserType
        {
            <PERSON><PERSON>,
            Buyer,
            Admin
        }

        public enum SysAdminRole
        {
            /// <summary>
            /// Super Mod: Quyền cao nhất trong hệ thống.
            /// </summary>
            SuperMod = 100, // Gán giá trị số để dễ quản lý hoặc phân cấp

            /// <summary>
            /// System Admin: Quản lý user hệ thống, đầy đủ quyền <PERSON>min (trừ màn hình kế toán), kiểm duyệt bài đăng, quản lý tin tức.
            /// </summary>
            SystemAdmin = 90,

            /// <summary>
            /// Admin Duyệt Bài: Chuyên trách kiểm duyệt bài đăng, không có quyền xem giá bài đăng.
            /// </summary>
            ContentModerator = 50,
            /// <summary>
            /// Admin Nội Dung: Chuyên trách quản lý và viết bài mục tin tức.
            /// </summary>
            ContentEditor = 40,
        }

        public enum PropertyStatus
        {
            Draft,
            PendingApproval,
            Approved,
            RejectedByAdmin,
            RejectedDueToUnpaid,
            WaitingPayment,
            Expired,
            Sold
        }

        public enum PostType
        {
            Sale,
            Rent,
        }

        public enum PropertyType
        {
            can_ho,
            nha_pho,
            nha_tro,
        }

        public enum ContactRequest
        {
            Pending, // Chưa xử lý.
            Read, //Đã liên hệ khách hàng.
        }

        public enum NotificationType
        {
            System,
            Transaction,
            Contact,
            Promotion,
            News,
            WalletUpdate,
            CustomerMessage
        }

        public enum TransactionType
        {
            TOP_UP,
            PAYMENT_POST,
            PAYMENT_HIGHLIGHT
        }

        public enum TransactionStatus
        {
            COMPLETED,
            PENDING,
            FAILED,
            CANCELLED
        }

        public const int UPDATE_DEFAULT_REMAINING_TIMES = 5;
    }
}
