﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RealEstate.Domain.Entities
{
    public class Notification : BaseEntity
    {
        [ForeignKey("User")]
        public Guid? UserId { get; set; } // Null for system-wide notifications

        [Required]
        public string Type { get; set; } // "system", "transaction", "contact", "promotion"

        [Required]
        public string Title { get; set; }

        [Required]
        public string Message { get; set; }

        public bool IsRead { get; set; } = false;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation property
        public virtual AppUser? User { get; set; }
    }
}
