---
description: 
globs: 
alwaysApply: false
---

# Your rule content
---

# Next.js Task Breakdown Rules

## Purpose
You are an expert project manager and frontend architect. Given a Next.js technical design document, your task is to break it down into an actionable checklist of development tasks suitable for assigning to frontend developers and tracking progress.

## Input
A Markdown technical design document for a Next.js feature, typically containing:
- Overview
- Requirements (functional/non-functional)
- Technical Design (UI, API routes, data fetching, components)
- Dependencies
- Testing Plan
- Open Questions

## Output
A Markdown checklist with granular, actionable tasks following Next.js-specific patterns. Save this markdown document in the 'document/[feature-name]/[tasks.md]' with the following guidelines below.

## Guidelines

### 1. Granularity
- Break tasks into units that take 2-8 hours to complete
- Separate:
  - Component creation
  - API route implementation
  - Data fetching logic
  - State management
  - Testing
  - Documentation

### 2. Actionable Tasks
Use clear verbs:
- "Create `<ComponentName>` in `app/[section]/`"
- "Implement POST handler in `app/api/[route]/route.ts`"
- "Add Zustand store for [feature]"
- "Write Jest test for [component]"

### 3. Dependencies
Clearly mark:
- Server-side tasks that must precede client-side work
- Shared components that need to be built first
- API routes required for data fetching

### 4. Completeness
Cover all aspects:
- New page routes
- API routes
- React components
- Hooks/custom logic
- State management
- Tests (unit, integration, e2e)
- Documentation updates
- SEO/metadata
- Accessibility requirements

### 5. Next.js-Specific Categories
Group tasks under these headings when applicable:
- **App Router Structure**
- **API Routes**
- **UI Components**
- **Data Fetching**
- **State Management**
- **Performance Optimization**
- **Testing**
- **Documentation**

## Example Breakdown

### Input (Technical Design Excerpt):
```markdown
# Product Review Feature

## Overview
Allow users to submit product reviews with ratings

## Requirements
- Users can submit star ratings (1-5) and text reviews
- Reviews display on product pages without refresh
- Admin moderation interface

## Technical Design
### API Routes
- POST /api/reviews → Create new review
- GET /api/reviews?productId=:id → Fetch reviews

### Components
- <ReviewForm> - Submission form
- <ReviewList> - Display component
- <StarRating> - Interactive rating control

### Data
- Uses React Query for data fetching
- Zustand for shared review state
```

### Output (Task Breakdown):
```markdown
## App Router Structure
- [ ] Create product review page at `app/products/[id]/reviews/page.tsx`
- [ ] Create admin moderation page at `app/admin/reviews/page.tsx`

## API Routes
- [ ] Implement POST /api/reviews route
  - [ ] Create `app/api/reviews/route.ts`
  - [ ] Add input validation (zod)
  - [ ] Connect to database service
- [ ] Implement GET /api/reviews route
  - [ ] Add query param validation
  - [ ] Implement pagination logic

## UI Components
- [ ] Create <ReviewForm> component
  - [ ] Build form layout
  - [ ] Integrate <StarRating> component
  - [ ] Implement form validation
- [ ] Create <ReviewList> component
  - [ ] Implement responsive layout
  - [ ] Add "load more" functionality
- [ ] Create <StarRating> component
  - [ ] Interactive star display
  - [ ] Accessibility support

## Data Handling
- [ ] Create useCreateReview mutation hook
- [ ] Create useProductReviews query hook
- [ ] Add review state to global store (Zustand)

## Testing
- [ ] Write Jest tests for <StarRating>
- [ ] Add Storybook stories for <ReviewList>
- [ ] Cypress test for submission flow

## Documentation
- [ ] Add JSDoc to all hooks
- [ ] Update API route Swagger docs
```

## Special Considerations for Next.js

1. **Rendering Strategy**:
   - Clearly mark tasks related to SSR/SSG/ISR configuration
   - Example: "Configure ISR revalidation for product page"

2. **Server Components**:
   - Separate server vs client component tasks
   - Example: "Implement data loading in server component"

3. **Dynamic Routes**:
   - Note dynamic route parameter requirements
   - Example: "Add [id] route param validation"

4. **Middleware**:
   - Include middleware tasks when applicable
   - Example: "Add auth check in middleware for /admin routes"

5. **Client Data Requirements**:
   - Mark where client-side data fetching is needed
   - Example: "Implement SWR hook for real-time updates"

Would you like me to adjust any aspects of this breakdown approach for your specific Next.js project needs?