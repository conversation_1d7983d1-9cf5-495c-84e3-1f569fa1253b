"use client";

import { Separator } from "@/components/ui/separator";
import PropertyImageUploader from "./PropertyImageUploader";
import PropertyVideoInput from "./PropertyVideoInput";
import CollapseHeader from "@/components/ui/collapse";
import { useTranslations } from "next-intl";

const PropertyMediaSection = ({ form, property, uploadedFiles, setUploadedFiles, isFormDisabled }) => {
  const t = useTranslations("PropertyForm");

  return (
    <CollapseHeader title={t("mediaSection")} subTitle={t("requiredInfo")}>
      <Separator className="mb-6" />

      <PropertyImageUploader
        propertyId={property?.id}
        initialImages={uploadedFiles}
        onUploadComplete={(files) => setUploadedFiles(files)}
        isFormDisabled={isFormDisabled}
      />
      <div className="mt-6">
        <PropertyVideoInput form={form} isFormDisabled={isFormDisabled} />
      </div>
    </CollapseHeader>
  );
};

export default PropertyMediaSection;
