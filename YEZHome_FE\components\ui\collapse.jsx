"use client";

import { useState, useRef, useEffect } from "react";
import { ChevronDown, ChevronUp } from "lucide-react";

const CollapseHeader = ({ title, subTitle, children }) => {
  const [isOpen, setIsOpen] = useState(true);

  const contentRef = useRef(null);
  const [maxHeight, setMaxHeight] = useState("0px");

  useEffect(() => {
    // Set the max-height based on the content's scroll height when open
    if (contentRef.current) {
      // Set the max-height based on the content's scroll height when open
      if (isOpen) {
        setMaxHeight(`${contentRef.current.scrollHeight}px`);
      } else {
        setMaxHeight("0px");
      }
    }
  }, [isOpen]);

  return (
    <div className="bg-white rounded-md shadow mb-3">
      <button
        type="button"
        className="w-full flex justify-between items-center px-4 py-3 hover:text-coral-500 transition-all"
        onClick={(e) => {
          e.preventDefault();
          setIsOpen(!isOpen);
        }}
      >
        <h3 className="font-semibold text-navy-blue">
          {title}
          {subTitle ? <span className="text-xs inline-block text-gray-400 ml-3">{`(${subTitle})`}</span> : <></>}
        </h3>
        {isOpen ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
      </button>

      <div
        className={`overflow-hidden transition-all duration-300 ease-in-out ${
          isOpen ? "p-4" : "max-h-0 p-0"
        }`}
      >
        {children}
      </div>
    </div>
  );
};

export default CollapseHeader;
