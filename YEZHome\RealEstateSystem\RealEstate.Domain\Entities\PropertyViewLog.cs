using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RealEstate.Domain.Entities
{
    public class PropertyViewLog : BaseEntityWithAuditable
    {
        [Required]
        public Guid PropertyId { get; set; }
        
        public Guid? ViewerId { get; set; }
        
        [StringLength(50)]
        public string? ViewerIP { get; set; }
        
        [Required]
        public DateTime ViewedAt { get; set; } = DateTime.UtcNow;
        
        public string? UserAgent { get; set; }
        
        public string? ReferrerUrl { get; set; }
        
        // Navigation properties
        [ForeignKey("PropertyId")]
        public Property? Property { get; set; }
        
        [ForeignKey("ViewerId")]
        public AppUser? Viewer { get; set; }
    }
}
