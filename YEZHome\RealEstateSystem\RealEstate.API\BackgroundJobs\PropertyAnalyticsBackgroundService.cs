using RealEstate.Application.Interfaces;

namespace RealEstate.API.BackgroundJobs
{
    public class PropertyAnalyticsBackgroundService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<PropertyAnalyticsBackgroundService> _logger;
        private readonly TimeSpan _period = TimeSpan.FromHours(24); // Run once per day

        public PropertyAnalyticsBackgroundService(
            IServiceProvider serviceProvider,
            ILogger<PropertyAnalyticsBackgroundService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            using PeriodicTimer timer = new PeriodicTimer(_period);

            while (!stoppingToken.IsCancellationRequested && 
                   await timer.WaitForNextTickAsync(stoppingToken))
            {
                try
                {
                    await UpdateAllPropertyEngagementSummariesAsync();
                    _logger.LogInformation("Successfully updated all property engagement summaries at {time}", DateTime.UtcNow);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred while updating property engagement summaries at {time}", DateTime.UtcNow);
                }
            }
        }

        private async Task UpdateAllPropertyEngagementSummariesAsync()
        {
            using var scope = _serviceProvider.CreateScope();
            var analyticsService = scope.ServiceProvider.GetRequiredService<IPropertyAnalyticsService>();
            await analyticsService.UpdateAllPropertiesEngagementSummaryAsync();
        }
    }
}
