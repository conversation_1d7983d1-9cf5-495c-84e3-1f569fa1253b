{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/api/map/wards/route.jsx"], "sourcesContent": ["export async function GET(request) {\n  try {\n    const API_URL = process.env.API_URL;\n    const { searchParams } = new URL(request.url);\n    const districtId = searchParams.get('districtId');\n    \n    if (!districtId) {\n      return Response.json(\n        { error: 'District ID is required' },\n        { status: 400 }\n      );\n    }\n\n    const response = await fetch(`${API_URL}/api/Address/districts/${districtId}/wards`, {\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    if (!response.ok) {\n      throw new Error(`Failed to fetch wards: ${response.status}`);\n    }\n\n    const data = await response.json();\n    return Response.json(data);\n  } catch (error) {\n    console.error('Error fetching wards:', error);\n    return Response.json(\n      { error: 'Failed to fetch wards' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAO,eAAe,IAAI,OAAO;IAC/B,IAAI;QACF,MAAM,UAAU,QAAQ,GAAG,CAAC,OAAO;QACnC,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,aAAa,aAAa,GAAG,CAAC;QAEpC,IAAI,CAAC,YAAY;YACf,OAAO,SAAS,IAAI,CAClB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,uBAAuB,EAAE,WAAW,MAAM,CAAC,EAAE;YACnF,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,MAAM,EAAE;QAC7D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,SAAS,IAAI,CAAC;IACvB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,SAAS,IAAI,CAClB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}