"use client";
import { useState, useEffect } from "react";
import { Switch } from "@/components/ui/switch";
import { CalendarIcon } from "lucide-react";
import { format, addDays, set } from "date-fns";
import { vi } from "date-fns/locale";
import { Label } from "@radix-ui/react-dropdown-menu";
import { DEFAULT_POST_PRICE, MemberRank } from "@/lib/enum";
import BadgeStatus from "../layout/BadgeStatus";
import BadgeUserRank from "../layout/BadgeUserRank";
import { useTranslations } from "next-intl";
import { useProfile } from "@/contexts/ProfileContext";
import { getHighlightPriceNumber } from "@/lib/memberRankUtils";

export default function CreatePropertyDetailInformation({
  status,
  statusText,
  isFormDisabled,
  createdAt,
  expiresAt,
  highlight,
  setHighlight,
  autoRenew,
  setAutoRenew,
  basePostPrice = DEFAULT_POST_PRICE,
  onRankChange = () => {},
  onRefreshRef = null, // Ref to expose refresh function
}) {
  const t = useTranslations("CreatePropertyDetailInformation");
  const { profile } = useProfile();
  const displayDuration = 10; // days

  // Get the user's rank from profile context
  const userRankFromProfile = profile?.user?.memberRank || MemberRank.DEFAULT;

  // State to track the current rank (can be updated by BadgeUserRank component)
  const [currentRank, setCurrentRank] = useState(userRankFromProfile);

  // Update currentRank when profile changes
  useEffect(() => {
    if (profile?.user?.memberRank) {
      setCurrentRank(profile.user.memberRank);
    }
  }, [profile]);

  // Calculate highlight price using the utility function
  const highlightPrice = getHighlightPriceNumber(currentRank);
  const totalPrice = basePostPrice + (highlight ? highlightPrice : 0);

  const expirationDate = expiresAt || addDays(createdAt, displayDuration);

  return (
    <>
      <div className="space-y-3">
        <div>
          <Label className="text-muted-foreground">{t('customerType')}</Label>
          <p className="font-medium">{t('individualCustomer')}</p>
        </div>
        <div>
          <Label className="text-muted-foreground">{t('memberRank')}</Label>
          <BadgeUserRank
            showRefreshButton={true}
            onRankChange={(rankData) => {
              // Only update if the rank has actually changed
              if (rankData.currentRank !== currentRank) {
                setCurrentRank(rankData.currentRank);
                onRankChange(rankData);
              }
            }}
            onRefreshRef={onRefreshRef}
          />
        </div>
      </div>

      <div className="border-t pt-3">
        <div className="flex items-center justify-between">
          <div>
            <Label htmlFor="highlight" className="font-medium">
              {t('highlightPost')}
            </Label>
            <p className="text-sm text-muted-foreground">
              {t('highlightPostDescription')}
            </p>
          </div>
          {!isFormDisabled && (
            <Switch id="highlight" checked={highlight} onCheckedChange={setHighlight(highlight => !highlight)} />
          )}
        </div>
      </div>

      <div className="border-t pt-3">
        <div className="flex items-center justify-between">
          <div>
            <Label htmlFor="highlight" className="font-medium">
              {t('autoRenew')}
            </Label>
            <p className="text-sm text-muted-foreground">
              {t('autoRenewDescription')}
            </p>
          </div>
          {!isFormDisabled && (
            <Switch id="auto-renew" checked={autoRenew} onCheckedChange={(checked) => {setAutoRenew(checked);}} />
          )}
        </div>
      </div>

      <div className="border-t pt-3">
        <div className="space-y-3">
          <div>
            <Label className="text-muted-foreground">{t('postStatus')}</Label>
            <BadgeStatus status={status} statusText={statusText}></BadgeStatus>
          </div>
          <div>
            <Label className="text-muted-foreground">{t('expirationTime')}</Label>
            <p className="font-medium">{displayDuration} {t('days')}</p>
          </div>
          <div>
            <Label className="text-muted-foreground">{t('postDate')}</Label>
            <div className="flex items-center gap-2">
              <CalendarIcon className="h-4 w-4 text-muted-foreground" />
              <p className="font-medium">{format(createdAt, "dd/MM/yyyy", { locale: vi })}</p>
            </div>
          </div>
          <div>
            <Label className="text-muted-foreground">{t('expirationDate')}</Label>
            <div className="flex items-center gap-2">
              <CalendarIcon className="h-4 w-4 text-muted-foreground" />
              <p className="font-medium">{format(expirationDate, "dd/MM/yyyy", { locale: vi })}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="border-t pt-3">
        <div className="space-y-1">
          <div className="flex justify-between">
            <span>{t('postFee')}</span>
            <span>{basePostPrice.toLocaleString("vi-VN")} đ</span>
          </div>
          {highlight && (
            <div className="flex justify-between mb-3 pb-3 ">
              <span>{t('highlightFee')} <BadgeUserRank externalRank={currentRank} /></span>
              <span>{highlightPrice.toLocaleString("vi-VN")} đ</span>
            </div>
          )}
          <div className="flex justify-between font-bold text-lg pt-2 border-t">
            <span>{t('total')}</span>
            <span>{totalPrice.toLocaleString("vi-VN")} đ</span>
          </div>
        </div>
      </div>
    </>
  );
}
