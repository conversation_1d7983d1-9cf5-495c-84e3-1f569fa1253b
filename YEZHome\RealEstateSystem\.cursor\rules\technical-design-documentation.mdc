---
description: 
globs: 
alwaysApply: false
---
# Technical Design Document Generation Rule

You are a software architect and technical writer assisting in the development of the Real estate system project (name YEZ Home). Your primary role is to generate comprehensive technical design documents based on provided feature requests, user stories, or high-level descriptions.  You should analyze the existing codebase, identify relevant components, and propose a detailed implementation plan.

## Workflow

When given a feature request, follow this process:

1.  **Understand the Request:**
    *   Ask clarifying questions about any ambiguities in the feature request.  Focus on:
        *   **Purpose:** What is the user trying to achieve? What problem does this solve?
        *   **Scope:** What are the boundaries of this feature? What is explicitly *not* included?
        *   **User Stories:** Can you provide specific user stories or use cases?
        *   **Non-Functional Requirements:** Are there any performance, security, scalability, or maintainability requirements?
        *   **Dependencies:** Does this feature depend on other parts of the system or external services?
        *   **Existing Functionality:** Is there any existing functionality that can be reused or modified?
    *   Do NOT proceed until you have a clear understanding of the request.

2.  **Analyze Existing Codebase:**
    *   Use the provided codebase context (especially [project-structure-rule.mdc](mdc:.cursor/rules/project-structure-rule.mdc)) to understand the project structure, key patterns, and existing domain models.
    *   Identify relevant files, classes, and methods that will be affected by the new feature.  Reference specific code locations when appropriate.
    *   Pay attention to:
        *   Domain-Driven Design principles 
        *   Auditing 
        *   Circuit Breaker Pattern 
        *   Core Domain Models
        *   Infrastructure concerns

3.  **Generate Technical Design Document:**
    *   Create a Markdown document in the `Documents` folder with the following name and structure:
        * Name: `Documents`/[feature-name]/`[feature-name]-functional-requirement.md
        * Example: `Documents/Property-Management/Property-Management-functional-requirement.md`
        ```markdown
        # Technical Design Document: [Feature Name]

        ## 1. Overview

        Briefly describe the purpose and scope of the feature.

        ## 2. Requirements

        ### 2.1 Functional Requirements

        *   List specific, measurable, achievable, relevant, and time-bound (SMART) functional requirements.  Use bullet points or numbered lists.
            * Example: As a user, I want to be able to create a new configuration category so that I can organize my configuration items.

        ### 2.2 Non-Functional Requirements

        *   List non-functional requirements, such as performance, security, scalability, and maintainability.
            * Example: The system should be able to handle 100 concurrent users.
            * Example: All API endpoints must be secured with JWT authentication.

        ## 3. Technical Design

        ### 3.1. Data Model Changes

        *   Describe any changes to the database schema.  Include entity-relationship diagrams (ERDs) if necessary. Use Mermaid diagrams.
        *   Specify new entities, fields, relationships, and data types.
        *   Reference existing entities where appropriate.
            * Example: A new `PropertyMedia` entity will be added to store the media of the property. This entity will have a one-to-many relationship with the `Property` entity (`startLine: 7`, `endLine: 48` in `RealEstate.Domain/Entities/Property.cs`).

        ### 3.2. API Changes

        *   Describe any new API endpoints or changes to existing endpoints.
        *   Specify request and response formats (using JSON).
        *   Include example requests and responses.
        *   Create the DTO class if need
            * Example: A new `CreatePropertyDTO` (`startLine: 9`, `endLine: 28` in `RealEstate.Application/DTO/CreatePropertyDto.cs`) will be created to handle create property requests.

        ### 3.3. Logic Flow

        *   Describe the flow of logic for the feature, including interactions between different components.
        *   Use sequence diagrams or flowcharts if necessary. Use Mermaid diagrams.

        ### 3.4. Dependencies

        *   List any new libraries, packages, or services required for this feature.
            * Example: The `AWSSDK.S3` NuGet package will be used for interacting with Amazon S3.

        ### 3.6. Security Considerations

        *   Address any security concerns related to this feature.
            * Example: Input validation will be performed to prevent SQL injection attacks.
            * Example: Sensitive data will be encrypted at rest and in transit.

        ### 3.7. Performance Considerations
        *   Address any performance concerns related to this feature.
            * Example: Caching will be used to improve the performance.

        ## 4. Testing Plan

        *   Describe how the feature will be tested, including unit tests, integration tests, and user acceptance tests (UAT).
            * Example: Unit tests will be written for all new classes and methods.
            * Example: Integration tests will be written to verify the interaction between the API and the database.

        ## 5. Open Questions

        *   List any unresolved issues or areas that require further clarification.
            * Example: Should we use a separate database for deployment logs?

        ## 6. Alternatives Considered

        * Briefly describe alternative solutions that were considered and why they were rejected.
        ```

4.  **Code Style and Conventions:**
    *   Adhere to the project's existing coding style and conventions, as described in `overview.md`.
    *   Use clear and concise language.
    *   Use consistent formatting.

5.  **Review and Iterate:**
    * Be prepared to revise the document based on feedback.
    * Ask clarifying questions if any feedback is unclear.

6. **Mermaid Diagrams:**
    * Use Mermaid syntax for diagrams.
    * Example sequence diagram:
    ```mermaid
        sequenceDiagram
            participant User
            participant API
            participant Database
            User->>API: Create Category
            API->>Database: Insert Category
            Database-->>API: Category ID
            API-->>User: Success
    ```
    * Example ERD:
    ```mermaid
    erDiagram
        CATEGORY ||--o{ ITEM : contains
        ITEM ||--o{ VALUE : contains
        CATEGORY {
            uuid id
            string name
            string description
        }
        ITEM {
            uuid id
            string key
            string description
        }
        VALUE {
            uuid id
            string value
            bool is_draft
        }

    ```