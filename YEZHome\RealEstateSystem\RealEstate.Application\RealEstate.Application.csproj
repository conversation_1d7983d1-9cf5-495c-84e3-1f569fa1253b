﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.0" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.3.0" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.7" />
    <PackageReference Include="SixLabors.ImageSharp.Drawing" Version="2.1.5" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.3.0" />
    <PackageReference Include="EPPlus" Version="7.0.10" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\RealEstate.Domain\RealEstate.Domain.csproj" />
  </ItemGroup>

</Project>
