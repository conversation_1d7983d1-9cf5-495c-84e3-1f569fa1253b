using AutoMapper;
using Microsoft.EntityFrameworkCore;
using RealEstate.Application.DTO.Analytics;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using System.Text.Json;

namespace RealEstate.Application.Services
{
    public class PropertyAnalyticsService : IPropertyAnalyticsService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public PropertyAnalyticsService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task LogPropertyViewAsync(Guid propertyId, Guid? viewerId, string viewerIp, string userAgent, string referrerUrl)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(propertyId);
            if (property == null)
            {
                throw new KeyNotFoundException($"Property with ID {propertyId} not found.");
            }

            var viewLog = new PropertyViewLog
            {
                PropertyId = propertyId,
                ViewerId = viewerId,
                ViewerIP = viewerIp,
                ViewedAt = DateTime.UtcNow,
                UserAgent = userAgent,
                ReferrerUrl = referrerUrl
            };

            await _unitOfWork.PropertyViewLogs.AddAsync(viewLog);
            await _unitOfWork.SaveChangesAsync();

            // Update the engagement summary asynchronously
            await UpdatePropertyEngagementSummaryAsync(propertyId);
        }

        public async Task LogPropertySpendingAsync(Guid propertyId, Guid userId, decimal amount, string spendingType, Guid? transactionId, string details)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(propertyId);
            if (property == null)
            {
                throw new KeyNotFoundException($"Property with ID {propertyId} not found.");
            }

            var spendingLog = new PropertySpendingLog
            {
                PropertyId = propertyId,
                UserId = userId,
                Amount = amount,
                SpendingType = spendingType,
                TransactionId = transactionId,
                SpentAt = DateTime.UtcNow,
                Details = details
            };

            await _unitOfWork.PropertySpendingLogs.AddAsync(spendingLog);
            await _unitOfWork.SaveChangesAsync();

            // Update the engagement summary asynchronously
            await UpdatePropertyEngagementSummaryAsync(propertyId);
        }

        public async Task<PropertyAnalyticsDto> GetPropertyAnalyticsAsync(Guid propertyId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(propertyId);
            if (property == null)
            {
                throw new KeyNotFoundException($"Property with ID {propertyId} not found.");
            }

            // Set default date range if not provided
            startDate ??= DateTime.UtcNow.AddMonths(-1);
            endDate ??= DateTime.UtcNow;

            // Get property views within the date range
            var viewLogs = await _unitOfWork.PropertyViewLogs.GetQueryable()
                .Where(v => v.PropertyId == propertyId && v.ViewedAt >= startDate && v.ViewedAt <= endDate)
                .ToListAsync();

            // Get property spending within the date range
            var spendingLogs = await _unitOfWork.PropertySpendingLogs.GetQueryable()
                .Where(s => s.PropertyId == propertyId && s.SpentAt >= startDate && s.SpentAt <= endDate)
                .ToListAsync();

            // Get favorites count
            var favoritesCount = await _unitOfWork.UserFavorites.GetQueryable()
                .CountAsync(f => f.PropertyID == propertyId);

            // Get or create engagement summary
            var engagementSummary = await _unitOfWork.PropertyEngagementSummaries.GetQueryable()
                .FirstOrDefaultAsync(s => s.PropertyId == propertyId);

            if (engagementSummary == null)
            {
                // Create a new summary if it doesn't exist
                await UpdatePropertyEngagementSummaryAsync(propertyId);
                engagementSummary = await _unitOfWork.PropertyEngagementSummaries.GetQueryable()
                    .FirstOrDefaultAsync(s => s.PropertyId == propertyId);
            }

            // Calculate daily views
            var dailyViews = viewLogs
                .GroupBy(v => v.ViewedAt.Date)
                .Select(g => new DailyViewsDto
                {
                    Date = g.Key,
                    Views = g.Count(),
                    UniqueViews = g.Select(v => v.ViewerId).Distinct().Count()
                })
                .OrderBy(d => d.Date)
                .ToList();

            // Calculate daily spending
            var dailySpending = spendingLogs
                .GroupBy(s => s.SpentAt.Date)
                .Select(g => new DailySpendingDto
                {
                    Date = g.Key,
                    Amount = g.Sum(s => s.Amount),
                    SpendingType = g.GroupBy(s => s.SpendingType)
                                    .OrderByDescending(sg => sg.Sum(s => s.Amount))
                                    .First().Key
                })
                .OrderBy(d => d.Date)
                .ToList();

            // Create the analytics DTO
            var analyticsDto = new PropertyAnalyticsDto
            {
                PropertyId = propertyId,
                PropertyTitle = property.Name,
                PropertyStatus = property.Status,
                CreatedAt = property.CreatedAt,
                ExpiresAt = property.ExpiresAt,
                
                // Engagement metrics
                TotalViews = engagementSummary?.TotalViews ?? 0,
                UniqueViews = engagementSummary?.UniqueViews ?? 0,
                TotalFavorites = favoritesCount,
                
                // Financial metrics
                TotalSpent = engagementSummary?.TotalSpent ?? 0,
                ExtensionSpent = engagementSummary?.ExtensionSpent ?? 0,
                HighlightSpent = engagementSummary?.HighlightSpent ?? 0,
                
                // Trend data
                ViewsTrend = dailyViews,
                SpendingTrend = dailySpending
            };

            return analyticsDto;
        }

        public async Task<PagedResultDto<PropertyAnalyticsDto>> GetUserPropertiesAnalyticsAsync(Guid userId, PropertyAnalyticsFilterDto filter)
        {
            // Get user's properties
            var propertiesQuery = _unitOfWork.Properties.GetQueryable()
                .Where(p => p.OwnerID == userId);

            // Apply filters
            if (filter.StartDate.HasValue)
            {
                propertiesQuery = propertiesQuery.Where(p => p.CreatedAt >= filter.StartDate.Value);
            }

            if (filter.EndDate.HasValue)
            {
                propertiesQuery = propertiesQuery.Where(p => p.CreatedAt <= filter.EndDate.Value);
            }

            if (filter.PropertyStatuses != null && filter.PropertyStatuses.Any())
            {
                propertiesQuery = propertiesQuery.Where(p => filter.PropertyStatuses.Contains(p.Status));
            }

            // Get total count before pagination
            var totalCount = await propertiesQuery.CountAsync();

            // Apply sorting
            if (!string.IsNullOrEmpty(filter.SortBy))
            {
                switch (filter.SortBy.ToLower())
                {
                    case "createdat":
                        propertiesQuery = filter.SortDescending
                            ? propertiesQuery.OrderByDescending(p => p.CreatedAt)
                            : propertiesQuery.OrderBy(p => p.CreatedAt);
                        break;
                    case "expiresat":
                        propertiesQuery = filter.SortDescending
                            ? propertiesQuery.OrderByDescending(p => p.ExpiresAt)
                            : propertiesQuery.OrderBy(p => p.ExpiresAt);
                        break;
                    case "name":
                        propertiesQuery = filter.SortDescending
                            ? propertiesQuery.OrderByDescending(p => p.Name)
                            : propertiesQuery.OrderBy(p => p.Name);
                        break;
                    default:
                        propertiesQuery = propertiesQuery.OrderByDescending(p => p.CreatedAt);
                        break;
                }
            }
            else
            {
                propertiesQuery = propertiesQuery.OrderByDescending(p => p.CreatedAt);
            }

            // Apply pagination
            var properties = await propertiesQuery
                .Skip((filter.Page - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            // Get property IDs for further queries
            var propertyIds = properties.Select(p => p.Id).ToList();

            // Get engagement summaries for these properties
            var engagementSummaries = await _unitOfWork.PropertyEngagementSummaries.GetQueryable()
                .Where(s => propertyIds.Contains(s.PropertyId))
                .ToListAsync();

            // Get favorites counts
            var favoritesCounts = await _unitOfWork.UserFavorites.GetQueryable()
                .Where(f => propertyIds.Contains(f.PropertyID))
                .GroupBy(f => f.PropertyID)
                .Select(g => new { PropertyId = g.Key, Count = g.Count() })
                .ToListAsync();

            // Create analytics DTOs
            var analyticsDtos = new List<PropertyAnalyticsDto>();

            foreach (var property in properties)
            {
                var summary = engagementSummaries.FirstOrDefault(s => s.PropertyId == property.Id);
                var favoritesCount = favoritesCounts.FirstOrDefault(f => f.PropertyId == property.Id)?.Count ?? 0;

                // Apply additional filters
                if (filter.MinViews.HasValue && (summary?.TotalViews ?? 0) < filter.MinViews.Value)
                    continue;

                if (filter.MaxViews.HasValue && (summary?.TotalViews ?? 0) > filter.MaxViews.Value)
                    continue;

                if (filter.MinSpent.HasValue && (summary?.TotalSpent ?? 0) < filter.MinSpent.Value)
                    continue;

                if (filter.MaxSpent.HasValue && (summary?.TotalSpent ?? 0) > filter.MaxSpent.Value)
                    continue;

                var analyticsDto = new PropertyAnalyticsDto
                {
                    PropertyId = property.Id,
                    PropertyTitle = property.Name,
                    PropertyStatus = property.Status,
                    CreatedAt = property.CreatedAt,
                    ExpiresAt = property.ExpiresAt,
                    
                    // Engagement metrics
                    TotalViews = summary?.TotalViews ?? 0,
                    UniqueViews = summary?.UniqueViews ?? 0,
                    TotalFavorites = favoritesCount,
                    
                    // Financial metrics
                    TotalSpent = summary?.TotalSpent ?? 0,
                    ExtensionSpent = summary?.ExtensionSpent ?? 0,
                    HighlightSpent = summary?.HighlightSpent ?? 0,
                    
                    // Trend data will be empty for list view
                    ViewsTrend = new List<DailyViewsDto>(),
                    SpendingTrend = new List<DailySpendingDto>()
                };

                analyticsDtos.Add(analyticsDto);
            }

            // Calculate page count
            var pageCount = (int)Math.Ceiling(totalCount / (double)filter.PageSize);

            // Create paged result
            var pagedResult = new PagedResultDto<PropertyAnalyticsDto>
            {
                Items = analyticsDtos,
                TotalCount = totalCount,
                PageCount = pageCount,
                CurrentPage = filter.Page,
                PageSize = filter.PageSize
            };

            return pagedResult;
        }

        public async Task<byte[]> ExportPropertyAnalyticsToExcelAsync(Guid propertyId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var analytics = await GetPropertyAnalyticsAsync(propertyId, startDate, endDate);
            
            // TODO: Implement Excel export functionality
            // This would typically use a library like EPPlus or ClosedXML to create an Excel file
            
            // For now, return a placeholder
            var jsonBytes = JsonSerializer.SerializeToUtf8Bytes(analytics);
            return jsonBytes;
        }

        public async Task<byte[]> ExportUserPropertiesAnalyticsToExcelAsync(Guid userId, PropertyAnalyticsFilterDto filter)
        {
            var analytics = await GetUserPropertiesAnalyticsAsync(userId, filter);
            
            // TODO: Implement Excel export functionality
            // This would typically use a library like EPPlus or ClosedXML to create an Excel file
            
            // For now, return a placeholder
            var jsonBytes = JsonSerializer.SerializeToUtf8Bytes(analytics);
            return jsonBytes;
        }

        public async Task UpdatePropertyEngagementSummaryAsync(Guid propertyId)
        {
            // Get property to ensure it exists
            var property = await _unitOfWork.Properties.GetByIdAsync(propertyId);
            if (property == null)
            {
                throw new KeyNotFoundException($"Property with ID {propertyId} not found.");
            }

            // Get existing summary or create new one
            var summary = await _unitOfWork.PropertyEngagementSummaries.GetQueryable()
                .FirstOrDefaultAsync(s => s.PropertyId == propertyId);

            var isNew = false;
            if (summary == null)
            {
                summary = new PropertyEngagementSummary
                {
                    PropertyId = propertyId,
                    LastUpdatedAt = DateTime.UtcNow
                };
                isNew = true;
            }

            // Calculate total views
            var totalViews = await _unitOfWork.PropertyViewLogs.GetQueryable()
                .CountAsync(v => v.PropertyId == propertyId);

            // Calculate unique views
            var uniqueViews = await _unitOfWork.PropertyViewLogs.GetQueryable()
                .Where(v => v.PropertyId == propertyId && v.ViewerId != null)
                .Select(v => v.ViewerId)
                .Distinct()
                .CountAsync();

            // Calculate total favorites
            var totalFavorites = await _unitOfWork.UserFavorites.GetQueryable()
                .CountAsync(f => f.PropertyID == propertyId);

            // Calculate spending metrics
            var spendingLogs = await _unitOfWork.PropertySpendingLogs.GetQueryable()
                .Where(s => s.PropertyId == propertyId)
                .ToListAsync();

            var totalSpent = spendingLogs.Sum(s => s.Amount);
            var extensionSpent = spendingLogs
                .Where(s => s.SpendingType.Contains("extension"))
                .Sum(s => s.Amount);
            var highlightSpent = spendingLogs
                .Where(s => s.SpendingType.Contains("highlight"))
                .Sum(s => s.Amount);

            // Update summary
            summary.TotalViews = totalViews;
            summary.UniqueViews = uniqueViews;
            summary.TotalFavorites = totalFavorites;
            summary.TotalSpent = totalSpent;
            summary.ExtensionSpent = extensionSpent;
            summary.HighlightSpent = highlightSpent;
            summary.LastUpdatedAt = DateTime.UtcNow;

            if (isNew)
            {
                await _unitOfWork.PropertyEngagementSummaries.AddAsync(summary);
            }
            else
            {
                _unitOfWork.PropertyEngagementSummaries.Update(summary);
            }

            await _unitOfWork.SaveChangesAsync();
        }

        public async Task UpdateAllPropertiesEngagementSummaryAsync()
        {
            // Get all property IDs
            var propertyIds = await _unitOfWork.Properties.GetQueryable()
                .Select(p => p.Id)
                .ToListAsync();

            // Update each property's engagement summary
            foreach (var propertyId in propertyIds)
            {
                try
                {
                    await UpdatePropertyEngagementSummaryAsync(propertyId);
                }
                catch (Exception)
                {
                    // Log the error but continue with other properties
                    // In a real implementation, you would use a logger here
                }
            }
        }
    }
}
