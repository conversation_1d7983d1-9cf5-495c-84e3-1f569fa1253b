using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;

namespace RealEstate.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class UserFavoritesController : BaseController
    {
        private readonly IUserFavoriteService _favoriteService;

        public UserFavoritesController(IUserFavoriteService favoriteService)
        {
            _favoriteService = favoriteService;
        }

        [HttpGet("favorites")]
        public async Task<ActionResult<IEnumerable<UserFavoriteDto>>> GetUserFavorites()
        {
            var userId = GetUserId();
            if (!userId.HasValue)
            {
                return Unauthorized("User not authenticated");
            }

            var favorites = await _favoriteService.GetUserFavoritesAsync(userId.Value);
            return Ok(favorites);
        }

        [HttpPost("add")]
        public async Task<IActionResult> AddToFavorites([FromBody] CreateUserFavoriteDto request)
        {
            var userId = GetUserId();
            if (!userId.HasValue)
            {
                return Unauthorized("User not authenticated");
            }

            try
            {
                var result = await _favoriteService.AddToFavoritesAsync(userId.Value, request.PropertyId);
                return Ok(new { success = result });
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpDelete("remove/{propertyId}")]
        public async Task<IActionResult> RemoveFromFavorites(Guid propertyId)
        {
            var userId = GetUserId();
            if (!userId.HasValue)
            {
                return Unauthorized("User not authenticated");
            }

            try
            {
                var result = await _favoriteService.RemoveFromFavoritesAsync(userId.Value, propertyId);
                return Ok(new { success = result });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpPost("check")]
        public async Task<ActionResult<List<FavoriteStatusDto>>> CheckFavoriteStatus([FromBody] FavoriteCheckRequestDto request)
        {
            var userId = GetUserId();
            if (!userId.HasValue)
            {
                return Unauthorized("User not authenticated");
            }

            try
            {
                var result = await _favoriteService.CheckFavoriteStatusAsync(userId.Value, request.PropertyIds);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpGet("count")]
        public async Task<ActionResult<FavoriteCountDto>> GetFavoritesCount()
        {
            var userId = GetUserId();
            if (!userId.HasValue)
            {
                return Unauthorized("User not authenticated");
            }

            try
            {
                var count = await _favoriteService.GetFavoritesCountAsync(userId.Value);
                return Ok(new FavoriteCountDto { Count = count });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }
    }
} 