using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.DTO.Analytics;
using RealEstate.Application.Interfaces;

namespace RealEstate.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class PropertyAnalyticsController : BaseController
    {
        private readonly IPropertyAnalyticsService _analyticsService;
        private readonly IPropertyService _propertyService;
        private readonly ILogger<PropertyAnalyticsController> _logger;

        public PropertyAnalyticsController(
            IPropertyAnalyticsService analyticsService,
            IPropertyService propertyService,
            ILogger<PropertyAnalyticsController> logger)
        {
            _analyticsService = analyticsService;
            _propertyService = propertyService;
            _logger = logger;
        }

        [HttpGet("property/{propertyId}")]
        public async Task<ActionResult<PropertyAnalyticsDto>> GetPropertyAnalytics(
            Guid propertyId,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var analytics = await _analyticsService.GetPropertyAnalyticsAsync(propertyId, startDate, endDate);
                return Ok(analytics);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting property analytics for property {PropertyId}", propertyId);
                return StatusCode(500, "An error occurred while retrieving property analytics");
            }
        }

        [HttpGet("user")]
        public async Task<ActionResult<RealEstate.Application.DTO.Analytics.PagedResultDto<PropertyAnalyticsDto>>> GetUserPropertiesAnalytics(
            [FromQuery] PropertyAnalyticsFilterDto filter)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var analytics = await _analyticsService.GetUserPropertiesAnalyticsAsync(userId.Value, filter);
                return Ok(analytics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user properties analytics");
                return StatusCode(500, "An error occurred while retrieving user properties analytics");
            }
        }

        [HttpGet("property/{propertyId}/export")]
        public async Task<IActionResult> ExportPropertyAnalytics(
            Guid propertyId,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var excelBytes = await _analyticsService.ExportPropertyAnalyticsToExcelAsync(propertyId, startDate, endDate);
                return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"property_analytics_{propertyId}.xlsx");
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting property analytics for property {PropertyId}", propertyId);
                return StatusCode(500, "An error occurred while exporting property analytics");
            }
        }

        [HttpGet("user/export")]
        public async Task<IActionResult> ExportUserPropertiesAnalytics(
            [FromQuery] PropertyAnalyticsFilterDto filter)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var excelBytes = await _analyticsService.ExportUserPropertiesAnalyticsToExcelAsync(userId.Value, filter);
                return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "user_properties_analytics.xlsx");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting user properties analytics");
                return StatusCode(500, "An error occurred while exporting user properties analytics");
            }
        }

        [HttpPost("property/{propertyId}/view")]
        [AllowAnonymous]
        public async Task<IActionResult> LogPropertyView(Guid propertyId)
        {
            try
            {
                var viewerId = GetUserId();
                var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
                var userAgent = Request.Headers["User-Agent"].ToString();
                var referrer = Request.Headers["Referer"].ToString();

                await _analyticsService.LogPropertyViewAsync(
                    propertyId,
                    viewerId,
                    ipAddress,
                    userAgent,
                    referrer
                );

                return Ok();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error logging property view for property {PropertyId}", propertyId);
                return StatusCode(500, "An error occurred while logging property view");
            }
        }

        /// <summary>
        /// Get the status history of a property
        /// </summary>
        /// <param name="propertyId">The ID of the property</param>
        /// <returns>List of property status history entries</returns>
        /// <response code="200">Returns the property history</response>
        /// <response code="401">If the user is not authenticated</response>
        [HttpGet("history/{propertyId}")]
        [Authorize(Policy = "UserExists")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<IEnumerable<PropertyStatusLogDto>>> GetPropertyHistory(Guid propertyId)
        {
            try
            {
                var history = await _propertyService.GetPropertyHistoryStatus(propertyId);
                return Ok(history);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting property history for property {PropertyId}", propertyId);
                return StatusCode(500, "An error occurred while retrieving property history");
            }
        }
    }
}
