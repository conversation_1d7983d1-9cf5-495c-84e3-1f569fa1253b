"use client";
import { useEffect, useState, useMemo } from "react";
import { getNotifications, markAsRead } from "@/app/actions/server/notification";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { Bell, Check, CheckCheck, Gift, MessageCircle, Wallet, FileText, ChevronLeft, ChevronRight } from "lucide-react";
import { format } from "date-fns";
import { vi, enUS } from "date-fns/locale";
import { toast } from "@/hooks/use-toast";
import { useTranslations, useLocale } from 'next-intl';
import {Link} from '@/i18n/navigation';;

export default function NotificationList() {
  const t = useTranslations('UserNotificationsPage');
  const locale = useLocale();
  const dateLocale = locale === 'vi' ? vi : enUS;

  const NOTIFICATION_TYPES = useMemo(() => [
    { id: "system", label: t('typeSystem'), icon: <FileText className="h-4 w-4" /> },
    { id: "transaction", label: t('typeTransaction'), icon: <Wallet className="h-4 w-4" /> },
    { id: "promotion", label: t('typePromotion'), icon: <Gift className="h-4 w-4" /> },
    { id: "contact", label: t('typeContactRequest'), icon: <MessageCircle className="h-4 w-4" /> },
  ], [t]);

  const [activeTab, setActiveTab] = useState(NOTIFICATION_TYPES[0].id);
  const [loading, setLoading] = useState(false);
  const [notifications, setNotifications] = useState({});
  const [pagination, setPagination] = useState({});

  useEffect(() => {
      const initialNotifications = {};
      const initialPagination = {};
      NOTIFICATION_TYPES.forEach((type) => {
          initialNotifications[type.id] = [];
          initialPagination[type.id] = { page: 1, totalPages: 1, totalItems: 0 };
      });
      setNotifications(initialNotifications);
      setPagination(initialPagination);
  }, []);

  const loadNotifications = async (type = activeTab, page = 1) => {
    setLoading(true);
    try {
      const response = await getNotifications({
        type,
        page,
        limit: 10,
      });
      if (response.success) {
        setNotifications((prev) => ({
          ...prev,
          [type]: response.data.items || [],
        }));
        setPagination((prev) => ({
          ...prev,
          [type]: {
            page: response.data.currentPage || 1,
            totalPages: response.data.pageCount || 1,
            totalItems: response.data.totalCount || 0,
          },
        }));
      } else {
        toast({
          variant: "destructive",
          title: t('loadingErrorTitle'),
          description: response.message || t('loadingErrorMessage'),
        });
      }
    } catch (error) {
      console.error("Error loading notifications:", error);
      toast({
        variant: "destructive",
        title: t('loadingErrorTitle'),
        description: t('loadingGenericError'),
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
     if (NOTIFICATION_TYPES.length > 0) {
        loadNotifications(NOTIFICATION_TYPES[0].id, 1);
     }
  }, [NOTIFICATION_TYPES]);

  const handleTabChange = (value) => {
    setActiveTab(value);
    if (!notifications[value] || notifications[value].length === 0) {
       if (pagination[value]?.totalPages >= 1) {
           loadNotifications(value, 1);
       }
    }
  };

  const handlePageChange = (type, newPage) => {
    if (newPage >= 1 && newPage <= pagination[type]?.totalPages) {
        loadNotifications(type, newPage);
    }
  };

  const handleMarkAsRead = async (id) => {
    try {
      const response = await markAsRead({ ids: [id] });
      if (response.success) {
        setNotifications((prev) => {
          const updatedNotifications = { ...prev };
          Object.keys(updatedNotifications).forEach((type) => {
            updatedNotifications[type] = updatedNotifications[type].map((notif) =>
              notif.id === id ? { ...notif, isRead: true } : notif
            );
          });
          return updatedNotifications;
        });
      } else {
        toast({
          variant: "destructive",
          title: t('markReadErrorTitle'),
          description: response.message || t('markReadErrorMessage'),
        });
      }
    } catch (error) {
      console.error("Error marking notification as read:", error);
      toast({
        variant: "destructive",
        title: t('markReadErrorTitle'),
        description: t('markReadGenericError'),
      });
    }
  };

  const handleMarkAllAsRead = async () => {
    const unreadIds = notifications[activeTab]
      ?.filter(n => !n.isRead)
      ?.map(n => n.id) || [];
      
    if (unreadIds.length === 0) return;

    try {
      const response = await markAsRead({ ids: unreadIds });

      if (response.success) {
        setNotifications((prev) => {
          const updatedNotifications = { ...prev };
          updatedNotifications[activeTab] = updatedNotifications[activeTab].map((notif) => ({
            ...notif,
            isRead: true,
          }));
          return updatedNotifications;
        });
        toast({
          title: t('markAllReadSuccessTitle'),
          description: t('markAllReadSuccessMessage'),
        });
      } else {
        toast({
          variant: "destructive",
          title: t('markAllReadErrorTitle'),
          description: response.message || t('markAllReadErrorMessage'),
        });
      }
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
      toast({
        variant: "destructive",
        title: t('markAllReadErrorTitle'),
        description: t('markAllReadGenericError'),
      });
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return "";
    try {
      const date = new Date(dateString);
      const formatString = `dd MMMM yyyy ${t('dateFormatTimeConnector')} HH:mm`;
      return format(date, formatString, { locale: dateLocale });
    } catch (error) {
        console.error("Error formatting date:", error);
      return dateString;
    }
  };

  const getNotificationIcon = (type) => {
    const notificationType = NOTIFICATION_TYPES.find((t) => t.id === type);
    return notificationType ? notificationType.icon : <Bell className="h-4 w-4" />;
  };

  return (
    <Tabs defaultValue={NOTIFICATION_TYPES[0]?.id || 'news'} value={activeTab} onValueChange={handleTabChange}>
      <TabsList className="grid grid-cols-2 sm:grid-cols-4 mb-8">
        {NOTIFICATION_TYPES.map((type) => (
          <TabsTrigger key={type.id} value={type.id} className="flex items-center gap-2">
            {type.icon}
            <span className="hidden sm:inline">{type.label}</span>
          </TabsTrigger>
        ))}
      </TabsList>

      {NOTIFICATION_TYPES.map((type) => (
        <TabsContent key={type.id} value={type.id} className="space-y-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">{type.label}</h2>
            {notifications[type.id]?.some((n) => !n.isRead) && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleMarkAllAsRead}
                className="flex items-center gap-2"
              >
                <CheckCheck className="h-4 w-4" />
                <span>{t('markAllReadButton')}</span>
              </Button>
            )}
          </div>

          {loading && (!notifications[type.id] || notifications[type.id]?.length === 0) ? (
            <div className="flex justify-center items-center h-40">
              <div className="animate-spin h-6 w-6 border-2 border-primary rounded-full border-t-transparent"></div>
            </div>
          ) : notifications[type.id]?.length > 0 ? (
            <>
              <div className="space-y-3">
                {notifications[type.id].map((notification) => (
                  <Card
                    key={notification.id}
                    className={`transition-colors ${notification.isRead ? 'bg-white hover:bg-gray-50' : 'bg-blue-50 hover:bg-blue-100'}`}
                  >
                    <CardContent className="p-4 flex gap-4 items-start">
                      <div className={`mt-1 p-2 rounded-full ${notification.isRead ? 'bg-gray-100 text-gray-500' : 'bg-blue-100 text-blue-600'}`}>
                           {getNotificationIcon(notification.type)}
                      </div>
                      <div className="flex-1">
                        <p className={`font-medium mb-1 ${notification.isRead ? 'text-gray-800' : 'text-gray-900'}`}>
                          {notification.title}
                        </p>
                        <p className={`text-sm mb-2 ${notification.isRead ? 'text-gray-600' : 'text-gray-700'}`}>
                           {notification.content}
                        </p>
                        <p className="text-xs text-gray-400">
                          {formatDate(notification.createdAt)}
                        </p>
                         {notification.link && (
                            <Link href={notification.link} className="text-xs text-blue-600 hover:underline mt-1 inline-block">
                                Xem chi tiết
                            </Link>
                         )}
                      </div>
                      {!notification.isRead && (
                        <button 
                            onClick={() => handleMarkAsRead(notification.id)}
                            className="p-1 rounded-full hover:bg-gray-200 text-gray-400 hover:text-gray-600 mt-1"
                            title={t("markReadSuccessMessage")}
                        >
                            <Check className="h-4 w-4" />
                        </button>
                       )}
                    </CardContent>
                  </Card>
                ))}
              </div>
              {pagination[type.id]?.totalPages > 1 && (
                  <div className="flex justify-center items-center space-x-2 mt-6">
                      <Button
                          variant="outline"
                          size="icon"
                          onClick={() => handlePageChange(type.id, pagination[type.id].page - 1)}
                          disabled={pagination[type.id].page <= 1 || loading}
                          aria-label={t('previousPageButton')}
                      >
                          <ChevronLeft className="h-4 w-4" />
                      </Button>
                      <span className="text-sm text-gray-600">
                          Trang {pagination[type.id].page} / {pagination[type.id].totalPages}
                      </span>
                      <Button
                          variant="outline"
                          size="icon"
                          onClick={() => handlePageChange(type.id, pagination[type.id].page + 1)}
                          disabled={pagination[type.id].page >= pagination[type.id].totalPages || loading}
                          aria-label={t('nextPageButton')}
                      >
                          <ChevronRight className="h-4 w-4" />
                      </Button>
                  </div>
              )}
            </>
          ) : (
            <div className="text-center py-10 text-gray-500">
              <Bell className="mx-auto h-12 w-12 text-gray-400 mb-3" />
              {t('noNotificationsMessage')}
            </div>
          )}
        </TabsContent>
      ))}
    </Tabs>
  );
}
