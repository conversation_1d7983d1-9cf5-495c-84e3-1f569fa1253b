﻿-- Add new columns to WalletTransactions table for transaction processing
ALTER TABLE "WalletTransactions"
    ADD COLUMN "Status" VARCHAR(50) NOT NULL DEFAULT 'PENDING' CHECK ("Status" IN ('PENDING', 'COMPLETED', 'FAILED', 'CANCELLED')),
    ADD COLUMN "PaymentMethod" VARCHAR(100) NOT NULL,
    ADD COLUMN "TransactionReference" VARCHAR(255),
    ADD COLUMN "ProcessedAt" TIMESTAMP,
    ADD COLUMN "FailureReason" TEXT;

-- Create index for faster queries on transaction status
CREATE INDEX "IDX_WalletTransactions_Status" ON "WalletTransactions"("Status");

-- Create index for faster queries on user's pending transactions
CREATE INDEX "IDX_WalletTransactions_UserStatus" ON "WalletTransactions"("UserId", "Status");

-- Create index for faster queries on transaction creation date
CREATE INDEX "IDX_WalletTransactions_CreatedAt" ON "WalletTransactions"("CreatedAt" DESC);

-- Comment on columns for better documentation
COMMENT ON COLUMN "WalletTransactions"."Status" IS 'Transaction status: pending, completed, failed, or cancelled';
COMMENT ON COLUMN "WalletTransactions"."PaymentMethod" IS 'Method used for payment (e.g., Credit Card, Bank Transfer)';
COMMENT ON COLUMN "WalletTransactions"."TransactionReference" IS 'External reference number for the transaction';
COMMENT ON COLUMN "WalletTransactions"."ProcessedAt" IS 'Timestamp when the transaction was processed';
COMMENT ON COLUMN "WalletTransactions"."FailureReason" IS 'Reason for transaction failure if status is failed';


-- Thêm Unique Index cho TransactionReference
CREATE UNIQUE INDEX IF NOT EXISTS "UQ_WalletTransactions_TransactionReference"
    ON public."WalletTransactions" USING btree
    ("TransactionReference" COLLATE pg_catalog."default")
    WHERE "TransactionReference" IS NOT NULL; -- Chỉ áp dụng cho các bản ghi có TransactionReference (vì cột này nullable)

-- Tạo Sequence cho mã tham chiếu giao dịch
CREATE SEQUENCE transaction_ref_sequence
    START 1
    INCREMENT 1
    NO MAXVALUE
    NO CYCLE; -- Đảm bảo số luôn tăng và không lặp lại

    -- Function để sinh mã Transaction Reference nội bộ
CREATE OR REPLACE FUNCTION generate_internal_transaction_ref()
RETURNS TRIGGER AS $$
DECLARE
    seq_val BIGINT;
    random_part TEXT;
    formatted_date TEXT;
BEGIN
    -- Lấy số tiếp theo từ sequence
    SELECT nextval('transaction_ref_sequence') INTO seq_val;

    -- Định dạng ngày hiện tại thành YYMMDD
    -- Sử dụng CURRENT_TIMESTAMP (có múi giờ) cho chính xác, PostgreSQL sẽ xử lý chuyển đổi nếu cột là without time zone
    formatted_date := to_char(CURRENT_TIMESTAMP, 'YYMMDD');

    -- Sinh một chuỗi ngẫu nhiên ngắn (ví dụ: 8 ký tự hệ thập lục phân - hex)
    -- gen_random_bytes(4) sinh ra 4 byte ngẫu nhiên, encode('hex') chuyển thành 8 ký tự hex
    random_part := encode(gen_random_bytes(4), 'hex');

    -- Kết hợp các phần tử thành mã tham chiếu cuối cùng
    -- Định dạng: YYMMDD-SequenceNumber-RandomHex
    NEW."TransactionReference" := formatted_date || '-' || seq_val::TEXT || '-' || random_part;

    -- Kiểm tra độ dài để đảm bảo vừa với cột VARCHAR(255)
    -- Độ dài sẽ là 6 (ngày) + 1 (-) + độ dài của seq_val + 1 (-) + 8 (ngẫu nhiên hex)
    -- Ví dụ: 250502-1234567890-A3F8C1D2 -> 6 + 1 + 10 + 1 + 8 = 26 ký tự. Hoàn toàn vừa với VARCHAR(255).

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;


-- Trigger để tự động sinh mã Transaction Reference nội bộ khi chèn bản ghi mới
CREATE OR REPLACE TRIGGER trg_generate_wallet_transaction_ref
BEFORE INSERT ON public."WalletTransactions"
FOR EACH ROW
EXECUTE FUNCTION generate_internal_transaction_ref();


ALTER TABLE "WalletTransactions"
    ADD COLUMN "ExternalPaymentReference" VARCHAR(255);


    -- Thêm Unique Index cho TransactionReference
CREATE UNIQUE INDEX IF NOT EXISTS "UQ_WalletTransactions_ExternalPaymentReference"
    ON public."WalletTransactions" USING btree
    ("ExternalPaymentReference" COLLATE pg_catalog."default")
    WHERE "ExternalPaymentReference" IS NOT NULL; -- Chỉ áp dụng cho các bản ghi có TransactionReference (vì cột này nullable)