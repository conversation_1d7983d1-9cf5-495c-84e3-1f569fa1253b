﻿namespace RealEstate.API.Controllers
{
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Extensions.Logging;
    using RealEstate.Application.DTO;
    using RealEstate.Application.Interfaces;
    using RealEstate.Domain.Common;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    [Route("api/notifications")]
    [ApiController]
    [Authorize] // Ensure only authenticated users can access notifications
    /// <summary>
    /// Controller for managing user notifications
    /// </summary>
    public class NotificationController : BaseController
    {
        private readonly INotificationService _notificationService;
        private readonly INotificationPreferenceService _notificationPreferenceService;
        private readonly ILogger<NotificationController> _logger;

        /// <summary>
        /// Initializes a new instance of the NotificationController
        /// </summary>
        /// <param name="notificationService">The notification service</param>
        /// <param name="notificationPreferenceService">The notification preference service</param>
        /// <param name="logger">The logger</param>
        public NotificationController(
            INotificationService notificationService,
            INotificationPreferenceService notificationPreferenceService,
            ILogger<NotificationController> logger)
        {
            _notificationService = notificationService;
            _notificationPreferenceService = notificationPreferenceService;
            _logger = logger;
        }

        /// <summary>
        /// Get all notifications for the current user with pagination and date filtering
        /// </summary>
        /// <param name="fromDate">Optional start date filter</param>
        /// <param name="toDate">Optional end date filter</param>
        /// <param name="page">Page number (default: 1)</param>
        /// <param name="pageSize">Page size (default: 10)</param>
        /// <returns>Paged list of notifications</returns>
        /// <response code="200">Returns the notifications</response>
        /// <response code="400">If the user ID is invalid</response>
        /// <response code="500">If there was an internal server error</response>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(PagedResultDto<NotificationDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<PagedResultDto<NotificationDto>>> GetAll(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                var userId = GetUserId();
                if (userId == null)
                {
                    return BadRequest("Invalid user");
                }

                // Get user notification preferences
                var preferences = await _notificationPreferenceService.GetPreferencesByUserIdAsync(userId.Value);

                var result = await _notificationService.GetNotificationsForUserAsync(
                    userId.Value,
                    fromDate,
                    toDate,
                    page,
                    pageSize);

                // Filter notifications based on user preferences
                // This assumes the service doesn't already do this filtering
                if (preferences != null)
                {
                    var filteredItems = result.Items.Where(n =>
                        (n.Type.Equals("promotion", StringComparison.OrdinalIgnoreCase) && preferences.ReceivePromotions) ||
                        (n.Type.Equals("wallet_update", StringComparison.OrdinalIgnoreCase) && preferences.ReceiveWalletUpdates) ||
                        (n.Type.Equals("news", StringComparison.OrdinalIgnoreCase) && preferences.ReceiveNews) ||
                        (n.Type.Equals("customer_message", StringComparison.OrdinalIgnoreCase) && preferences.ReceiveCustomerMessages) ||
                        // Always include system and transaction notifications
                        n.Type.Equals("system", StringComparison.OrdinalIgnoreCase) ||
                        n.Type.Equals("transaction", StringComparison.OrdinalIgnoreCase) ||
                        n.Type.Equals("contact", StringComparison.OrdinalIgnoreCase)
                    ).ToList();

                    // Create a new result with filtered items
                    result = new PagedResultDto<NotificationDto>
                    {
                        Items = filteredItems,
                        TotalCount = filteredItems.Count,
                        PageCount = (int)Math.Ceiling(filteredItems.Count / (double)pageSize),
                        CurrentPage = page,
                        PageSize = pageSize
                    };
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving notifications for user");
                return StatusCode(500, "An error occurred while retrieving notifications. Please try again later.");
            }
        }

        /// <summary>
        /// Get a specific notification by ID
        /// </summary>
        /// <param name="id">The notification ID</param>
        /// <returns>The notification details</returns>
        /// <response code="200">Returns the notification</response>
        /// <response code="404">If the notification is not found</response>
        /// <response code="500">If there was an internal server error</response>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(NotificationDto))]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<NotificationDto>> GetById(Guid id)
        {
            try
            {
                var notification = await _notificationService.GetNotificationByIdAsync(id);
                if (notification == null) return NotFound();
                return Ok(notification);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving notification with ID {NotificationId}", id);
                return StatusCode(500, "An error occurred while retrieving the notification. Please try again later.");
            }
        }

        /// <summary>
        /// Get notifications by type with pagination and date filtering
        /// </summary>
        /// <param name="type">The notification type</param>
        /// <param name="fromDate">Optional start date filter</param>
        /// <param name="toDate">Optional end date filter</param>
        /// <param name="page">Page number (default: 1)</param>
        /// <param name="pageSize">Page size (default: 10)</param>
        /// <returns>Paged list of notifications of the specified type</returns>
        /// <response code="200">Returns the notifications</response>
        /// <response code="400">If the notification type is invalid</response>
        /// <response code="500">If there was an internal server error</response>
        [HttpGet("by-type/{type}")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(PagedResultDto<NotificationDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<PagedResultDto<NotificationDto>>> GetByType(
            string type,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                if (!IsValidNotificationType(type))
                {
                    return BadRequest("Invalid notification type");
                }

                var result = await _notificationService.GetNotificationsByTypeAsync(
                    type,
                    fromDate,
                    toDate,
                    page,
                    pageSize);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving notifications of type {NotificationType}", type);
                return StatusCode(500, "An error occurred while retrieving notifications. Please try again later.");
            }
        }

        /// <summary>
        /// Get notifications by type for a specific user with pagination and date filtering (Admin only)
        /// </summary>
        /// <param name="type">The notification type</param>
        /// <param name="userId">The user ID</param>
        /// <param name="fromDate">Optional start date filter</param>
        /// <param name="toDate">Optional end date filter</param>
        /// <param name="page">Page number (default: 1)</param>
        /// <param name="pageSize">Page size (default: 10)</param>
        /// <returns>Paged list of notifications of the specified type for the specified user</returns>
        /// <response code="200">Returns the notifications</response>
        /// <response code="400">If the notification type is invalid</response>
        /// <response code="403">If the user is not an admin</response>
        /// <response code="500">If there was an internal server error</response>
        [HttpGet("by-type/{type}/user/{userId}")]
        [Authorize(Policy = "AdminOnly")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(PagedResultDto<NotificationDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<PagedResultDto<NotificationDto>>> GetByTypeAndUser(
            string type,
            Guid userId,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                if (!IsValidNotificationType(type))
                {
                    return BadRequest("Invalid notification type");
                }

                var result = await _notificationService.GetNotificationsByTypeAndUserAsync(
                    type,
                    userId,
                    fromDate,
                    toDate,
                    page,
                    pageSize);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving notifications of type {NotificationType} for user {UserId}", type, userId);
                return StatusCode(500, "An error occurred while retrieving notifications. Please try again later.");
            }
        }

        /// <summary>
        /// Get user notifications (including system and promotion notifications) with pagination and date filtering
        /// </summary>
        /// <param name="fromDate">Optional start date filter</param>
        /// <param name="toDate">Optional end date filter</param>
        /// <param name="page">Page number (default: 1)</param>
        /// <param name="pageSize">Page size (default: 10)</param>
        /// <returns>Paged list of notifications for the current user</returns>
        /// <response code="200">Returns the notifications</response>
        /// <response code="400">If the user ID is invalid</response>
        /// <response code="500">If there was an internal server error</response>
        [HttpGet("for-user")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(PagedResultDto<NotificationDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<PagedResultDto<NotificationDto>>> GetUserNotifications(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                var userId = GetUserId();
                if (userId == null)
                {
                    return BadRequest("Invalid user");
                }

                // Get user notification preferences
                var preferences = await _notificationPreferenceService.GetPreferencesByUserIdAsync(userId.Value);

                var result = await _notificationService.GetAllNotificationsForUserAsync(
                    userId.Value,
                    fromDate,
                    toDate,
                    page,
                    pageSize);

                // Filter notifications based on user preferences
                if (preferences != null)
                {
                    var filteredItems = result.Items.Where(n =>
                        (n.Type.Equals("promotion", StringComparison.OrdinalIgnoreCase) && preferences.ReceivePromotions) ||
                        (n.Type.Equals("wallet_update", StringComparison.OrdinalIgnoreCase) && preferences.ReceiveWalletUpdates) ||
                        (n.Type.Equals("news", StringComparison.OrdinalIgnoreCase) && preferences.ReceiveNews) ||
                        (n.Type.Equals("customer_message", StringComparison.OrdinalIgnoreCase) && preferences.ReceiveCustomerMessages) ||
                        // Always include system and transaction notifications
                        n.Type.Equals("system", StringComparison.OrdinalIgnoreCase) ||
                        n.Type.Equals("transaction", StringComparison.OrdinalIgnoreCase) ||
                        n.Type.Equals("contact", StringComparison.OrdinalIgnoreCase)
                    ).ToList();

                    // Create a new result with filtered items
                    result = new PagedResultDto<NotificationDto>
                    {
                        Items = filteredItems,
                        TotalCount = filteredItems.Count,
                        PageCount = (int)Math.Ceiling(filteredItems.Count / (double)pageSize),
                        CurrentPage = page,
                        PageSize = pageSize
                    };
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving notifications for user");
                return StatusCode(500, "An error occurred while retrieving notifications. Please try again later.");
            }
        }

        /// <summary>
        /// Mark a notification as read
        /// </summary>
        /// <param name="id">The notification ID</param>
        /// <returns>No content if successful</returns>
        /// <response code="204">If the notification was marked as read</response>
        /// <response code="400">If the user ID is invalid</response>
        /// <response code="404">If the notification is not found</response>
        /// <response code="500">If there was an internal server error</response>
        [HttpPut("{id}/mark-as-read")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> MarkAsRead(Guid id)
        {
            try
            {
                var userId = GetUserId();
                if (userId == null)
                {
                    return BadRequest("Invalid user");
                }

                var success = await _notificationService.MarkAsReadAsync(id, userId.Value);
                if (!success) return NotFound();
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking notification {NotificationId} as read", id);
                return StatusCode(500, "An error occurred while updating the notification. Please try again later.");
            }
        }

        /// <summary>
        /// Mark all notifications as read for the current user
        /// </summary>
        /// <returns>No content if successful</returns>
        /// <response code="204">If the notifications were marked as read</response>
        /// <response code="400">If the user ID is invalid</response>
        /// <response code="500">If there was an internal server error</response>
        [HttpPut("mark-all-as-read")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> MarkAllAsRead()
        {
            try
            {
                var userId = GetUserId();
                if (userId == null)
                {
                    return BadRequest("Invalid user");
                }

                await _notificationService.MarkAllAsReadAsync(userId.Value);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking all notifications as read for user");
                return StatusCode(500, "An error occurred while updating notifications. Please try again later.");
            }
        }

        /// <summary>
        /// Delete a notification
        /// </summary>
        /// <param name="id">The notification ID</param>
        /// <returns>No content if successful</returns>
        /// <response code="204">If the notification was deleted</response>
        /// <response code="400">If the user ID is invalid</response>
        /// <response code="404">If the notification is not found</response>
        /// <response code="500">If there was an internal server error</response>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Delete(Guid id)
        {
            try
            {
                var userId = GetUserId();
                if (userId == null)
                {
                    return BadRequest("Invalid user");
                }

                var success = await _notificationService.DeleteNotificationAsync(id, userId.Value);
                if (!success) return NotFound();
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting notification {NotificationId}", id);
                return StatusCode(500, "An error occurred while deleting the notification. Please try again later.");
            }
        }

        /// <summary>
        /// Get count of unread notifications for the current user
        /// </summary>
        /// <returns>The count of unread notifications</returns>
        /// <response code="200">Returns the unread notification count</response>
        /// <response code="400">If the user ID is invalid</response>
        /// <response code="500">If there was an internal server error</response>
        [HttpGet("unread-count")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(UnreadNotificationCountDto))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<UnreadNotificationCountDto>> GetUnreadCount()
        {
            try
            {
                var userId = GetUserId();
                if (userId == null)
                {
                    return BadRequest("Invalid user");
                }

                var count = await _notificationService.GetUnreadNotificationCountAsync(userId.Value);
                return Ok(new UnreadNotificationCountDto { Count = count });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving unread notification count for user");
                return StatusCode(500, "An error occurred while retrieving the notification count. Please try again later.");
            }
        }

        /// <summary>
        /// Mark multiple notifications as read
        /// </summary>
        /// <param name="notificationIds">List of notification IDs to mark as read</param>
        /// <returns>No content if successful</returns>
        /// <response code="204">If the notifications were marked as read</response>
        /// <response code="400">If the user ID is invalid or the request is invalid</response>
        /// <response code="500">If there was an internal server error</response>
        [HttpPut("mark-multiple-as-read")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> MarkMultipleAsRead([FromBody] List<Guid> notificationIds)
        {
            try
            {
                if (notificationIds == null || !notificationIds.Any())
                {
                    return BadRequest("No notification IDs provided");
                }

                var userId = GetUserId();
                if (userId == null)
                {
                    return BadRequest("Invalid user");
                }

                // Process each notification ID
                foreach (var id in notificationIds)
                {
                    await _notificationService.MarkAsReadAsync(id, userId.Value);
                    // Note: We're not checking success here as we want to try to mark all as read
                    // even if some fail. In a more robust implementation, we might track failures.
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking multiple notifications as read");
                return StatusCode(500, "An error occurred while updating notifications. Please try again later.");
            }
        }

        /// <summary>
        /// Helper method to validate notification type
        /// </summary>
        /// <param name="type">The notification type to validate</param>
        /// <returns>True if the type is valid, false otherwise</returns>
        private bool IsValidNotificationType(string type)
        {
            // Check if the type can be parsed to the NotificationType enum
            return Enum.TryParse(typeof(EnumValues.NotificationType), type, true, out _);
        }
    }
}
