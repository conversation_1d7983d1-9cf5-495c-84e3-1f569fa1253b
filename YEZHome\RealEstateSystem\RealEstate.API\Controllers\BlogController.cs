﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Application.Services;
using RealEstate.Domain.CustomModel;
using RealEstate.Domain.Entities;

namespace RealEstate.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class BlogController : BaseController
    {
        private readonly IBlogService _blogPostService;

        public BlogController(IBlogService blogPostService)
        {
            _blogPostService = blogPostService;
        }

        [AllowAnonymous]
        [HttpGet("{id}")]
        public async Task<ActionResult<BlogPostDto>> GetBlogPostById(Guid id)
        {
            var blogPost = await _blogPostService.GetBlogByIdAsync(id);
            if (blogPost == null)
            {
                return NotFound();
            }
            return Ok(blogPost);
        }

        [AllowAnonymous]
        [HttpGet("slug/{slug}")]
        public async Task<ActionResult<BlogPostDto>> GetBlogPostBySlug(string slug)
        {
            var blogPost = await _blogPostService.GetBlogBySlugAsync(slug);
            if (blogPost == null)
            {
                return NotFound();
            }
            return Ok(blogPost);
        }

        [HttpGet]
        [AllowAnonymous]
        public async Task<ActionResult<IEnumerable<BlogPostDto>>> GetAllBlogPost()
        {
            var result = await _blogPostService.GetAllBlogAsync();
            return Ok(result);
        }

        [HttpGet("blog-posts")]
        [AllowAnonymous]
        public async Task<IActionResult> GetBlogPosts([FromQuery] PagingRequest request, [FromQuery] string? title)
        {
            var result = await _blogPostService.GetBlogAsync(request, title ?? string.Empty);
            return Ok(result);
        }

        [HttpPost]
        [Authorize(Policy = "UserExists,ContentEditor")]
        [Authorize(Policy = "UserExists,SystemAdmin")]
        [Authorize(Policy = "UserExists,SuperMod")]
        public async Task<ActionResult<BlogPostDto>> CreateBlog(CreateBlogPostDto blogDto)
        {
            var userId = GetUserId();
            if (userId == null)
            {
                return BadRequest("User không hợp lệ");
            }

            var createdBlog = await _blogPostService.CreateBlogAsync(blogDto, userId.Value);
            return CreatedAtAction(nameof(GetBlogPostById), new { id = createdBlog.Id }, createdBlog);
        }

        [HttpPut("{id}")]
        [Authorize(Policy = "UserExists,ContentEditor")]
        [Authorize(Policy = "UserExists,SystemAdmin")]
        [Authorize(Policy = "UserExists,SuperMod")]
        public async Task<IActionResult> UpdateBlog(Guid id, CreateBlogPostDto blogDto)
        {
            try
            {
                var userId = GetUserId();
                if (userId == null)
                {
                    return BadRequest("User không hợp lệ");
                }

                await _blogPostService.UpdateBlogAsync(id, blogDto, userId.Value);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            return NoContent();
        }

        [HttpDelete("{id}")]
        [Authorize(Policy = "UserExists,ContentEditor")]
        [Authorize(Policy = "UserExists,SystemAdmin")]
        [Authorize(Policy = "UserExists,SuperMod")]
        public async Task<IActionResult> DeleteBlog(Guid id)
        {
            try
            {
                var userId = GetUserId();
                if (userId == null)
                {
                    return BadRequest("User không hợp lệ");
                }

                await _blogPostService.DeleteBlogAsync(id, userId.Value);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            return NoContent();
        }
    }
}
