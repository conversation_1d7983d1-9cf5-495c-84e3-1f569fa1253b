"use client";

import { Badge } from "@/components/ui/badge";

export default function PropertySpecifications({ property }) {
  // Map API fields to component fields
  const propertyData = {
    type: property.propertyType || "-",
    yearBuilt: property.yearBuilt || "-", // Not in API
    area: property.area ? `${property.area} m²` : "-",
    livingArea: property.livingArea ? `${property.livingArea} m²` : "-", // Not in API
    bedrooms: property.rooms || "-", // rooms in API is bedrooms in component
    bathrooms: property.toilets || "-", // toilets in API is bathrooms in component
    floors: property.floors || "-",
    parking: property.parking || "-", // Not in API
    // Default empty array if amenities doesn't exist
    amenities: property.amenities || []
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-semibold mb-4">Specifications</h2>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <p className="text-gray-600">Property Type</p>
            <p className="font-medium">{propertyData.type}</p>
          </div>
          <div className="space-y-2">
            <p className="text-gray-600">Year Built</p>
            <p className="font-medium">{propertyData.yearBuilt}</p>
          </div>
          <div className="space-y-2">
            <p className="text-gray-600">Total Area</p>
            <p className="font-medium">{propertyData.area}</p>
          </div>
          <div className="space-y-2">
            <p className="text-gray-600">Living Area</p>
            <p className="font-medium">{propertyData.livingArea}</p>
          </div>
          <div className="space-y-2">
            <p className="text-gray-600">Bedrooms</p>
            <p className="font-medium">{propertyData.bedrooms}</p>
          </div>
          <div className="space-y-2">
            <p className="text-gray-600">Bathrooms</p>
            <p className="font-medium">{propertyData.bathrooms}</p>
          </div>
          <div className="space-y-2">
            <p className="text-gray-600">Floors</p>
            <p className="font-medium">{propertyData.floors}</p>
          </div>
          <div className="space-y-2">
            <p className="text-gray-600">Parking</p>
            <p className="font-medium">{propertyData.parking}</p>
          </div>
        </div>
      </div>

      {propertyData.amenities.length > 0 && (
        <div>
          <h2 className="text-2xl font-semibold mb-4">Amenities</h2>
          <div className="flex flex-wrap gap-2">
            {propertyData.amenities.map((amenity, index) => (
              <Badge key={index} variant="outline">
                {amenity}
              </Badge>
            ))}
          </div>
        </div>
      )}
    </div>
  );
} 